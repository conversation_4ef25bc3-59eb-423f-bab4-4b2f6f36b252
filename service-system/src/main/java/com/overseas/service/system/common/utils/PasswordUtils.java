package com.overseas.service.system.common.utils;

import com.overseas.common.exception.CustomException;
import com.overseas.service.system.entity.User;
import org.apache.shiro.crypto.SecureRandomNumberGenerator;
import org.apache.shiro.crypto.hash.SimpleHash;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class PasswordUtils {
    // 加密算法名称
    private static String algorithmName;

    // md5加密次数
    private static int encryptTimes;

    @Value("${shiro.common.algorithm-name}")
    public void setAlgorithmName(String algorithmName) {
        PasswordUtils.algorithmName = algorithmName;
    }

    @Value("${shiro.common.encrypt-times}")
    public void setEncryptTimes(int encryptTimes) {
        PasswordUtils.encryptTimes = encryptTimes;
    }

    /**
     * 生成密码
     *
     * @param password 被加密的密码
     * @param salt     加密盐
     * @return 加密字符串
     */
    private static String password(String password, String salt) {
        return new SimpleHash(PasswordUtils.algorithmName, password, salt, PasswordUtils.encryptTimes)
                .toString();
    }

    /**
     * 生成加密盐
     *
     * @return 盐字符串
     */
    private static String getSalt() {
        return new SecureRandomNumberGenerator().nextBytes().toString();
    }

    /**
     * 给用户设置密码和盐信息
     *
     * @param user 用户对象
     */
    public static void setUserPassword(User user) {
        String salt = getSalt();
        user.setSalt(salt);
        user.setPassword(password(user.getPassword(), salt));
    }


    public static void checkUserPassword(User user, String oldPassword) {
        if (!password(oldPassword, user.getSalt()).equals(user.getPassword())) {
            throw new CustomException("原密码不正确，请确认后再试");
        }
    }
}
