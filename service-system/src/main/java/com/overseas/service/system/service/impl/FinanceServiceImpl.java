package com.overseas.service.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.report.UserCostDTO;
import com.overseas.common.dto.sys.finance.*;
import com.overseas.common.utils.*;
import com.overseas.common.utils.redis.RedisUtils;
import com.overseas.service.system.entity.User;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.sys.finance.UserStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.vo.common.GetVO;
import com.overseas.common.vo.sys.finance.FinanceListVO;
import com.overseas.common.vo.sys.finance.FinanceRechargeGetVO;
import com.overseas.common.vo.sys.finance.FinanceRechargeListVO;
import com.overseas.service.system.entity.Finance;
import com.overseas.service.system.entity.FinanceRecharge;
import com.overseas.service.system.enums.user.UserTypeEnum;
import com.overseas.common.vo.report.UserCostListVO;
import com.overseas.service.system.feign.FgReportService;
import com.overseas.service.system.mapper.FinanceMapper;
import com.overseas.service.system.mapper.FinanceRechargeMapper;
import com.overseas.service.system.mapper.UserMapper;
import com.overseas.service.system.service.FinanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Array;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-18 20:44
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FinanceServiceImpl extends ServiceImpl<FinanceMapper, Finance> implements FinanceService {

    private final FgReportService fgReportService;

    private final UserMapper userMapper;

    private final FinanceRechargeMapper financeRechargeMapper;

    private final RedisUtils redisUtils;

    private final Double costRate = 0.95D;

    private final BigDecimal balance = new BigDecimal(5000);

    private final SheinConfiguration sheinConfiguration;

    private final Map<Integer, Integer> userTypeMap = new HashMap<>() {{
        put(1, 3); // 如果当前登录用户为代理商，则查询投放账户
        put(4, 1); // 如果当前登录用户为财务管理员，则查询代理商
    }};

    @Override
    public List<UserFinanceListDTO> listUserFinance(List<Integer> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        return baseMapper.listUserFinance(new QueryWrapper<Finance>().lambda().in(Finance::getUserId, userIds));
    }

    @Override
    public UserFinanceGetDTO getUserFinance(GetVO getVO, User sysUser) {

        User user = this.userMapper.selectById(getVO.getId());
        UserFinanceGetDTO userFinanceGetDTO = new UserFinanceGetDTO();
        userFinanceGetDTO.setUserId(getVO.getId());
        // 如果是代理商账号，则查询代理商账号信息
        if (ICommonEnum.get(user.getUserType(), UserTypeEnum.class).equals(UserTypeEnum.AGENT)) {
            FinanceAgentMarketDTO financeAgentMarketDTO = this.baseMapper.getMarketFinance(new QueryWrapper<Finance>()
                    .eq("uf.user_id", user.getId()));
            if (financeAgentMarketDTO == null) {
                return userFinanceGetDTO;
            }
            BeanUtils.copyProperties(financeAgentMarketDTO, userFinanceGetDTO, "userId");
        } else {
            Finance finance = this.baseMapper.selectOne(new QueryWrapper<Finance>().lambda().eq(Finance::getUserId, getVO.getId()));
            if (finance == null) {
                return userFinanceGetDTO;
            }
            if (sheinConfiguration.isRole(sysUser.getRoleId())) {
                userFinanceGetDTO.setAmount(finance.getAmount().subtract(finance.getShowConsume()));
                userFinanceGetDTO.setDayCost(finance.getShowDayCost());
            } else {
                userFinanceGetDTO.setAmount(finance.getAmount().subtract(finance.getConsume()));
                userFinanceGetDTO.setDayCost(finance.getDayCost());
            }
        }
        return userFinanceGetDTO;
    }

    @Override
    public PageUtils<FinanceListDTO> getFinanceList(FinanceListVO listVO, User user) {

        Map<String, String> sortFieldMap = new HashMap<>() {{
            put("", "uu.id"); // 默认用户ID排序
            put("amount", "uf.amount"); // 总充值排序
            put("allocatedAmount", "allocated_amount"); // 已分配金额排序
            put("distributableAmount", "distributable_amount"); // 可分配余额排序
            put("consume", "uf.consume"); // 总消耗排序
            put("balance", "balance"); // 余额排序
        }};

        QueryWrapper<Finance> queryWrapper = new QueryWrapper<Finance>()
                .eq(ObjectUtils.isNotNullOrZero(listVO.getStatus()), "uu.user_status", listVO.getStatus())
                .eq("uu.user_type", this.userTypeMap.get(user.getUserType()))
                // 如果是代理商用户进入的投放管理账户财务列表，则过滤登录的代理商用户
                .eq(ICommonEnum.get(user.getUserType(), UserTypeEnum.class).equals(UserTypeEnum.AGENT), "uur.user_id", user.getId())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q
                        .like("uu.id", listVO.getSearch())
                        .or().like("uu.company_name", listVO.getSearch()))
                .groupBy("uu.id")
                .orderBy(true, listVO.getSortType().equals("asc"), sortFieldMap.get(listVO.getSortField()));

        IPage<FinanceListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        IPage<FinanceListDTO> pageData = ICommonEnum.get(user.getUserType(), UserTypeEnum.class).equals(UserTypeEnum.FINANCE_MANAGER) ?
                this.baseMapper.getAgentFinanceList(page, queryWrapper) : this.baseMapper.getMarketFinanceList(page, queryWrapper);

        pageData.getRecords().forEach(entity -> entity.setStatusName(ICommonEnum.getNameById(entity.getStatus(), UserStatusEnum.class)));

        return new PageUtils<>(pageData);
    }

    @Override
    public PageUtils<FinanceRechargeListDTO> getFinanceRechargeList(FinanceRechargeListVO listVO, User user) {

        IPage<FinanceRechargeListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        IPage<FinanceRechargeListDTO> pageData = this.financeRechargeMapper.getFinanceRechargeList(page, new QueryWrapper<FinanceRecharge>()
                .eq("uu.user_type", this.userTypeMap.get(user.getUserType()))
                .eq(ObjectUtils.isNotNullOrZero(listVO.getId()), "uu.id", listVO.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getOperatorId()), "ufr.create_uid", listVO.getOperatorId())
                .eq(user.getUserType().equals(UserTypeEnum.AGENT.getId()), "ufr.create_uid", user.getId())
                .between("ufr.create_time", listVO.getStartDate() + " 00:00:00", listVO.getEndDate() + " 23:59:59")
                .ge(StringUtils.isNotBlank(listVO.getStartAmount()), "ufr.recharge_amount", listVO.getStartAmount())
                .le(StringUtils.isNotBlank(listVO.getEndAmount()), "ufr.recharge_amount", listVO.getEndAmount())
                .orderByDesc("ufr.create_time"));

        return new PageUtils<>(pageData);
    }

    @Override
    public void rechargeFinance(FinanceRechargeGetVO getVO, User user) {

        if (CollectionUtils.isEmpty(getVO.getIds())) {
            throw new CustomException("ID不能为空");
        }
        List<Finance> financeList = this.baseMapper.selectList(new QueryWrapper<Finance>().lambda()
                .in(Finance::getUserId, getVO.getIds()));

        // 1.如果是代理商账户给投放账户充值，则校验当前用户可用金额是否支持该次充值
        if (ICommonEnum.get(user.getUserType(), UserTypeEnum.class).equals(UserTypeEnum.AGENT)) {

            // 获取指定代理商账户及其投放账户财务信息
            FinanceAgentMarketDTO financeAgentMarketDTO = this.baseMapper.getMarketFinance(new QueryWrapper<Finance>()
                    .eq("uf.user_id", user.getId()));
            if (financeAgentMarketDTO == null ||
                    (financeAgentMarketDTO.getDistributableAmount().compareTo(getVO.getAmount().multiply(BigDecimal.valueOf(getVO.getIds().size()))) < 0)) {
                throw new CustomException("账户余额不足，请确认后再试");
            }
        }

        // 2.处理充值数据
        List<Long> existIds = financeList.stream().map(u -> u.getUserId().longValue()).collect(Collectors.toList());

        // 已存在财务信息的数据加上充值金额
        financeList.forEach(finance -> finance.setAmount(finance.getAmount().add(getVO.getAmount())));
        // 未存在财务信息的账户新建财务记录信息
        List<Finance> newFinanceList = getVO.getIds().stream().filter(id -> !existIds.contains(id)).map(id -> {
            Finance finance = new Finance();
            finance.setUserId(id.intValue());
            finance.setAmount(getVO.getAmount());
            finance.setConsume(new BigDecimal("0.00"));
            finance.setCreateUid(user.getId());
            return finance;
        }).collect(Collectors.toList());
        financeList.addAll(newFinanceList);

        // 3.充值
        this.baseMapper.batchRechargeFinance(financeList, user.getId());

        // 4.添加充值记录
        List<FinanceRecharge> financeRechargeList = financeList.stream().map(finance -> {
            FinanceRecharge financeRecharge = new FinanceRecharge();
            financeRecharge.setUserId(finance.getUserId().longValue());
            financeRecharge.setRechargeAmount(getVO.getAmount());
            financeRecharge.setAmount(finance.getAmount());
            financeRecharge.setCreateUid(user.getId());
            return financeRecharge;
        }).collect(Collectors.toList());
        this.financeRechargeMapper.batchSaveFinanceRechargeRecord(financeRechargeList);
    }

    @Override
    public void setUserCost() {
        UserCostListVO listVO = new UserCostListVO();
        Date today = DateUtils.getTodayDate();
        Date startDate = DateUtils.string2Date("2022-01-01");
        listVO.setStartDate(startDate);
        listVO.setEndDate(today);
        // TODO 1.先取账号，分时区
        // 2.取账号花费

        // 获取整体的花费
        listVO.setUserType(UserTypeEnum.AGENT.getId());
        FeignR<Map<Long, UserCostDTO>> agentAllCosts = this.fgReportService.listUserCost(listVO);
        listVO.setUserType(UserTypeEnum.MASTER.getId());
        FeignR<Map<Long, UserCostDTO>> masterAllCosts = this.fgReportService.listUserCost(listVO);

        // 获取今日花费
        listVO.setStartDate(today);
        listVO.setUserType(UserTypeEnum.AGENT.getId());
        Map<Long, UserCostDTO> agentTodayCosts = this.fgReportService.listUserCost(listVO).getData();
        listVO.setUserType(UserTypeEnum.MASTER.getId());
        Map<Long, UserCostDTO> masterTodayCosts = this.fgReportService.listUserCost(listVO).getData();

        List<Finance> finances = new ArrayList<>();
        finances.addAll(this.listUserFinance(agentAllCosts.getData(), agentTodayCosts));
        finances.addAll(this.listUserFinance(masterAllCosts.getData(), masterTodayCosts));
        if (!finances.isEmpty()) {
            this.baseMapper.batchInsert(finances);
        }
    }

    @Override
    public void checkMasterAllBudget() {

        // 查询账户总消耗/总充值的比例，当超过80%邮件提醒
        log.info("Start searching account total budget");
        List<FinanceMasterBudgetDTO> finances = this.baseMapper.checkMasterAllBudget(new QueryWrapper<Finance>()
                // 只查总充值大于0的数据
                .ne("uf.amount", 0)
                .eq("uu.user_type", UserTypeEnum.MANAGER.getId()));
//        List<FinanceMasterBudgetDTO> overFinances = finances.stream().filter(u ->
//                u.getConsume().divide(u.getAmount(), 2, RoundingMode.HALF_UP).doubleValue() >= this.costRate)
//                .collect(Collectors.toList());
//        List<FinanceMasterBudgetDTO> unOverFinances = finances.stream().filter(u ->
//                u.getConsume().divide(u.getAmount(), 2, RoundingMode.HALF_UP).doubleValue() < this.costRate)
//                .collect(Collectors.toList());
        List<FinanceMasterBudgetDTO> overFinances = new ArrayList<>();
        List<FinanceMasterBudgetDTO> unOverFinances = new ArrayList<>();
        finances.forEach(finance -> {
            // 总充值高于5w的按余额5k提醒，低于5W的按5%提醒
            if (finance.getAmount().compareTo(new BigDecimal(50000)) <= 0) {
                // 低于5W，按余额
                double costRate = finance.getConsume().divide(finance.getAmount(), 2, RoundingMode.HALF_UP)
                        .doubleValue();
                // 花费比例大于阈值，则告警
                if (costRate >= this.costRate) {
                    overFinances.add(finance);
                } else {
                    unOverFinances.add(finance);
                }
            } else {
                // 余额小于等于5000，则告警
                if (finance.getAmount().subtract(finance.getConsume()).compareTo(this.balance) <= 0) {
                    overFinances.add(finance);
                } else {
                    unOverFinances.add(finance);
                }
            }
        });
        String prefix = "market_all_budget_";
        // 清除未达线的已存redis
        this.removeRedisCache(unOverFinances, prefix);
        // 发送邮件
        this.sendEmail(overFinances, prefix, "账户总消费达线提醒", "以上投放账户当前总消耗已达到总充值金额的"
                + (100 * this.costRate) + "%，请及时充值！</td></tr>");
        log.info("End searching account total budget");
    }

    @Override
    public void checkMasterDayBudget() {

        // 查询账户日消耗/日预算的比例，当超过80%邮件提醒
        log.info("Start searching account day budget");
        List<FinanceMasterBudgetDTO> finances = this.baseMapper.checkMasterDayBudget(new QueryWrapper<Finance>()
                // 只查日预算大于0的数据
                .ne("mm.budget_day", 0)
                .eq("uu.user_type", UserTypeEnum.MANAGER.getId()));
        List<FinanceMasterBudgetDTO> overFinances = finances.stream().filter(u ->
                u.getDayCost().divide(u.getBudgetDay(), 2, RoundingMode.HALF_UP).doubleValue() >= this.costRate).collect(Collectors.toList());
        List<FinanceMasterBudgetDTO> unOverFinances = finances.stream().filter(u ->
                u.getDayCost().divide(u.getBudgetDay(), 2, RoundingMode.HALF_UP).doubleValue() < this.costRate).collect(Collectors.toList());
        String prefix = "market_day_budget_";
        // 清除未达线的已存redis
        this.removeRedisCache(unOverFinances, prefix);
        // 发送邮件
        this.sendEmail(overFinances, prefix, "账户日消费达线提醒", "今日消耗已达日预算的"
                + (100 * this.costRate) + "%，请注意！</td></tr>");
        log.info("End searching account day budget");
    }

    private void removeRedisCache(List<FinanceMasterBudgetDTO> finances, String prefix) {
        finances.forEach(finance -> {
            String key = prefix + finance.getManagerId() + "_" + finance.getId();
            if (this.redisUtils.get(key) != null) {
                this.redisUtils.del(key);
            }
        });
    }

    private void sendEmail(List<FinanceMasterBudgetDTO> finances, String prefix, String subject, String noticeContent) {

        if (finances.isEmpty()) {
            return;
        }
        // 以 ${prefix}_{managerId}_${masterId}的方式存入redis
        // 过滤已经存入redis的数据
        finances.removeIf(finance -> this.redisUtils.get(prefix + finance.getManagerId() + "_" + finance.getId()) != null);
        if (finances.isEmpty()) {
            return;
        }
        log.info("记录预算预警: {}", finances.stream().map(finance -> prefix + finance.getManagerId() + "_" + finance.getId()).collect(Collectors.joining(",")));
        // 写入redis
        finances.forEach(finance -> {
            String key = prefix + finance.getManagerId() + "_" + finance.getId();
            Integer expire = (int) (DateUtils.string2Long(DateUtils.format(DateUtils.format(new Date(), 1))) - DateUtils.date2Long(new Date()));
            this.redisUtils.set(key, expire.toString(), expire);
            log.info("record in redis：{}", key);
        });
        Map<Long, List<FinanceMasterBudgetDTO>> financeMap = finances.stream().collect(Collectors.groupingBy(FinanceMasterBudgetDTO::getManagerId));
        for (Map.Entry<Long, List<FinanceMasterBudgetDTO>> entry : financeMap.entrySet()) {
            StringBuilder content = new StringBuilder("<table border=\"1\" cellspacing=\"0\">" +
                    "<tr><th style=\"minWidth: 100px;\">账号名称</th><th style=\"minWidth: 100px;\">账号ID</th>" +
                    (prefix.equals("market_all_budget_") ? "<th style=\"minWidth: 100px;\">总充值</th><th style=\"minWidth: 100px;\">总消耗</th>" :
                            "<th style=\"minWidth: 100px;\">日预算</th><th style=\"minWidth: 100px;\">日消耗</th>") + "</tr>");
            for (FinanceMasterBudgetDTO financeMasterBudgetDTO : entry.getValue()) {
                content.append("<tr><td>").append(financeMasterBudgetDTO.getName()).append("</td><td>").append(financeMasterBudgetDTO.getId()).append("</td><td>");
                if (prefix.equals("market_all_budget_")) {
                    content.append(financeMasterBudgetDTO.getAmount()).append("</td><td>")
                            .append(financeMasterBudgetDTO.getConsume());
                } else {
                    content.append(financeMasterBudgetDTO.getBudgetDay()).append("</td><td>")
                            .append(financeMasterBudgetDTO.getDayCost());
                }
                content.append("</td></tr>");
            }
            content.append("</table>\n").append(noticeContent);
            MailUtils.sendHtmlEmail(List.of(entry.getValue().get(0).getEmail()), subject, content.toString(), "");
        }
    }

    private List<Finance> listUserFinance(Map<Long, UserCostDTO> userAllCosts, Map<Long, UserCostDTO> userTodayCosts) {
        List<Finance> finances = new ArrayList<>();
        if (userAllCosts != null) {
            userAllCosts.forEach((userId, cost) -> {
                Finance finance = new Finance();
                finance.setUserId(userId.intValue());
                finance.setConsume(cost.getCost());
                finance.setShowConsume(cost.getShowCost());
                UserCostDTO userTodayCost = userTodayCosts.getOrDefault(userId, null);
                if (null == userTodayCost) {
                    finance.setDayCost(BigDecimal.ZERO);
                    finance.setShowDayCost(BigDecimal.ZERO);
                } else {
                    finance.setDayCost(userTodayCost.getCost());
                    finance.setShowDayCost(userTodayCost.getShowCost());
                }
                finances.add(finance);
            });
        }
        return finances;
    }
}
