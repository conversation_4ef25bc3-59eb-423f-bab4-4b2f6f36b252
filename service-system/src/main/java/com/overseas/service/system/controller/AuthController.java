package com.overseas.service.system.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.sys.MenuDTO;
import com.overseas.common.dto.sys.UserLoginDTO;
import com.overseas.common.exception.CustomException;
import com.overseas.common.vo.sys.PermissionGetVO;
import com.overseas.common.vo.sys.UserLoginVO;
import com.overseas.common.vo.sys.user.UserMasterGetVO;
import com.overseas.service.system.common.utils.PasswordUtils;
import com.overseas.service.system.common.utils.RealmUtils;
import com.overseas.service.system.entity.User;
import com.overseas.service.system.service.PermissionService;
import com.overseas.service.system.service.UserService;
import com.overseas.service.system.enums.user.UserStatusEnum;
import com.overseas.service.system.vo.ResetPasswordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-13 9:18
 */
@RestController
@RequestMapping("/sys/auth")
@RequiredArgsConstructor
@Api(value = "用户权限")
public class AuthController extends AbstractController {

    private final UserService userService;

    private final PermissionService permissionService;


    @ApiOperation(value = "登录", notes = "登录", produces = "application/json", response = UserLoginDTO.class)
    @PostMapping("/login")
    public R login(@RequestBody UserLoginVO authLoginVO) {
        Subject subject = SecurityUtils.getSubject();
        UsernamePasswordToken token = new UsernamePasswordToken(authLoginVO.getUserName(), authLoginVO.getPassword());
        try {
            subject.login(token);
            UserLoginDTO userLogin = userService.getUserLoginInfo((User) SecurityUtils.getSubject().getPrincipal());
            // 判断用户启用停用状态，停用的无法登录
            if (UserStatusEnum.DISABLE.getId().equals(userLogin.getUserStatus())) {
                throw new CustomException("账户已停用，无法登录，请联系管理员");
            }
            userLogin.setAuthToken(subject.getSession().getId());
            return R.ok().put("data", userLogin);
        } catch (IncorrectCredentialsException e) {
            return R.error("用户名或密码不正确");
        } catch (CustomException | AuthenticationException e) {
            return R.error(e.getMessage());
        }
    }

    @ApiOperation(value = "登出", notes = "登出", produces = "application/json", response = R.class)
    @PostMapping("/logout")
    public R logoutPost() {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            subject.logout();
        }
        return R.ok();
    }

    @ApiIgnore
    @PostMapping("/isLogin")
    public boolean isLogin() {
        return SecurityUtils.getSubject().isAuthenticated();
    }

    @ApiOperation(value = "获取登录信息", notes = "获取登录信息", produces = "application/json", response = UserLoginDTO.class)
    @PostMapping("/get")
    public R getAuth() {
        UserLoginDTO userLogin = userService.getUserLoginInfo(RealmUtils.getUser());
        // 获取当前用户登录token
        Subject subject = SecurityUtils.getSubject();
        userLogin.setAuthToken(subject.getSession().getId());
        // 获取IOP模块的项目列表
        return R.ok().put("data", userLogin);
    }

    @ApiOperation(value = "获取模块菜单信息", notes = "获取模块菜单信息", produces = "application/json", response = MenuDTO.class)
    @PostMapping("/menus/tree")
    public R getUserMenu() {
        return R.data(this.permissionService.getUserMenu(this.getUser().getRoleId()));
    }

    @PostMapping("/getUser")
    public R getAuthUser() {
        return R.data(getUser());
    }

    @ApiOperation(value = "重置用户密码", notes = "重置用户密码", produces = "application/json")
    @PostMapping("/password/reset")
    public R resetPassword(@RequestBody @Validated ResetPasswordVO resetPassword) {
        User user = userService.getById(getUserId());
        PasswordUtils.checkUserPassword(user, resetPassword.getOldPassword());
        user.setPassword(resetPassword.getPassword());
        PasswordUtils.setUserPassword(user);
        userService.updateById(user);
        return R.ok();
    }


    @PostMapping("/getUserId")
    public R getAuthUserId() {
        return R.data(getUserId());
    }

    @PostMapping("/listMasterId")
    public R listAuthMasterId() {
        return R.data(listMasterId());
    }

    @PostMapping("/masterIds/get")
    public R getAuthMasterIds(@RequestBody @Validated UserMasterGetVO getVO) {
        return R.data(this.getMasterIds(getVO));
    }

    @PostMapping("/masterIds/get/all")
    public R getAllMasterIds(@RequestBody @Validated UserMasterGetVO getVO) {
        return R.data(this.userResourceService.getMasterIdByInfo(getVO));
    }

    @PostMapping("/permission/get")
    public R getPermission(@RequestBody @Validated PermissionGetVO getVO) {
        return R.data(permissionService.getPermissions(getVO.getPermissionId(), this.getUser().getRoleId()));
    }

}
