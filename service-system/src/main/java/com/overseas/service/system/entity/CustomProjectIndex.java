package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.Base;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_custom_project_index")
public class CustomProjectIndex extends Base {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long projectId;

    private String content;
}
