package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.TimeBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@TableName("u_user_custom_index")
@EqualsAndHashCode(callSuper = true)
public class UserCustomIndex extends TimeBase {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private String module;

    private String identify;

    private String content;
}