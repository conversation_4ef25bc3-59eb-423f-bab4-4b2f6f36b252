package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-29 10:52
 */
@Setter
@Getter
@TableName("d_country")
public class Country {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String countryId;

    private String countryName;

    private String provinceId;

    private String provinceName;

    private String cityId;

    private String cityName;

    private Integer level;
}
