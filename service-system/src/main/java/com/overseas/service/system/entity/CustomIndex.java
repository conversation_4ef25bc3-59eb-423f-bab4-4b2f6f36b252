package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.TimeBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@TableName("d_custom_index")
@EqualsAndHashCode(callSuper = true)
public class CustomIndex extends TimeBase {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String module;

    private String baseModule;

    private String content;

    private String description;
}