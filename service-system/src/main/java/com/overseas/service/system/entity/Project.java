package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.Base;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@TableName("m_project")
public class Project extends Base {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 项目标识
     */
    private String project;

    /**
     * 项目名称
     */
    private String projectName;


    /**
     * 老项目标识
     */
    private String oldIdentify;

    /**
     * 项目ID
     */
    private Long parentId;

    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


}
