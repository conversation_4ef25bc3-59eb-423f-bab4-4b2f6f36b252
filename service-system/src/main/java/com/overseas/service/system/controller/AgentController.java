package com.overseas.service.system.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.service.system.service.UserService;
import com.overseas.service.system.enums.user.UserTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-16 19:27
 */
@Api(value = "代理商管理", description = "账号管理")
@RestController
@RequestMapping("/sys/agents/")
@RequiredArgsConstructor
public class AgentController extends AbstractController {

    private final UserService userService;

    @PostMapping("/select")
    @ApiOperation(value = "获取代理商下拉", notes = "获取代理商下拉", produces = "application/json", response = SelectDTO.class)
    public R selectAgent() {
        // 非管理账户返回空集合
        if (UserTypeEnum.MANAGER.getId().equals(getUser().getUserType())) {
            return R.data(this.userService.selectPermissionAgent(getUserId()));
        } else {
            return R.data(List.of());
        }
    }

    @ApiOperation(value = "获取全部代理商下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/all/select")
    public R getAllAgentSelect() {
        return R.data(this.userService.getAllAgentSelect());
    }
}
