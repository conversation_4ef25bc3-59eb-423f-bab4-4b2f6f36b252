package com.overseas.service.system.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.TreeNodeDTO;
import com.overseas.common.dto.sys.resource.LinkageStringDto;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.service.system.entity.Country;
import com.overseas.service.system.entity.CountryCity;
import com.overseas.service.system.enums.area.AreaLevelEnum;
import com.overseas.service.system.mapper.CountryCityMapper;
import com.overseas.service.system.mapper.CountryMapper;
import com.overseas.service.system.service.CountryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-16 10:07
 */
@Service
@RequiredArgsConstructor
public class CountryServiceImpl extends ServiceImpl<CountryMapper, Country> implements CountryService {

    private final CountryCityMapper countryCityMapper;

    @Override
    public List<SelectDTO3> selectAreaCountryAlias(AreaCountrySelectVO selectVO) {
        return baseMapper.selectAreaCountryAlias(new QueryWrapper<Country>().eq("dc.level", 1)
                .in(CollectionUtils.isNotEmpty(selectVO.getCountryIds()), "dc.country_id", selectVO.getCountryIds())
                .isNotNull("dca.id")
                .orderByAsc("dca.country_id"));
    }

    @Override
    public List<SelectDTO3> selectAreaCountry(AreaCountrySelectVO selectVO) {
        return baseMapper.selectAreaCountry(new QueryWrapper<Country>().eq("level", 1)
                .in(CollectionUtils.isNotEmpty(selectVO.getCountryIds()), "country_id", selectVO.getCountryIds())
                .orderByAsc("`key`"));
    }

    @Override
    public List<TreeNodeDTO> areaCityTree(AreaCountrySelectVO selectVO) {
        List<TreeNodeDTO> countryNode = this.selectAreaCountry(selectVO).stream()
                .map(u -> new TreeNodeDTO(Long.parseLong(u.getKey()), u.getTitle(), 0L))
                .collect(Collectors.toList());
        if (countryNode.isEmpty()) {
            return List.of();
        }
        List<TreeNodeDTO> cityNode = this.countryCityMapper.selectList(new LambdaQueryWrapper<CountryCity>()
                .eq(CountryCity::getShowStatus, 1)
                .in(CountryCity::getCountryId, countryNode.stream().map(TreeNodeDTO::getId).collect(Collectors.toList()))
        ).stream().map(u -> new TreeNodeDTO(u.getId(), String.format("%s（%s）", u.getCityName(), u.getChineseName()), u.getCountryId())).collect(Collectors.toList());
        countryNode.addAll(cityNode);
        return countryNode;
    }

    @Override
    public List<SelectDTO> selectCountry(AreaCountrySelectVO selectVO) {
        return baseMapper.selectCountry(new QueryWrapper<Country>()
                .in(CollectionUtils.isNotEmpty(selectVO.getCountryIds()),
                        "country_id", selectVO.getCountryIds())
                .orderByAsc("country_id"));
    }

    @Override
    public List<SelectDTO> selectLanguages(AreaCountrySelectVO selectVO) {
        return baseMapper.selectLanguages(new QueryWrapper<Country>()
                .in(CollectionUtils.isNotEmpty(selectVO.getCountryIds()),
                        "country_id", selectVO.getCountryIds())
                .ne("country_language", "")
                .orderByAsc("country_id"));
    }

    @Override
    public List<LinkageStringDto> linkageCity(String countryId) {
        List<Country> areas = this.lambdaQuery().eq(Country::getCountryId, countryId)
                .in(Country::getLevel, List.of(AreaLevelEnum.PROVINCE.getId(), AreaLevelEnum.CITY.getId()))
                .orderByAsc(Country::getLevel)
                .list();
        Map<String, LinkageStringDto> res = new HashMap<>();
        areas.forEach(ind -> {
            if (AreaLevelEnum.PROVINCE.getId().equals(ind.getLevel())) {
                res.put(ind.getProvinceId(), new LinkageStringDto(ind.getProvinceId(), ind.getProvinceName(), new ArrayList<LinkageStringDto>()));
            } else {
                LinkageStringDto dto = res.get(ind.getProvinceId());
                if (dto != null) {
                    dto.getChildren().add(new LinkageStringDto(ind.getCityId(), ind.getCityName(), List.of()));
                }
            }
        });
        return new ArrayList<>(res.values());
    }
}
