package com.overseas.service.system.enums;

/**
 * 权限类型枚举
 */
public enum PermissionTypeEnum {
    UNKNOWN(0, "未知"),
    MODULE(1, "模块"),
    MENU(2, "菜单"),
    PAGE(3, "页面"),
    API_BUTTON(4, "接口/功能按钮"),
    FIELD(5, "字段"),
    PAGE_MODULE(6, "页面模块");

    private Integer key;
    private String name;

    PermissionTypeEnum(final Integer key, final String name) {
        this.key = key;
        this.name = name;
    }

    /**
     * 获取枚举类型
     *
     * @param id 枚举id
     * @return 枚举对象
     */
    public static PermissionTypeEnum getById(final Integer id) {
        for (PermissionTypeEnum enumValue : values()) {
            if (enumValue.getKey().equals(id)) {
                return enumValue;
            }
        }
        return null;
    }

    /**
     * 获取名称
     *
     * @param id 枚举id
     * @return 枚举名称
     */
    public static String getNameById(final Integer id) {
        PermissionTypeEnum enumValue = PermissionTypeEnum.getById(id);
        return null == enumValue ? null : enumValue.getName();
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
