package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "u_permission")
public class Permission {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.id
     *
     * @mbg.generated
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.pid
     *
     * @mbg.generated
     */
    private Integer pid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.permission_name
     *
     * @mbg.generated
     */
    private String permissionName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.permission_url
     *
     * @mbg.generated
     */
    private String permissionUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.permission_type
     *
     * @mbg.generated
     */
    private Integer permissionType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.permission_status
     *
     * @mbg.generated
     */
    private Byte permissionStatus;

    private Integer permissionLevel;


    private String permissionIcon;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.module
     *
     * @mbg.generated
     */
    private Byte module;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.is_required
     *
     * @mbg.generated
     */
    private Integer isRequired;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.is_allot
     *
     * @mbg.generated
     */
    private Byte isAllot;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.create_uid
     *
     * @mbg.generated
     */
    private Integer createUid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.update_uid
     *
     * @mbg.generated
     */
    private Integer updateUid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column u_permission.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;
}
