package com.overseas.service.system.controller;

import com.overseas.service.system.entity.UserResource;
import com.overseas.service.system.service.UserResourceService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-12 10:44
 */
@RestController
@RequestMapping("/sys/userResource")
@RequiredArgsConstructor
public class UserResourceController extends AbstractController {

    private final UserResourceService userResourceService;

    @PostMapping("/listResourceByUserId")
    public List<UserResource> listResourceByUserId(@RequestBody Integer userId) {
        return userResourceService.listUserResource(userId);
    }
}
