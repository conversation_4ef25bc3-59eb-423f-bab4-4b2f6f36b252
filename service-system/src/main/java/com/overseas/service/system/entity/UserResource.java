package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("u_user_resource")
public class UserResource {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer userId;
    private Integer resourceId;
    private Integer resourceType;
    private Integer createUid;
    private Date createTime;
}
