package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.TimeBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@TableName("m_consult")
@EqualsAndHashCode(callSuper = true)
public class Consult extends TimeBase {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String name;

    private String company;

    private String phone;

    private String mail;

    private Integer status;
}
