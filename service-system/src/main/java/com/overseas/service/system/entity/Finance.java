package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("u_finance")
public class Finance {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer userId;

    private BigDecimal amount;

    private BigDecimal consume;

    private BigDecimal showConsume;

    private BigDecimal dayCost;

    private BigDecimal showDayCost;

    private BigDecimal backAmount;

    private BigDecimal backConsume;

    private Integer createUid;

    private Integer updateUid;

    private Date createTime;

    private Date updateTime;
}
