package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.Base;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@TableName("sys_msg_resource")
public class SysMsgResource extends Base {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long msgId;

    private Integer userId;

    private Integer isRead;
}
