package com.overseas.service.system.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.sys.project.ProjectGetDTO;
import com.overseas.common.dto.sys.project.ProjectByMasterDTO;
import com.overseas.common.dto.sys.project.ProjectListDTO;
import com.overseas.common.vo.sys.project.*;
import com.overseas.service.system.service.ProjectService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 **/
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/sys/projects")
public class ProjectController extends AbstractController {

    private final ProjectService projectService;

    @ApiOperation(value = "根据账户获取项目标识", response = ProjectByMasterDTO.class)
    @PostMapping("/by/master")
    public R maseter(@RequestBody @Validated ProjectByMasterVO byMasterVO) {
        return R.data(this.projectService.projectInfoByMasterIds(byMasterVO.getMasterIds(), null));
    }

    @ApiOperation(value = "根据账户获取账户所属项目Map", response = Map.class)
    @PostMapping("/master/project")
    public R maseter(@RequestBody @Validated ProjectForMasterVO masterVO) {
        return R.data(this.projectService.getMasterProject(masterVO));
    }

    @ApiOperation(value = "根据用户获取项目下资源", response = ProjectByMasterDTO.class)
    @PostMapping("/resource")
    public R resource(@RequestBody @Validated ProjectResourceVO resourceVO) {
        return R.data(this.projectService.projectResource(resourceVO));
    }

    @ApiOperation(value = "项目保存")
    @PostMapping("/save")
    public R save(@RequestBody @Validated ProjectSaveVO saveVO) {
        return R.data(this.projectService.saveProject(saveVO, this.getUserId()));
    }

    @ApiOperation(value = "根据字段获取自定义规则", produces = "application/json", response = ProjectGetDTO.class)
    @PostMapping("/get")
    public R getProject(@Validated @RequestBody ProjectGetVO getVO) {
        return R.data(this.projectService.getProject(getVO));
    }

    @ApiOperation(value = "获取项目下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectProject(@Validated @RequestBody ProjectSelectGetVO getVO) {
        getVO.setMasterIds(this.listMasterId());
        return R.data(this.projectService.selectProject(getVO));
    }

    @ApiOperation(value = "获取项目下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select/all")
    public R selectAllProject() {
        return R.data(this.projectService.selectAllProject());
    }

    @ApiOperation(value = "项目更新")
    @PostMapping("/update")
    public R update(@RequestBody @Validated ProjectUpdateVO updateVO) {
        projectService.updateProject(updateVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "项目列表", response = ProjectListDTO.class)
    @PostMapping("/list")
    public R list(@RequestBody @Validated ProjectListVO listVO) {
        return R.page(projectService.listProject(listVO, this.getUserId()));
    }

    @ApiOperation(value = "项目列表", response = ProjectListDTO.class)
    @PostMapping("/set/master/select")
    public R setMasterSelect() {
        return R.data(projectService.setMasterSelect(this.getUserId(), listMasterId()));
    }

    @ApiOperation(value = "获取设置的账户", response = ProjectListDTO.class)
    @PostMapping("/master/get")
    public R getMaster(@RequestBody @Validated ProjectGetVO projectGetVO) {
        return R.data(projectService.getSetMaster(projectGetVO, this.getUserId()));
    }

    @ApiOperation(value = "设置账户", response = ProjectListDTO.class)
    @PostMapping("/master/set")
    public R setMaster(@RequestBody @Validated ProjectSetMasterVO setMasterVO) {
        projectService.setMaster(setMasterVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取设置的客户指标", response = ProjectListDTO.class)
    @PostMapping("/action/map/get")
    public R getActionMap(@RequestBody @Validated ProjectGetVO projectGetVO) {
        return R.data(projectService.getActionMap(projectGetVO, this.getUserId()));
    }

    @ApiOperation(value = "设置客户指标", response = ProjectListDTO.class)
    @PostMapping("/action/map/set")
    public R setActionMap(@RequestBody @Validated ProjectSetActionMapVO setActionMapVO) {
        projectService.setActionMap(setActionMapVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "获取设置的计算指标数据", response = ProjectListDTO.class)
    @PostMapping("/custom/index/get")
    public R getCustomIndex(@RequestBody @Validated ProjectGetVO projectGetVO) {
        return R.data(projectService.getCustomIndex(projectGetVO, this.getUserId()));
    }

    @ApiOperation(value = "设置自定义计算指标数据", response = ProjectListDTO.class)
    @PostMapping("/custom/index/set")
    public R setCustomIndex(@RequestBody @Validated ProjectSetCustomIndexVO setCustomIndexVO) {
        projectService.setCustomIndex(setCustomIndexVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "根据项目ID获取名称数据", response = SelectDTO.class)
    @PostMapping("/select/by/info")
    public R projectByIds(@RequestBody @Validated ProjectSelectByInfoVO idsVO) {
        return R.data(projectService.selectProjectByInfo(idsVO));
    }
}
