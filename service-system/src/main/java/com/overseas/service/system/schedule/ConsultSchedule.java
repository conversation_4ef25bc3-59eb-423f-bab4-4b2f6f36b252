package com.overseas.service.system.schedule;

import com.overseas.service.system.service.ConsultService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Profile({"online"})
public class ConsultSchedule {

    private final ConsultService consultService;

    @Scheduled(cron = "0 0 0 * * ?")
    public void pushConsult() throws Exception {

        this.consultService.pushConsult();
    }
}
