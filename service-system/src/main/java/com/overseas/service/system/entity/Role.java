package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName(value = "u_role")
public class Role {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String roleName;
    private String description;
    private Integer roleType;
    private Byte isDel;
    private Integer createUid;
    private Integer updateUid;
    private Date createTime;
    private Date updateTime;
    private String permissions;
}
