package com.overseas.service.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexChildColumnDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexParentColumnDTO;
import com.overseas.common.dto.sys.project.*;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.FeignR;
import com.overseas.common.utils.HumpLineUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.monitor.action.TrackerActionByProjectSelectVO;
import com.overseas.common.vo.market.monitor.action.TrackerActionGetProjectActionMapVO;
import com.overseas.common.vo.sys.project.*;
import com.overseas.service.system.entity.CustomIndex;
import com.overseas.service.system.entity.CustomProjectIndex;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.service.system.entity.Project;
import com.overseas.service.system.entity.ProjectResource;
import com.overseas.service.system.feign.FgMarketService;
import com.overseas.service.system.mapper.CustomIndexMapper;
import com.overseas.service.system.mapper.CustomProjectIndexMapper;
import com.overseas.service.system.mapper.ProjectMapper;
import com.overseas.service.system.mapper.ProjectResourceMapper;
import com.overseas.service.system.service.ProjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> implements ProjectService {

    private final ProjectResourceMapper projectResourceMapper;

    private final FgMarketService fgMarketService;

    private final CustomProjectIndexMapper customProjectIndexMapper;

    private final CustomIndexMapper customIndexMapper;

    @Override
    public String projectByMasterIds(List<Long> masterIds, Long projectId) {
        if (CollectionUtils.isEmpty(masterIds) && ObjectUtils.isNullOrZero(projectId)) {
            return "";
        }
        return this.projectInfoByMasterIds(masterIds, projectId).getIdentify();
    }

    @Override
    public ProjectByMasterDTO projectInfoByMasterIds(List<Long> masterIds, Long projectId) {
        ProjectByMasterDTO projectByMasterDTO = ProjectByMasterDTO.builder()
                .projectId(this.projectIdByMasterIds(masterIds, projectId))
                .identify("").build();
        if (CollectionUtils.isNotEmpty(projectByMasterDTO.getProjectId())) {
            List<Project> projects = this.baseMapper.selectBatchIds(projectByMasterDTO.getProjectId());
            List<String> identifies = projects.stream().map(Project::getProject).distinct().collect(Collectors.toList());
            if (projects.size() == projectByMasterDTO.getProjectId().size() && identifies.size() == 1) {
                projectByMasterDTO.setIdentify(identifies.get(0));
                projectByMasterDTO.setOldIdentify(projects.get(0).getOldIdentify());
            }
        }
        return projectByMasterDTO;
    }

    @Override
    public ProjectResourceDTO projectResource(ProjectResourceVO projectResourceVO) {
        Project project = this.baseMapper.selectById(projectResourceVO.getProjectId());
        if (null == project) {
            throw new CustomException("项目不存在");
        }
        return ProjectResourceDTO.builder().projectId(project.getId()).identify(project.getProject())
                .resourceIds(this.projectResourceMapper.selectList(new LambdaQueryWrapper<ProjectResource>()
                                .eq(ProjectResource::getProjectId, project.getId())
                                .eq(ProjectResource::getIsDel, IsDelEnum.NORMAL.getId())
                        ).stream().map(ProjectResource::getResourceId).collect(Collectors.toList())
                ).build();
    }

    @Override
    public Project saveProject(ProjectSaveVO saveVO, Integer operatorUid) {
        //检查简写是否重复
        long count = this.baseMapper.selectCount(new LambdaQueryWrapper<Project>().eq(Project::getProject, saveVO.getProject()));
        if (count > 0) {
            throw new CustomException("项目简称已被使用，请更换后再试");
        }
        //检查名称是否使用
        checkProjectName(saveVO.getProjectName(), operatorUid, null);
        //保存项目
        Project project = new Project();
        if (ObjectUtils.isNotNullOrZero(saveVO.getId())) {
            project.setId(saveVO.getId());
        }
        project.setProjectName(saveVO.getProjectName());
        project.setProject(saveVO.getProject());
        project.setCreateUid(operatorUid);
        this.baseMapper.insert(project);
        return project;
    }

    @Override
    public Project updateProject(ProjectUpdateVO updateVO, Integer operateUid) {
        Project project = findProjectById(updateVO.getId(), operateUid);
        if (project.getProjectName().equals(updateVO.getProjectName())) {
            return project;
        }
        //检查项目名称
        checkProjectName(updateVO.getProjectName(), operateUid, updateVO.getId());
        //更新项目
        Project update = new Project();
        update.setProjectName(updateVO.getProjectName());
        this.baseMapper.update(update, new LambdaUpdateWrapper<Project>().eq(Project::getId, project.getId()));
        project.setProjectName(update.getProjectName());
        return project;
    }

    @Override
    public PageUtils<?> listProject(ProjectListVO projectListVO, Integer operatorUid) {
        IPage<Project> iPage = new Page<>(projectListVO.getPage(), projectListVO.getPageNum());
        iPage = this.baseMapper.selectPage(iPage, new LambdaQueryWrapper<Project>()
                .eq(Project::getCreateUid, operatorUid)
                .like(StringUtils.isNotBlank(projectListVO.getSearch()), Project::getProjectName, projectListVO.getSearch())
                .orderByDesc(Project::getId)
        );
        Map<Long, Long> masterCountMap;
        if (!iPage.getRecords().isEmpty()) {
            masterCountMap = projectResourceMapper.selectList(new QueryWrapper<ProjectResource>()
                    .select("count(id) AS resource_id, project_id ")
                    .lambda()
                    .in(ProjectResource::getProjectId, iPage.getRecords().stream().map(Project::getId).collect(Collectors.toList()))
                    .groupBy(ProjectResource::getProjectId)
            ).stream().collect(Collectors.toMap(ProjectResource::getProjectId, ProjectResource::getResourceId));
        } else {
            masterCountMap = new HashMap<>();
        }
        return new PageUtils<>(iPage.getRecords().stream()
                .map(u -> ProjectListDTO.builder()
                        .id(u.getId())
                        .project(u.getProject())
                        .projectName(u.getProjectName())
                        .masterCount(masterCountMap.getOrDefault(u.getId(), 0L)).build()
                ).collect(Collectors.toList()),
                iPage.getTotal());
    }

    @Override
    public List<SelectDTO> setMasterSelect(Integer userId, List<Integer> permissionMasterIds) {
        return projectResourceMapper.selectMasterNotSet(new QueryWrapper<>()
                .in("uu.id", permissionMasterIds)
                .eq("uu.user_status", 1)
                .eq("uu.parent_id", userId)
                .isNull("mpr.id ")
                .orderByDesc("uu.id")
        );
    }

    @Override
    public List<ProjectSetMasterListDTO> getSetMaster(ProjectGetVO getVO, Integer operatorUid) {
        this.findProjectById(getVO.getId(), operatorUid);
        return projectResourceMapper.selectMaster(new QueryWrapper<ProjectResource>()
                .eq("mpr.project_id", getVO.getId())
                .eq("mpr.is_del", IsDelEnum.NORMAL.getId())
                .orderByDesc("mpr.id")
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setMaster(ProjectSetMasterVO setMasterVO, Integer operatorUid) {
        //验证项目归属
        this.findProjectById(setMasterVO.getId(), operatorUid);
        //插入账户数据
        if (CollectionUtils.isNotEmpty(setMasterVO.getMasterIds())) {
            projectResourceMapper.insertByUk(setMasterVO.getMasterIds()
                    .stream().map(u -> {
                        ProjectResource entity = new ProjectResource();
                        entity.setProjectId(setMasterVO.getId());
                        entity.setResourceId(u);
                        entity.setCreateUid(operatorUid);
                        return entity;
                    }).collect(Collectors.toList()));
        }
    }

    @Override
    public ProjectActionMapDTO getActionMap(ProjectGetVO getVO, Integer operatorUid) {
        Project project = findProjectById(getVO.getId(), operatorUid);
        ProjectActionMapDTO mapDTO = new ProjectActionMapDTO();
        mapDTO.setActionMaps(fgMarketService.getProjectActionMap(TrackerActionGetProjectActionMapVO
                        .builder().project(project.getProject()).build())
                .getData()
        );
        mapDTO.setId(project.getId());
        mapDTO.setProjectName(project.getProjectName());
        return mapDTO;
    }

    @Override
    public void setActionMap(ProjectSetActionMapVO actionMapVO, Integer operatorUid) {
        Project project = findProjectById(actionMapVO.getId(), operatorUid);
        actionMapVO.setProject(project.getProject());
        FeignR<?> feignR = fgMarketService.setProjectActionMap(actionMapVO);
        if (!feignR.getCode().equals(0)) {
            throw new CustomException(feignR.getMsg());
        }
    }

    @Override
    public ProjectGetCustomIndexDTO getCustomIndex(ProjectGetVO getVO, Integer operatorUid) {
        Project project = findProjectById(getVO.getId(), operatorUid);
        CustomProjectIndex customProjectIndex = customProjectIndexMapper.selectOne(new LambdaQueryWrapper<CustomProjectIndex>()
                .eq(CustomProjectIndex::getProjectId, getVO.getId()));
        ProjectGetCustomIndexDTO customIndexDTO;
        if (null != customProjectIndex) {
            customIndexDTO = JSONObject.parseObject(customProjectIndex.getContent(), ProjectGetCustomIndexDTO.class);
        } else {
            customIndexDTO = new ProjectGetCustomIndexDTO();
            customIndexDTO.setId(getVO.getId());
            customIndexDTO.setBaseFields(new ArrayList<>());
            customIndexDTO.setComputedFields(null);
            customIndexDTO.setRateFields(List.of());
        }
        //补充 基础指标数据
        List<ProjectSetActionMapVO.ActionMapVO> actionMapList = fgMarketService.getProjectActionMap(TrackerActionGetProjectActionMapVO
                .builder().project(project.getProject()).build()).getData();
        if (!actionMapList.isEmpty()) {
            Map<String, ProjectSetCustomIndexVO.BaseCustomContentVO> baseMap = customIndexDTO.getBaseFields().stream()
                    .peek(u -> {
                        if (null == u.getIndexCategory()) {
                            u.setIndexCategory("-1");
                        }
                    }).collect(Collectors.toMap(ProjectSetCustomIndexVO.BaseCustomContentVO::getKey, Function.identity()));
            for (ProjectSetActionMapVO.ActionMapVO action : actionMapList) {
                ProjectSetCustomIndexVO.BaseCustomContentVO base;
                if (baseMap.containsKey(action.getSource())) {
                    base = baseMap.get(action.getSource());
                } else {
                    base = new ProjectSetCustomIndexVO.BaseCustomContentVO();
                    base.setIndexCategory("-1");
                    base.setIsShow(action.getIsCommon() == 1 ? 1 : 0);
                    base.setAction(action.getAction());
                    base.setKey(action.getSource());
                    base.setIsCommon(action.getIsCommon());
                }
                base.setTitle(action.getSourceName());
                baseMap.put(action.getSource(), base);
            }
            customIndexDTO.setBaseFields(new ArrayList<>(baseMap.values()));
        }
        //  补充数据
        if (CollectionUtils.isNotEmpty(customIndexDTO.getComputedFields())) {
            customIndexDTO.getComputedFields().forEach(compute -> {
                if (null == compute.getIndexCategory()) {
                    compute.setIndexCategory("-1");
                }
            });
        }
        customIndexDTO.setActionFields(actionCustom());
        return customIndexDTO;
    }


    @Override
    public void setCustomIndex(ProjectSetCustomIndexVO customIndexVO, Integer operatorUid) {
        Project project = findProjectById(customIndexVO.getId(), operatorUid);
        //完善 action index
        completeActionIndex(customIndexVO);
        //更新操作数据
        CustomProjectIndex customProjectIndex = customProjectIndexMapper.selectOne(new LambdaQueryWrapper<CustomProjectIndex>()
                .eq(CustomProjectIndex::getProjectId, customIndexVO.getId()));
        if (null == customProjectIndex) {
            customProjectIndex = new CustomProjectIndex();
            customProjectIndex.setCreateUid(operatorUid);
            customProjectIndex.setContent(JSONObject.toJSONString(customIndexVO));
            customProjectIndex.setProjectId(customIndexVO.getId());
            customProjectIndexMapper.insert(customProjectIndex);
        } else {
            CustomProjectIndex update = new CustomProjectIndex();
            update.setContent(JSONObject.toJSONString(customIndexVO));
            update.setUpdateUid(operatorUid);
            customProjectIndexMapper.update(update, new LambdaUpdateWrapper<CustomProjectIndex>()
                    .eq(CustomProjectIndex::getProjectId, customIndexVO.getId()));
        }
        //基础指标数据
        Map<String, String> baseRuleMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(customIndexVO.getBaseFields())) {
            baseRuleMap = customIndexVO.getBaseFields().stream()
                    .collect(Collectors.toMap(
                            ProjectSetCustomIndexVO.CustomContentVO::getKey, ProjectSetCustomIndexVO.BaseCustomContentVO::getRAction
                    ));
        }
        Map<String, String> rateRuleMap = new HashMap<>();
        //系数指标数据
        if (CollectionUtils.isNotEmpty(customIndexVO.getRateFields())) {
            rateRuleMap = customIndexVO.getRateFields().stream()
                    .collect(Collectors.toMap(ProjectSetCustomIndexVO.CustomContentVO::getKey, ProjectSetCustomIndexVO.CustomContentVO::getTitle));
        }
        //进行基础指标规则校验
        if (baseRuleMap.isEmpty()) {
            if (customIndexVO.getComputedFields().isEmpty()) {
                return;
            } else {
                throw new CustomException("基础指标无数据，计算指标数据无意义");
            }
        }
        // 生成计算指标规则
        Map<String, String> computedRuleMap = new HashMap<>();
        //基础指标添加进入
        for (ProjectGetCustomIndexDTO.ActionCustomContentVO ac : actionCustom()) {
            rateRuleMap.put(ac.getKey(), ac.getRule());
        }
        findComputedField(customIndexVO.getComputedFields(), computedRuleMap, baseRuleMap, rateRuleMap);
        // 生成入库规则
        List<CustomIndexChildColumnDTO> columns = new ArrayList<>();
        // 基础指标生成
        for (ProjectSetCustomIndexVO.BaseCustomContentVO base : customIndexVO.getBaseFields()) {
            if (0 == base.getIsShow() || ObjectUtils.isNotNullOrZero(base.getIsCommon())) {
                continue;
            }
            columns.add(CustomIndexChildColumnDTO.builder()
                    .key(base.getRAction())
                    .title(base.getTitle())
                    .rule(String.format("SUM(`idx_%s`) AS `%s`", base.getRAction(), base.getRAction()))
                    .isChecked(false)
                    .insertRule("")
                    .indexCategory(base.getIndexCategory())
                    .build()
            );
        }
        // 生成计算指标规则
        for (ProjectSetCustomIndexVO.ComputedCustomContentVO computed : customIndexVO.getComputedFields()) {
            if (0 == computed.getIsShow()) {
                continue;
            }
            columns.add(CustomIndexChildColumnDTO.builder()
                    .key(computed.getActionIndex())
                    .title(computed.getTitle())
                    .rule(String.format("IFNULL(%s, 0) AS `%s`", computedRuleMap.get(computed.getKey()), HumpLineUtils.humpToLine2(computed.getActionIndex())))
                    .isChecked(false)
                    .insertRule("")
                    .indexCategory(computed.getIndexCategory())
                    .build()
            );
        }
        String moduleName = String.format("report_%s", project.getProject());
        String content = "[]";
        if (!columns.isEmpty()) {
            content = new Gson().toJson(List.of(
                    CustomIndexParentColumnDTO.builder()
                            .key("action").title("转化指标")
                            .sort(10)
                            .columns(columns)
                            .build()
            ));
        }
        CustomIndex customIndex = customIndexMapper.selectOne(new LambdaQueryWrapper<CustomIndex>()
                .eq(CustomIndex::getModule, moduleName)
        );
        //存储数据
        if (null == customIndex) {
            customIndex = new CustomIndex();
            customIndex.setModule(moduleName);
            customIndex.setBaseModule("[\"report_base\"]");
            customIndex.setContent(content);
            customIndexMapper.insert(customIndex);
        } else {
            customIndex.setContent(content);
            customIndexMapper.updateById(customIndex);
        }
    }

    /**
     * 根据账户ID 和项目ID
     *
     * @param masterIds 账户ID
     * @param projectId 项目ID
     * @return 返回项目ID
     */
    private List<Long> projectIdByMasterIds(List<Long> masterIds, Long projectId) {
        if (ObjectUtils.isNotNullOrZero(projectId)) {
            return List.of(projectId);
        }
        //如果账户不为空，则获取账户ID数据
        if (CollectionUtils.isNotEmpty(masterIds)) {
            List<ProjectResource> projectResources = this.projectResourceMapper.selectList(new QueryWrapper<ProjectResource>()
                    .select("distinct project_id AS project_id")
                    .lambda()
                    .in(ProjectResource::getResourceId, masterIds)
                    .eq(ProjectResource::getIsDel, IsDelEnum.NORMAL.getId()));
            return projectResources.stream().map(ProjectResource::getProjectId).collect(Collectors.toList());
        }
        return List.of();
    }

    /**
     * 返回项目信息
     *
     * @param id          项目ID
     * @param operatorUid 操作用户
     * @return 返回数据
     */
    private Project findProjectById(Long id, Integer operatorUid) {
        Project project = this.baseMapper.selectById(id);
        if (null == project || !project.getCreateUid().equals(operatorUid)) {
            throw new CustomException("项目不存在");
        }
        return project;
    }

    /**
     * 判定项目名称是否在当前账户下重复
     *
     * @param projectName 项目名称
     * @param operatorUid 操作用户
     * @param id          项目ID
     */
    private void checkProjectName(String projectName, Integer operatorUid, Long id) {
        long count = this.baseMapper.selectCount(new LambdaQueryWrapper<Project>()
                .eq(Project::getProjectName, projectName)
                .eq(Project::getCreateUid, operatorUid)
                .ne(ObjectUtils.isNotNullOrZero(id), Project::getId, id)
        );
        if (count > 0) {
            throw new CustomException("项目名称已使用，请更换后再试");
        }
    }

    /**
     * 递归获取计算指标计算数据
     *
     * @param computedFields  计算数据
     * @param computedRuleMap 计算指标规则
     * @param baseRuleMap     基础指标规则
     */
    private void findComputedField(List<ProjectSetCustomIndexVO.ComputedCustomContentVO> computedFields,
                                   Map<String, String> computedRuleMap, Map<String, String> baseRuleMap, Map<String, String> rateRuleMap) {
        if (computedFields.isEmpty()) {
            return;
        }
        List<ProjectSetCustomIndexVO.ComputedCustomContentVO> notFinds = new ArrayList<>();
        for (ProjectSetCustomIndexVO.ComputedCustomContentVO computedField : computedFields) {
            String field1 = findIsExist(computedField.getField1(), baseRuleMap, computedRuleMap, rateRuleMap);
            String field2 = findIsExist(computedField.getField2(), baseRuleMap, computedRuleMap, rateRuleMap);
            if (null == field1 || null == field2) {
                notFinds.add(computedField);
                continue;
            }
            //增加指标
            computedRuleMap.put(computedField.getKey(),
                    String.format("ROUND((%s %s %s), %s)", field1, computedField.getComputedType(), field2,
                            computedField.getRoundVal()));
        }
        if (notFinds.size() == computedFields.size()) {
            throw new CustomException("含有无法解析的指标，请确认后再试");
        }
        findComputedField(notFinds, computedRuleMap, baseRuleMap, rateRuleMap);
    }

    /**
     * 判断字段是否存在，并进行操作
     *
     * @param field           字段
     * @param baseRuleMap     基础字段
     * @param computedRuleMap 计算指标
     * @return 返回数据
     */
    private String findIsExist(String field, Map<String, String> baseRuleMap,
                               Map<String, String> computedRuleMap, Map<String, String> rateRuleMap) {
        if (baseRuleMap.containsKey(field)) {
            return String.format("SUM(`idx_%s`)", baseRuleMap.get(field));
        } else if (computedRuleMap.containsKey(field)) {
            return String.format("( %s )", computedRuleMap.get(field));
        }
        return rateRuleMap.getOrDefault(field, null);
    }


    /**
     * 设置 custom action index 字段
     *
     * @param customIndexVO 返回数据
     */

    private void completeActionIndex(ProjectSetCustomIndexVO customIndexVO) {
        Integer actionDIndex = 11;
        Integer actionIndex = 50;
        if (ObjectUtils.isNotNullOrZero(customIndexVO.getActionDIndex())) {
            actionDIndex = Math.max(actionDIndex, customIndexVO.getActionDIndex());
        }
        if (ObjectUtils.isNotNullOrZero(customIndexVO.getActionIndex())) {
            actionIndex = Math.max(actionIndex, customIndexVO.getActionIndex());
        }
        //设置报表指标
        Map<String, String> actionToInnerMap = fgMarketService.getMapByProject(TrackerActionByProjectSelectVO.builder()
                        .projectId(customIndexVO.getId()).build())
                .getData().stream().collect(Collectors.toMap(u -> u.getKey().toString(), v -> v.getTitle().toString()));

        customIndexVO.setBaseFields(customIndexVO.getBaseFields().stream().peek(u -> u.setRAction(actionToInnerMap.get(u.getAction())))
                .filter(u -> StringUtils.isNotBlank(u.getRAction())).collect(Collectors.toList()));

        String ACTION_D = "actionD";
        String ACTION = "action";
        //删除actionMap
        for (ProjectSetCustomIndexVO.ComputedCustomContentVO compute : customIndexVO.getComputedFields()) {
            if (StringUtils.isNotBlank(compute.getActionIndex())) {
                if (compute.getActionIndex().contains(ACTION_D)) {
                    actionDIndex = isMax(actionDIndex, compute.getActionIndex().replace(ACTION_D, ""));
                } else if (compute.getActionIndex().contains(ACTION)) {
                    actionIndex = isMax(actionIndex, compute.getActionIndex().replace(ACTION, ""));
                }
            }
        }
        //计算指标生成 actionIndex
        for (ProjectSetCustomIndexVO.ComputedCustomContentVO compute : customIndexVO.getComputedFields()) {
            if (StringUtils.isBlank(compute.getActionIndex()) && 1 == compute.getIsShow()) {
                if (compute.getRoundVal() == 0) {
                    compute.setActionIndex(ACTION + actionIndex);
                    actionIndex++;
                } else {
                    compute.setActionIndex(ACTION_D + actionDIndex);
                    actionDIndex++;
                }
            }
        }
        customIndexVO.setActionDIndex(actionDIndex);
        customIndexVO.setActionIndex(actionIndex);
    }


    /**
     * 判定谁更大
     *
     * @param num1    number
     * @param number2 设置后的index
     * @return 返回数据
     */
    private Integer isMax(Integer num1, String number2) {
        if (StringUtils.isBlank(number2)) {
            return num1;
        }
        try {
            Integer num2 = Integer.parseInt(number2);
            return num1 > num2 ? num1 : num2 + 1;
        } catch (Exception e) {
            return num1;
        }
    }

    @Override
    public ProjectGetDTO getProject(ProjectGetVO getVO) {
        return this.baseMapper.getProject(new QueryWrapper<Project>().lambda()
                .eq(Project::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getId()), Project::getId, getVO.getId())
                .eq(StringUtils.isNotBlank(getVO.getProject()), Project::getProject, getVO.getProject())
                .eq(StringUtils.isNotBlank(getVO.getProjectName()), Project::getProjectName, getVO.getProjectName()));
    }

    @Override
    public List<ProjectSelectDTO> selectProject(ProjectSelectGetVO getVO) {
        if (ObjectUtils.isNullOrZero(getVO.getIsRelatedMaster())) {
            return this.baseMapper.selectProject(new QueryWrapper<Project>().lambda()
                    .eq(Project::getIsDel, IsDelEnum.NORMAL.getId())
                    .orderByAsc(Project::getId));
        }
        if (CollectionUtils.isEmpty(getVO.getMasterIds())) {
            return List.of();
        }
        return this.baseMapper.selectProjectByMasterId(new QueryWrapper<Project>()
                .eq("project.is_del", IsDelEnum.NORMAL.getId())
                .eq("resource.is_del", IsDelEnum.NORMAL.getId())
                .in("resource.resource_id", getVO.getMasterIds())
                .groupBy("project.id")
                .orderByAsc("project.id"));
    }

    @Override
    public List<ProjectSelectDTO> selectAllProject() {
        return this.baseMapper.selectAllProject(new QueryWrapper<>()
                .eq("project.is_del", IsDelEnum.NORMAL.getId())
                .orderByAsc("project.id")
        );
    }

    @Override
    public Map<Long, Long> getMasterProject(ProjectForMasterVO masterVO) {
        return this.projectResourceMapper.selectList(new QueryWrapper<ProjectResource>()
                        .lambda()
                        .in(ProjectResource::getResourceId, masterVO.getMasterIds())
                        .eq(ProjectResource::getIsDel, IsDelEnum.NORMAL.getId()))
                .stream().collect(Collectors.toMap(ProjectResource::getResourceId, ProjectResource::getProjectId));
    }

    @Override
    public List<SelectDTO> selectProjectByInfo(ProjectSelectByInfoVO idsVO) {
        return baseMapper.selectList(new LambdaQueryWrapper<Project>()
                .in(Project::getId, idsVO.getIds())
                .eq(Project::getIsDel, IsDelEnum.NORMAL.getId())
        ).stream().map(u -> new SelectDTO(u.getId(), u.getProjectName())).collect(Collectors.toList());
    }

    /**
     * 通用指标 必返回
     *
     * @return 返回数据
     */
    public List<ProjectGetCustomIndexDTO.ActionCustomContentVO> actionCustom() {
        CustomIndex customIndex = customIndexMapper.selectOne(new LambdaQueryWrapper<CustomIndex>()
                .eq(CustomIndex::getModule, "report_base"));
        if (null == customIndex) {
            return List.of();
        }
        // 项目管理设置的指标
        List<String> strings = List.of("view", "userView", "click", "userClick",
                "masterCost", "mediaCost", "consumerCost",
                "bid", "win", "clickUniqSession", "reach", "action");
        try {
            List<CustomIndexParentColumnDTO> customColumns = JSONObject.parseArray(customIndex.getContent(),
                    CustomIndexParentColumnDTO.class);
            return customColumns.stream()
                    .filter(u -> List.of("basic", "action").contains(u.getKey()))
                    .flatMap(u -> u.getColumns().stream())
                    .filter(u -> strings.contains(u.getKey()))
                    .map(v -> {
                        ProjectGetCustomIndexDTO.ActionCustomContentVO actionCustomContentVO =
                                new ProjectGetCustomIndexDTO.ActionCustomContentVO();
                        actionCustomContentVO.setKey(v.getKey());
                        actionCustomContentVO.setTitle(v.getTitle());
                        actionCustomContentVO.setRule(v.getRule().split("AS")[0]);
                        return actionCustomContentVO;
                    }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return List.of();
    }
}
