package com.overseas.service.system.enums.area;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-16 11:40
 */
@Getter
@AllArgsConstructor
public enum AreaLevelEnum implements ICommonEnum {
    COUNTRY(1, "国家"),
    PROVINCE(2, "省份"),
    CITY(3, "城市");

    private final Integer id;
    private final String name;
}
