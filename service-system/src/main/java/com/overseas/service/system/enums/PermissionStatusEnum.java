package com.overseas.service.system.enums;

/**
 * 权限状态枚举
 */
public enum PermissionStatusEnum {
    DELETE(0, "删除"),
    NORMAL(1, "正常"),
    FORBIDDEN(2, "禁用");

    private Integer key;
    private String name;

    PermissionStatusEnum(final Integer key, final String name) {
        this.key = key;
        this.name = name;
    }

    /**
     * 获取枚举类型
     *
     * @param id
     * @return
     */
    public static PermissionStatusEnum getById(final Integer id) {
        for (PermissionStatusEnum enumValue : values()) {
            if (enumValue.getKey() == id) {
                return enumValue;
            }
        }
        return null;
    }

    /**
     * 获取名称
     *
     * @param id
     * @return
     */
    public static String getNameById(final Integer id) {
        PermissionStatusEnum enumValue = PermissionStatusEnum.getById(id);
        return null == enumValue ? null : enumValue.getName();
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
