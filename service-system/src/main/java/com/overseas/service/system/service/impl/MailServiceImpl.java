package com.overseas.service.system.service.impl;

import com.overseas.service.system.service.MailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MailServiceImpl implements MailService {

    /**
     * 配置文件中邮箱
     */
    @Value("${spring.mail.username}")
    private String from;

    private final JavaMailSender mailSender;

    @Async
    @Override
    public void sendEmail(String to, String subject, String content, String fileName, InputStreamSource inputStreamSource) throws Exception {

        MimeMessage message = mailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(this.from);
            helper.setTo(to.split(";"));
            helper.setSubject(subject);
            helper.setText(content, true);
            if (null != fileName && null != inputStreamSource) {
                helper.addAttachment(MimeUtility.encodeText(fileName), inputStreamSource);
            }
            mailSender.send(message);
            log.info("发送邮件成功；收件人：{}，标题：{}，内容：{}", to, subject, content);
        } catch (MessagingException e) {
            log.error("发送邮件失败！", e);
        }
    }
}
