package com.overseas.service.system.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexSaveVO;
import com.overseas.service.system.service.CustomIndexService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "CORE-权限相关接口")
@RestController
@RequestMapping("/sys/customIndex")
@RequiredArgsConstructor
public class CustomIndexController extends AbstractController {

    private final CustomIndexService customIndexService;

    @ApiOperation("获取用户自定义列数据")
    @PostMapping("/get")
    public R listCustomIndex(@RequestBody @Validated CustomIndexGetVO getVO) {
        return R.data(this.customIndexService.getCustomIndex(getVO, this.getUserNoException()));
    }

    @ApiOperation("保存用户自定义列数据")
    @PostMapping("/save")
    public R saveCustomIndex(@RequestBody @Validated CustomIndexSaveVO saveVO) {
        this.customIndexService.save(saveVO, getUserId());
        return R.ok();
    }

    @ApiOperation("获取用户自定义列数据（包含报表字段描述）")
    @PostMapping("/get/report/select")
    public R listCustomIndexReportSelect(@RequestBody @Validated CustomIndexGetVO getVO) {
        return R.data(this.customIndexService.getCustomIndexReportSelect(getVO, this.getUserNoException()));
    }

    @ApiOperation("获取自定义列基础指标key-field对应关系")
    @PostMapping("/fieldMap/get")
    public R getCustomIndexFieldMap(@RequestBody @Validated CustomIndexGetVO getVO) {
        return R.data(this.customIndexService.getCustomIndexFieldMap(getVO, this.getUserNoException()));
    }

    @ApiOperation("获取自定义列基础指标key-field对应关系V2")
    @PostMapping("/fieldMap/get/v2")
    public R getCustomIndexFieldMapV2(@RequestBody @Validated CustomIndexGetVO getVO) {
        return R.data(this.customIndexService.getCustomIndexFieldMapV2(getVO, this.getUserNoException()));
    }

    @ApiOperation("获取用户自定义列数据（包含报表字段描述）")
    @PostMapping("/get/report")
    public R listCustomIndexReport(@RequestBody @Validated CustomIndexGetVO getVO) {
        return R.data(this.customIndexService.getCustomIndexReport(getVO, this.getUserNoException()));
    }

    @ApiOperation("获取用户自定义列数据（包含报表字段描述）")
    @PostMapping("/get/report/download")
    public R listCustomIndexReportDownload(@RequestBody @Validated CustomIndexGetVO getVO) {
        return R.data(this.customIndexService.getCustomIndexReportDownload(getVO, this.getUserNoException()));
    }

    @ApiOperation("获取用户自定义列数据")
    @PostMapping("/byModule/get")
    public R getCustomIndexByModule(@RequestBody @Validated CustomIndexGetVO getVO) {
        return R.data(this.customIndexService.getCustomIndexByModule(getVO, this.getUserNoException()));
    }

}
