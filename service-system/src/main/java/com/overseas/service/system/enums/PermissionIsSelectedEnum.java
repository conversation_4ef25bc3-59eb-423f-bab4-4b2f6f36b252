package com.overseas.service.system.enums;

/**
 * 权限项是否选中枚举
 */
public enum PermissionIsSelectedEnum {
    NOT_SELECTED(0, "未选中"),
    SELECTED(1, "选中");

    private Integer key;
    private String name;

    PermissionIsSelectedEnum(final Integer key, final String name) {
        this.key = key;
        this.name = name;
    }

    /**
     * 获取枚举类型
     *
     * @param id
     * @return
     */
    public static PermissionIsSelectedEnum getById(final Integer id) {
        for (PermissionIsSelectedEnum enumValue : values()) {
            if (enumValue.getKey() == id) {
                return enumValue;
            }
        }
        return null;
    }

    /**
     * 获取名称
     *
     * @param id
     * @return
     */
    public static String getNameById(final Integer id) {
        PermissionIsSelectedEnum enumValue = PermissionIsSelectedEnum.getById(id);
        return null == enumValue ? null : enumValue.getName();
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
