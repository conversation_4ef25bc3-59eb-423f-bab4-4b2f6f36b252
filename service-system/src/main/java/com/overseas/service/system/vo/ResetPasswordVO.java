package com.overseas.service.system.vo;

import com.overseas.common.validation.annotation.MpPassword;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("com.overseas.vo.ResetPasswordVO")
public class ResetPasswordVO {

    @NotNull(message = "原密码不能为空")
    @MpPassword(message = "原密码须包含数字和字母，数字不能顺序或相同重复")
    @ApiModelProperty("原密码")
    private String oldPassword;

    @NotNull(message = "新密码不能为空")
    @MpPassword
    @ApiModelProperty("新密码")
    private String password;
}
