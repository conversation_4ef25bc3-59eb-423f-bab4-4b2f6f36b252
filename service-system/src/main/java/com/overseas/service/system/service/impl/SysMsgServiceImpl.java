package com.overseas.service.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.dto.sys.msg.SysMsgGetDTO;
import com.overseas.common.dto.sys.msg.SysMsgListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.sys.msg.MsgIndustryEnum;
import com.overseas.common.enums.sys.msg.MsgIsReadEnum;
import com.overseas.common.enums.sys.msg.MsgReadTypeEnum;
import com.overseas.common.enums.sys.msg.MsgTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.sys.msg.*;
import com.overseas.service.system.entity.SysMsgContent;
import com.overseas.service.system.entity.SysMsg;
import com.overseas.service.system.entity.SysMsgResource;
import com.overseas.service.system.mapper.SysMsgContentMapper;
import com.overseas.service.system.mapper.SysMsgMapper;
import com.overseas.service.system.mapper.SysMsgResourceMapper;
import com.overseas.service.system.service.SysMsgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Slf4j
@Service
public class SysMsgServiceImpl implements SysMsgService {

    private final SysMsgMapper sysMsgMapper;

    private final SysMsgResourceMapper sysMsgResourceMapper;

    private final SysMsgContentMapper sysMsgContentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendMsg(SysMsgSendVO msg) {
        //存入主表
        SysMsg sysMsg = new SysMsg();
        sysMsg.setMsgType(msg.getMsgType());
        sysMsg.setMsgIndustry(msg.getMsgIndustry());
        sysMsg.setMsgTitle(msg.getMsgTitle());
        String content = msg.getMsgContent().getString("content").split("\n")[0];
        sysMsg.setMsgLine(content.substring(0, Math.min(content.length(), 300)));
        sysMsgMapper.insert(sysMsg);
        //存入content
        SysMsgContent sysMsgContent = new SysMsgContent();
        sysMsgContent.setMsgId(sysMsg.getId());
        sysMsgContent.setMsgContent(JSONObject.toJSONString(msg.getMsgContent()));
        sysMsgContentMapper.insert(sysMsgContent);
        //存入关系
        msg.getUserIds().forEach(userId -> {
            SysMsgResource sysMsgResource = new SysMsgResource();
            sysMsgResource.setMsgId(sysMsg.getId());
            sysMsgResource.setUserId(userId);
            sysMsgResource.setIsRead(MsgIsReadEnum.NOT_READ.getId());
            sysMsgResourceMapper.insert(sysMsgResource);
        });
    }

    @Override
    public SysMsgGetDTO getMsg(SysMsgGetVO getVO, Integer operateUid) {
        SysMsgResource sysMsgResource = sysMsgResourceMapper.selectOne(new LambdaQueryWrapper<SysMsgResource>()
                .eq(SysMsgResource::getUserId, operateUid)
                .eq(SysMsgResource::getMsgId, getVO.getMsgId())
        );
        if (null == sysMsgResource) {
            throw new CustomException("消息不存在");
        }
        //已读
        if (sysMsgResource.getIsRead().equals(MsgIsReadEnum.NOT_READ.getId())) {
            readMsg(SysMsgReadVO.builder().readType(MsgReadTypeEnum.READ_MSG_ID.getId())
                    .msgIds(List.of(sysMsgResource.getMsgId()))
                    .build(), operateUid);
        }
        //获取信息
        SysMsgGetDTO info = sysMsgMapper.getMsg(new QueryWrapper<>()
                .eq("sm.id", getVO.getMsgId())
                .eq("sm.is_del", IsDelEnum.NORMAL.getId())
        );
        if (null == info) {
            throw new CustomException("消息不存在");
        }
        info.setMsgContent(JSONObject.parse(info.getContent()));
        info.setMsgTypeName(ICommonEnum.getNameById(info.getMsgType(), MsgTypeEnum.class));
        info.setMsgIndustryName(ICommonEnum.getNameById(info.getMsgIndustry(), MsgIndustryEnum.class));
        return info;
    }

    @Override
    public List<SysMsgListDTO> getLastMsg(SysMsgLastGetVO lastGetVO, Integer operateUid) {
        List<SysMsgListDTO> list = sysMsgResourceMapper.msgLastList(new QueryWrapper<>()
                .gt("smr.msg_id", lastGetVO.getLastMsgId())
                .eq("smr.user_id", operateUid)
                .eq("smr.is_read", MsgIsReadEnum.NOT_READ.getId())
                .eq("sm.is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(lastGetVO.getMsgType()), "sm.msg_type", lastGetVO.getMsgType())
                .eq(ObjectUtils.isNotNullOrZero(lastGetVO.getMsgIndustry()), "sm.msg_industry", lastGetVO.getMsgIndustry())
                .orderByDesc("sm.id")
        );
        list.forEach(u -> {
            u.setMsgTypeName(ICommonEnum.getNameById(u.getMsgType(), MsgTypeEnum.class));
            if (ObjectUtils.isNotNullOrZero(u.getMsgIndustry())) {
                u.setMsgIndustryName(ICommonEnum.getNameById(u.getMsgIndustry(), MsgIndustryEnum.class));
            }
        });
        return list;
    }

    @Override
    public PageUtils<?> listMsg(SysMsgListVO listVO, Integer operateUid) {
        IPage<SysMsgListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = sysMsgResourceMapper.msgList(iPage, new QueryWrapper<>()
                .eq("smr.user_id", operateUid)
                .eq("sm.is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMsgType()), "sm.msg_type", listVO.getMsgType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMsgIndustry()), "sm.msg_industry", listVO.getMsgIndustry())
                .eq(ObjectUtils.isNotAll(listVO.getIsRead()), "smr.is_read", listVO.getIsRead())
                .orderByDesc("sm.id")
        );
        iPage.getRecords().forEach(u -> {
            u.setMsgTypeName(ICommonEnum.getNameById(u.getMsgType(), MsgTypeEnum.class));
            if (ObjectUtils.isNotNullOrZero(u.getMsgIndustry())) {
                u.setMsgIndustryName(ICommonEnum.getNameById(u.getMsgIndustry(), MsgIndustryEnum.class));
            }
        });
        return new PageUtils<>(iPage);
    }

    @Override
    public void readMsg(SysMsgReadVO readVO, Integer operateUid) {
        SysMsgResource resource = new SysMsgResource();
        resource.setIsRead(MsgIsReadEnum.IS_READ.getId());
        resource.setUpdateUid(operateUid);
        if (readVO.getReadType().equals(MsgReadTypeEnum.READ_MSG_ID.getId()) && CollectionUtils.isEmpty(readVO.getMsgIds())) {
            throw new CustomException("消息ID不能空");
        }
        sysMsgResourceMapper.update(resource, new LambdaQueryWrapper<SysMsgResource>()
                .in(readVO.getReadType().equals(MsgReadTypeEnum.READ_MSG_ID.getId()), SysMsgResource::getMsgId, readVO.getMsgIds())
                .eq(SysMsgResource::getUserId, operateUid)
                .eq(SysMsgResource::getIsRead, 0)
        );
    }
}
