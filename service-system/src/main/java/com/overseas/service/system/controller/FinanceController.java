package com.overseas.service.system.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.sys.finance.FinanceListDTO;
import com.overseas.common.dto.sys.finance.FinanceRechargeListDTO;
import com.overseas.common.dto.sys.finance.UserFinanceGetDTO;
import com.overseas.common.utils.redis.RedisUtils;
import com.overseas.common.vo.common.GetVO;
import com.overseas.common.vo.sys.finance.FinanceListVO;
import com.overseas.common.vo.sys.finance.FinanceRechargeGetVO;
import com.overseas.common.vo.sys.finance.FinanceRechargeListVO;
import com.overseas.service.system.service.FinanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-18 20:36
 */
@Api(value = "财务管理")
@RestController
@RequestMapping("/sys/finance")
@RequiredArgsConstructor
public class FinanceController extends AbstractController {

    private final FinanceService financeService;

    private final RedisUtils redisUtils;

    @PostMapping("/listUserFinance")
    public R listUserFinance(@RequestBody List<Integer> userIds) {
        return R.data(this.financeService.listUserFinance(userIds));
    }

    @ApiOperation(value = "获取用户账户余额", produces = "application/json", response = UserFinanceGetDTO.class)
    @PostMapping("/get")
    public R getUserFinance(@RequestBody @Validated GetVO getVO) {
        return R.data(this.financeService.getUserFinance(getVO, this.getUser()));
    }

    @ApiOperation(value = "获取财务管理列表", produces = "application/json", response = FinanceListDTO.class)
    @PostMapping("/list")
    public R getFinanceList(@RequestBody @Validated FinanceListVO listVO) {
        return R.page(this.financeService.getFinanceList(listVO, this.getUser()));
    }

    @ApiOperation(value = "获取财务管理列表", produces = "application/json", response = FinanceRechargeListDTO.class)
    @PostMapping("/record/list")
    public R getFinanceRechargeList(@RequestBody @Validated FinanceRechargeListVO listVO) {
        return R.page(this.financeService.getFinanceRechargeList(listVO, this.getUser()));
    }

    @ApiOperation(value = "充值", produces = "application/json")
    @PostMapping("/recharge")
    public R rechargeFinance(@RequestBody @Validated FinanceRechargeGetVO getVO) {
        this.financeService.rechargeFinance(getVO, this.getUser());
        return R.ok();
    }

    @ApiOperation(value = "测试预算预警", produces = "application/json")
    @PostMapping("/budget/test")
    public R testBudget(@RequestBody @Validated Map<String, String> map) {
        switch (map.get("type")) {
            case "all":
                this.financeService.checkMasterAllBudget();
                break;
            case "remove":
                this.redisUtils.del(map.get("key"));
                break;
            case "get":
                System.out.println(this.redisUtils.get(map.get("key")));
                return R.data(this.redisUtils.get(map.get("key")));
            case "day":
                this.financeService.checkMasterDayBudget();
                break;
            case "set":
                this.redisUtils.set(map.get("key"), "1");
                break;
        }
        return R.ok();
    }
}
