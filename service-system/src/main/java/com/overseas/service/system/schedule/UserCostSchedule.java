package com.overseas.service.system.schedule;

import com.overseas.service.system.service.FinanceService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Profile({"online", "test2"})
public class UserCostSchedule {

    private final FinanceService financeService;

    @Scheduled(fixedDelay = 60000)
    public void pushConsult() {
        this.financeService.setUserCost();
    }
}
