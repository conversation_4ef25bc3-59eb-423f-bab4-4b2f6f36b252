package com.overseas.service.system.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.sys.msg.SysMsgGetDTO;
import com.overseas.common.dto.sys.msg.SysMsgListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.sys.msg.MsgIndustryEnum;
import com.overseas.common.enums.sys.msg.MsgTypeEnum;
import com.overseas.common.vo.sys.msg.*;
import com.overseas.service.system.service.SysMsgService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 **/
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/sys/msg")
public class SysMsgController extends AbstractController {

    private final SysMsgService sysMsgService;

    @ApiOperation(value = "发送消息")
    @PostMapping("/send")
    public R sendMsg(@RequestBody @Validated SysMsgSendVO sendVO) {
        sysMsgService.sendMsg(sendVO);
        return R.ok();
    }

    @ApiOperation(value = "获取消息", response = SysMsgGetDTO.class)
    @PostMapping("/get")
    public R getMsg(@RequestBody @Validated SysMsgGetVO getVO) {
        return R.data(sysMsgService.getMsg(getVO, this.getUserId()));
    }

    @ApiOperation(value = "根据最新消息id获取消息", response = SysMsgListDTO.class)
    @PostMapping("/list/last")
    public R getLastMsg(@RequestBody @Validated SysMsgLastGetVO lastGetVO) {
        return R.data(sysMsgService.getLastMsg(lastGetVO, this.getUserId()));
    }

    @ApiOperation(value = "获取消息list", response = SysMsgListDTO.class)
    @PostMapping("/list")
    public R listMsg(@RequestBody @Validated SysMsgListVO listVO) {
        return R.page(sysMsgService.listMsg(listVO, this.getUserId()));
    }

    @ApiOperation(value = "读取消息")
    @PostMapping("/read")
    public R readMsg(@RequestBody @Validated SysMsgReadVO readVO) {
        sysMsgService.readMsg(readVO, this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "消息类型")
    @PostMapping("/type/select")
    public R msgType() {
        return R.data(ICommonEnum.list(MsgTypeEnum.class));
    }

    @ApiOperation(value = "消息分类")
    @PostMapping("/industry/select")
    public R msgIndustry(@RequestBody @Validated SysMsgIndustryVO industryVO) {
        return R.data(MsgIndustryEnum.list(industryVO.getMsgType()));
    }


}
