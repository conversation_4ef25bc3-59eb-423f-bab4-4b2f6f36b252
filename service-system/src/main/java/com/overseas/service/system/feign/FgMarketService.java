package com.overseas.service.system.feign;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.sys.customIndex.CustomIndexParentColumnDTO;
import com.overseas.common.utils.FeignR;
import com.overseas.common.vo.market.monitor.action.TrackerActionByProjectSelectVO;
import com.overseas.common.vo.market.monitor.action.TrackerActionGetProjectActionMapVO;
import com.overseas.common.vo.market.reportField.ReportFieldGetVO;
import com.overseas.common.vo.sys.project.ProjectSetActionMapVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient("ai-overseas-service-market")
public interface FgMarketService {

    @PostMapping("/market/masters/select")
    FeignR<List<SelectDTO>> selectPermissionMaster();

    @PostMapping("/market/reportFields/custom/get")
    FeignR<List<CustomIndexParentColumnDTO>> getCustomIndexColumns(ReportFieldGetVO getVO);

    @PostMapping("/market/monitors/action/get/project/map")
    FeignR<List<ProjectSetActionMapVO.ActionMapVO>> getProjectActionMap(TrackerActionGetProjectActionMapVO getProjectActionVO);

    @PostMapping("/market/monitors/action/save/project/map")
    FeignR<?> setProjectActionMap(ProjectSetActionMapVO setActionMapVO);

    @PostMapping("/market/monitors/action/map/by/project")
    FeignR<List<SelectDTO2>> getMapByProject(TrackerActionByProjectSelectVO selectVO);


}
