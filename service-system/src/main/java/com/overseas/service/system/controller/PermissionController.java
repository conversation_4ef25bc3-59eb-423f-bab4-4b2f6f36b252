package com.overseas.service.system.controller;

import com.overseas.service.system.service.PermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-12 10:07
 */
@RestController
@RequestMapping("/sys/permissions")
@RequiredArgsConstructor
public class PermissionController {

    private final PermissionService permissionService;

    @PostMapping("/listByRoleId")
    public List<Integer> getPermissionsByRoleId(@RequestBody Integer roleId) {
        return permissionService.getRolePermissionIds(roleId);
    }

    @PostMapping("/listPermUrlsByRoleId")
    public Set<String> listPermUrlsByRoleId(@RequestBody Integer roleId) {
        return permissionService.listPermissionURLs(roleId);
    }

    @PostMapping("/isNeedInterceptor")
    public boolean isUrlNeedInterceptor(@RequestBody String url) {
        return permissionService.isNeedInterceptor(url);
    }
}
