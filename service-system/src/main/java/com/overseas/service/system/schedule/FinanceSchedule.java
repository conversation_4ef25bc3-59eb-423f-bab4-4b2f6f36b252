package com.overseas.service.system.schedule;

import com.overseas.service.system.service.FinanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online"})
public class FinanceSchedule {

    private final FinanceService financeService;

    /**
     * 每五分钟检查有无账户总消费超过总预算的线额
     */
    @Scheduled(fixedDelay = 300000)
    public void checkMasterAllBudget() {
        this.financeService.checkMasterAllBudget();
    }

    /**
     * 每五分钟检查有无账户日消费超过日预算的线额
     */
//    @Scheduled(fixedDelay = 300000)
    public void checkMasterDayBudget() {
        this.financeService.checkMasterDayBudget();
    }
}
