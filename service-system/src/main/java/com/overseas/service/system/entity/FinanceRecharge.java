package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.Base;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("u_finance_recharge")
@EqualsAndHashCode(callSuper = true)
public class FinanceRecharge extends Base {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long userId;

    private BigDecimal rechargeAmount;

    private BigDecimal amount;
}
