package com.overseas.service.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.vo.LanguageVO;
import com.overseas.common.vo.sys.area.AreaCityListVO;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.common.vo.sys.industry.IndustryListGetVO;
import com.overseas.service.system.entity.Country;
import com.overseas.service.system.service.CountryService;
import com.overseas.service.system.service.IndustryService;
import com.overseas.service.system.vo.area.AreaCitySelectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("系统资源")
@RestController
@RequestMapping("/sys/resource")
@RequiredArgsConstructor
public class ResourcesController {

    private final IndustryService industryService;

    private final CountryService countryService;

    @ApiOperation("广告行业数据（分级）")
    @PostMapping("/industry")
    public R industry() {
        return R.data(this.industryService.industryLinkage());
    }

    @ApiOperation("获取行业数据")
    @PostMapping("/industry/get")
    public R getIndustry(@RequestBody @Validated LanguageVO languageVO) {
        return R.data(this.industryService.getIndustry(languageVO));
    }

    @ApiOperation("根据行业ID获取行业名称")
    @PostMapping("/industry/name/get")
    public R getIndustryName(@RequestBody @Validated IndustryListGetVO getVO) {
        return R.data(this.industryService.getIndustryName(getVO.getIds()));
    }

    @ApiOperation("获取国家下拉数据")
    @PostMapping("/area/countries/select")
    public R selectAreaCountry(@RequestBody @Validated AreaCountrySelectVO selectVO) {
        return R.data(countryService.selectAreaCountry(selectVO));
    }

    @ApiOperation("获取国家下拉数据")
    @PostMapping("/area/city/tree")
    public R areaCityTree(@RequestBody @Validated AreaCountrySelectVO selectVO) {
        return R.data(countryService.areaCityTree(selectVO));
    }

    @ApiOperation("获取国家下拉数据")
    @PostMapping("/area/countries/alias/select")
    public R selectAreaCountryAlias(@RequestBody @Validated AreaCountrySelectVO selectVO) {
        return R.data(countryService.selectAreaCountryAlias(selectVO));
    }

    @ApiOperation("获取国家下拉数据")
    @PostMapping("/countries/select")
    public R selectCountry(@RequestBody @Validated AreaCountrySelectVO selectVO) {
        return R.data(countryService.selectCountry(selectVO));
    }

    @ApiOperation("获取语言下拉数据")
    @PostMapping("/languages/select")
    public R selectLanguages(@RequestBody @Validated AreaCountrySelectVO selectVO) {
        return R.data(countryService.selectLanguages(selectVO));
    }

    @ApiOperation("获取国家下的省市下拉数据")
    @PostMapping("/area/cities/select")
    public R selectAreaCity(@Validated @RequestBody AreaCitySelectVO vo) {
        return R.data(countryService.linkageCity(vo.getCountryId()));
    }

    @ApiOperation("获取城市的列表接口")
    @PostMapping("/area/cities/list")
    public R listCity(@Validated @RequestBody AreaCityListVO vo) {
        return R.data(countryService.list(new LambdaQueryWrapper<Country>().in(Country::getCityId, vo.getCityIds())));
    }

    @ApiOperation(value = "获取时区下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/timeZone/select")
    public R getTimeZoneSelect() {
        return R.data(TimeZoneEnum.list());
    }
}
