package com.overseas.service.system.feign;

import com.overseas.common.dto.report.UserCostDTO;
import com.overseas.common.utils.FeignR;
import com.overseas.common.vo.report.UserCostGetVO;
import com.overseas.common.vo.report.UserCostListVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient("ai-overseas-service-report")
public interface FgReportService {

    @PostMapping("/report/user/cost/get")
    FeignR<UserCostDTO> getUserCost(UserCostGetVO getVO);

    @PostMapping("/report/user/cost/list")
    FeignR<Map<Long,UserCostDTO>> listUserCost(UserCostListVO listVO);
}
