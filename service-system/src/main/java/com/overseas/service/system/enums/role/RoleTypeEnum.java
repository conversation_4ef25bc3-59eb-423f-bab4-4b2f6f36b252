package com.overseas.service.system.enums.role;

/**
 * 角色类型枚举
 */
public enum RoleTypeEnum {
    AGENT(1, "代理商"),
    MANAGER(2, "管理");

    private Integer key;
    private String name;

    RoleTypeEnum(final Integer key, final String name) {
        this.key = key;
        this.name = name;
    }

    /**
     * 获取枚举类型
     *
     * @param id
     * @return
     */
    public static RoleTypeEnum getById(final Integer id) {
        for (RoleTypeEnum userTypeEnum : values()) {
            if (userTypeEnum.getKey() == id) {
                return userTypeEnum;
            }
        }
        return null;
    }

    /**
     * 获取名称
     *
     * @param id
     * @return
     */
    public static String getNameById(final Integer id) {
        RoleTypeEnum roleTypeEnum = RoleTypeEnum.getById(id);
        return null == roleTypeEnum ? null : roleTypeEnum.getName();
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
