package com.overseas.service.system.common.config;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.overseas.common.dto.R;
import com.overseas.common.entity.BaseServer;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.MachineRoomEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.HttpUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.service.system.mapper.BaseServerMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.*;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class AspectConfig {

    private final List<String> mappingList = List.of(
            //"/sys/finance/recharge",
            "/sys/projects/update",
            "/sys/projects/master/set",
            "/sys/projects/action/map/set",
            "/sys/projects/custom/index/set",
            "/sys/auth/password/reset");

    private final BaseServerMapper baseServerMapper;

    @Around(value = "execution(* com.overseas.service.system.controller.AuthController.login(..))")
    public Object afterLogin(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {

        // 1.获取请求参数
        Object[] objects = proceedingJoinPoint.getArgs();
        Map<String, Object> params = ObjectUtils.toMap(objects[0]);
        // 2.获取header
        Map<String, Object> headerMap = this.getHeaderMap();
        // 3.获取当次请求返回结果
        Object response = proceedingJoinPoint.proceed();
        Map<String, Object> responseMap = ObjectUtils.toMap(response);
        // 请求失败，不执行后面步骤
        if (!responseMap.get("code").equals(0)) {
            return response;
        }
        // 3.查看header中是否包含not-send-request，如果包含，则不请求其他机房数据
        if (headerMap.get("not-send-request") != null) {
            return response;
        }
        headerMap.put("not-send-request", 1);
        // 获取其他需要同步的机房数据信息
        String machineRoom = headerMap.getOrDefault("machine-room", MachineRoomEnum.SG.getMachineRoom()).toString();
        List<BaseServer> baseServers = this.baseServerMapper.selectList(new QueryWrapper<BaseServer>().lambda()
                .eq(BaseServer::getIsDel, IsDelEnum.NORMAL.getId())
                .ne(BaseServer::getServerKey, machineRoom));
        Map<String, Object> responseData = ObjectUtils.toMap(responseMap.get("data"));
        // 获取各个机房的token信息
        Map<String, Serializable> tokenMap = new HashMap<>() {{
            put(machineRoom, (Serializable) responseData.get("authToken"));
        }};
        if (CollectionUtils.isNotEmpty(baseServers)) {
            // 针对每个机房都进行请求
            baseServers.forEach(baseServer -> {
                // 获取请求header
                Map<String, Object> headers = new HashMap<>() {{
                    putAll(headerMap);
                }};
                // 每次请求更改cookie，防止服务请求时使用同一个cookie
                headers.put("cookie", "JSESSIONID=" + DigestUtils.md5DigestAsHex(String.valueOf(System.currentTimeMillis()).getBytes()));
                // 请求更新其他机房数据
                Map<String, Object> resMap = JSONObject.parseObject(HttpUtils.post(baseServer.getDomain() + "/sys/auth/login", params, headers));
                if (resMap.get("code").toString().equals("0")) {
                    log.info("请求成功");
                    // 将其他机房的AuthToken填充
                    tokenMap.put(baseServer.getServerKey(), (Serializable) ObjectUtils.toMap(ObjectUtils.toMap(resMap.get("data"))).get("authToken"));
                } else {
                    throw new CustomException("登录失败");
                }
            });
        }
        responseData.put("authTokens", tokenMap);
        responseMap.put("data", responseData);
        return JSONObject.parseObject(JSONObject.toJSONString(responseMap), R.class);
    }

    /**
     * 在新增项目后同步机房数据
     *
     * @param joinPoint 切点
     * @param resParams 当前机房返回数据
     */
    @AfterReturning(value = "execution(* com.overseas.service.system.controller.ProjectController.save(..))", returning = "resParams")
    public void afterAddProject(JoinPoint joinPoint, Object resParams) {

        // 1.获取mapping
        String mapping = this.getMapping();
        // 2.填充请求参数
        Object[] objects = joinPoint.getArgs();
        Map<String, Object> params = ObjectUtils.toMap(objects[0]);
        params.put("id", ObjectUtils.toMap(ObjectUtils.toMap(resParams).get("data")).get("id"));
        // 3.针对每个机房都进行请求
        this.executeOtherServerMapping(mapping, params);
    }

    /**
     * 其他方法同步数据
     *
     * @param joinPoint 切点
     */
    @After("execution(* com.overseas.service.system.controller.FinanceController.rechargeFinance(..))"
            + "|| execution(* com.overseas.service.system.controller.ProjectController.*(..))"
            + "|| execution(* com.overseas.service.system.controller.AuthController.resetPassword(..))")
    public void after(JoinPoint joinPoint) {

        // 1.获取mapping
        String mapping = this.getMapping();
        // 2.如果请求的mapping中不在所设的集合内，则不需要同步更新机房数据
        if (!this.mappingList.contains(mapping)) {
            return;
        }
        // 3.填充请求参数
        Object[] objects = joinPoint.getArgs();
        Map<String, Object> params = ObjectUtils.toMap(objects[0]);
        // 4.针对每个机房都进行请求
        this.executeOtherServerMapping(mapping, params);
    }

    /**
     * 获取Mapping
     *
     * @return 返回mapping
     */
    private String getMapping() {
        return ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes()))
                .getRequest().getAttribute("org.springframework.web.servlet.HandlerMapping.bestMatchingPattern").toString();
    }

    /**
     * 获取当前header
     *
     * @return 返回header
     */
    private Map<String, Object> getHeaderMap() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        HttpServletRequest httpServletRequest = servletRequestAttributes.getRequest();
        Enumeration<String> headerNames = httpServletRequest.getHeaderNames();
        Map<String, Object> headerMap = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headerMap.put(headerName, httpServletRequest.getHeader(headerName));
        }
        return headerMap;
    }

    /**
     * 执行其他机房的请求
     *
     * @param mapping 请求mapping
     * @param params  请求参数
     */
    private void executeOtherServerMapping(String mapping, Map<String, Object> params) {

        // 1.获取header、url
        Map<String, Object> headerMap = this.getHeaderMap();
        Map<String, Object> tokenMap = ObjectUtils.toMap(JSONObject.parseObject(headerMap.get("access-tokens").toString()));
        // 2.查看header中是否包含not-send-request，如果包含，则不额外请求其他机房数据
        if (headerMap.get("not-send-request") != null) {
            return;
        }
        // 3.设置not-send-request字段，防止其他机房循环请求
        headerMap.put("not-send-request", 1);
        // 4.获取其他需要同步的机房数据信息
        List<BaseServer> baseServers = this.baseServerMapper.selectList(new QueryWrapper<BaseServer>().lambda()
                .eq(BaseServer::getIsDel, IsDelEnum.NORMAL.getId())
                .ne(BaseServer::getServerKey, headerMap.getOrDefault("machine-room", MachineRoomEnum.SG.getMachineRoom()).toString()));
        // 5.针对每个机房都进行请求
        baseServers.forEach(baseServer -> {
            // 获取请求header
            Map<String, Object> headers = new HashMap<>() {{
                putAll(headerMap);
                put("access-token", tokenMap.get(baseServer.getServerKey()));
            }};
            // 填充该机房对应token信息
            // 请求更新另一个机房数据
            Map<String, Object> resMap = JSONObject.parseObject(HttpUtils.post(baseServer.getDomain() + mapping, params, headers));
            if (resMap.get("code").toString().equals("0")) {
                log.info("请求成功");
            } else {
                log.error("请求异常，{}", resMap.get("msg"));
            }
        });
    }
}
