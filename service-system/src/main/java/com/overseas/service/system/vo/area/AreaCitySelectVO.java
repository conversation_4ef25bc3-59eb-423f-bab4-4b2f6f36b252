package com.overseas.service.system.vo.area;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-09-16 13:48
 */
@Getter
@Setter
@ToString
@ApiModel("com.overseas.vo.area.AreaCitySelectVO")
public class AreaCitySelectVO {
    @ApiModelProperty("国家ID")
    @NotNull(message = "国家ID不能为空")
    private String countryId;
}
