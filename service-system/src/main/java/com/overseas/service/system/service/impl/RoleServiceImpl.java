package com.overseas.service.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.overseas.common.dto.PageDTO;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.sys.PermissionTreeDTO;
import com.overseas.common.dto.sys.role.RoleGetDTO;
import com.overseas.common.dto.sys.role.RoleListDTO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.GsonUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.service.system.entity.Role;
import com.overseas.service.system.entity.User;
import com.overseas.service.system.enums.PermissionModuleEnum;
import com.overseas.service.system.enums.role.RoleTypeEnum;
import com.overseas.service.system.mapper.RoleMapper;
import com.overseas.service.system.mapper.UserMapper;
import com.overseas.service.system.service.PermissionService;
import com.overseas.service.system.service.RoleService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {
    private final RoleMapper roleMapper;
    private final PermissionService permissionService;
    private final UserMapper userMapper;

    /**
     * 保存角色信息
     *
     * @param role 角色信息
     * @return 保存后的角色
     */
    @Override
    public Role saveRole(Role role) {
        // 检查是否重名
        boolean isNameRepeated = this.isRoleNameRepeated(role.getRoleName(), 0, role.getCreateUid());
        if (isNameRepeated) {
            throw new CustomException("角色名称已存在");
        }
        List<Integer> permissionIds = GsonUtils.fromJson(role.getPermissions(), new TypeToken<List<Integer>>() {
        });
        role.setPermissions(new Gson().toJson(permissionIds));
        this.roleMapper.insert(role);
        return role;
    }

    /**
     * 根据角色id和用户id获取角色信息
     *
     * @param roleId     角色id
     * @param userRoleId 当前登录用户角色id
     * @param agentId    代理商id，超管和内部员工查询时无需查询此字段
     * @return 角色信息
     */
    @Override
    public RoleGetDTO getRole(Integer roleId, Integer userRoleId, Integer agentId) {
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Role::getId, roleId)
                .eq(agentId != null, Role::getCreateUid, agentId);
        Role roleInDb = this.roleMapper.selectOne(queryWrapper);
        if (roleInDb == null) {
            throw new CustomException("无此角色信息");
        }

        RoleGetDTO roleGetDto = new RoleGetDTO();
        BeanUtils.copyProperties(roleInDb, roleGetDto);
        // 获取角色已赋予的权限项
        List<Integer> ownPermissionIds = this.permissionService.getRolePermissionIds(userRoleId);
        List<Integer> rolePermissionIds = GsonUtils.fromJson(roleGetDto.getPermissions(), new TypeToken<List<Integer>>() {
        });
        List<PermissionTreeDTO> permissionTrees = this.permissionService.getPermissionTree(
                roleGetDto.getRoleType(), ownPermissionIds, rolePermissionIds);
        roleGetDto.setPermissionTrees(permissionTrees);
        return roleGetDto;
    }

    /**
     * 获取空的角色权限树
     *
     * @param roleType   角色类型
     * @param userRoleId 当前用户角色id
     * @return 角色权限树信息
     */
    @Override
    public List<PermissionTreeDTO> getRoleTree(Integer roleType, Integer userRoleId) {
        List<Integer> ownPermissionIds = this.permissionService.getRolePermissionIds(userRoleId);
        return this.permissionService.getPermissionTree(roleType, ownPermissionIds, null);
    }

    /**
     * 更新角色信息
     *
     * @param role    角色信息
     * @param agentId 代理商id
     * @return 保存后的角色
     */
    @Override
    public Role updateRole(Role role, Integer agentId) {
        // 检查是否重名
        boolean isNameRepeated = this.isRoleNameRepeated(role.getRoleName(), role.getId(), role.getCreateUid());
        if (isNameRepeated) {
            throw new CustomException("角色名称已存在");
        }
        Role roleInDb = this.baseMapper.selectById(role.getId());
        if (null == roleInDb) {
            throw new CustomException("角色不存在");
        }
        UpdateWrapper<Role> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(Role::getId, role.getId())
                .eq(ObjectUtils.isNotNullOrZero(agentId), Role::getCreateUid, agentId);
        List<Integer> permissionIds = GsonUtils.fromJson(role.getPermissions(), new TypeToken<List<Integer>>() {
        });
        role.setPermissions(new Gson().toJson(permissionIds));
        int updateResult = this.roleMapper.update(role, updateWrapper);

        // 如果是代理商角色，查找使用此角色的代理商创建的所有角色，比对权限项是否一致
        if (RoleTypeEnum.AGENT.getKey().equals(roleInDb.getRoleType())) {
            // 查找角色对应用户创建的所有角色
            List<Role> childrenRoles = this.baseMapper.selectChildrenRole(role.getId());
            for (Role childrenRole : childrenRoles) {
                List<Integer> childPermissionIds = GsonUtils.fromJson(childrenRole.getPermissions(), new TypeToken<List<Integer>>() {
                });
                // 保留子角色中和父角色都有的权限
                childPermissionIds.retainAll(permissionIds);
                childrenRole.setPermissions(new Gson().toJson(childPermissionIds));
                this.baseMapper.updateById(childrenRole);
            }
        }
        if (updateResult == 0) {
            throw new CustomException("更新角色信息异常");
        }
        return role;
    }

    /**
     * 删除角色
     *
     * @param roleId      角色id
     * @param loginUserId 操作用户id
     * @param agentId     代理商id，SMP删除代理商下角色时使用
     * @return 删除结果
     */
    @Override
    public Integer deleteRole(Integer roleId, Integer loginUserId, Integer agentId) {
        // 检查是否被引用，无引用可删除
        Long userCount = this.userMapper.selectCount(
                new QueryWrapper<User>().lambda().eq(User::getRoleId, roleId));
        if (userCount.equals(0L)) {
            Role role = new Role();
            role.setUpdateUid(loginUserId);
            role.setIsDel(IsDelEnum.DELETE.getId());
            UpdateWrapper<Role> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(Role::getId, roleId).eq(agentId != null, Role::getCreateUid, agentId);
            return this.roleMapper.update(role, updateWrapper);
        } else {
            throw new CustomException("角色已绑定用户，不可删除");
        }
    }

    /**
     * 获取角色分页数据
     *
     * @param page     页码
     * @param pageNum  每页记录数
     * @param roleType 角色类型
     * @param search   查询字符串
     * @param agentId  代理商id，SMP平台使用
     * @return 角色分页信息对象
     */
    @Override
    public PageDTO listRole(Long page, Long pageNum, Integer roleType, String search, Integer agentId) {
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Role::getIsDel, IsDelEnum.NORMAL.getId()).eq(Role::getRoleType, roleType)
                .eq(agentId != null, Role::getCreateUid, agentId)
                .and(i -> i.like(search != null, Role::getRoleName, search)
                        .or().like(search != null, Role::getDescription, search))
                .orderByDesc(Role::getId);
        IPage<Role> rolePage = new Page<>(page, pageNum);
        this.roleMapper.selectPage(rolePage, queryWrapper);

        // 转为dto
        List<Role> rolesInDb = rolePage.getRecords();
        List<RoleListDTO> roleDtoList = new ArrayList<>();
        for (Role roleInDb : rolesInDb) {
            RoleListDTO roleDto = new RoleListDTO();
            BeanUtils.copyProperties(roleInDb, roleDto);
            roleDto.setRoleTypeName(RoleTypeEnum.getNameById(roleDto.getRoleType()));
            List<SelectDTO> modules = PermissionModuleEnum.selectModules(
                    JSONObject.parseArray(roleInDb.getPermissions(), Integer.class));
            roleDto.setModules(modules);
            roleDtoList.add(roleDto);
        }
        return new PageDTO<>(rolePage.getTotal(), roleDtoList);
    }

    /**
     * 根据角色类型获取角色下拉数据
     *
     * @param roleType 角色类型
     * @param agentId  代理商id，smp模块调用使用
     * @return 角色列表
     */
    @Override
    public List<SelectDTO> selectRole(Integer roleType, Integer agentId) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Role::getRoleType, roleType)
                .eq(agentId != null, Role::getCreateUid, agentId)
                .eq(Role::getIsDel, IsDelEnum.NORMAL.getId())
                .select(Role::getId, Role::getRoleName)
                .orderByDesc(Role::getId);
        return this.roleMapper.selectRole(queryWrapper);
    }

    /**
     * 判断角色名称是否存在
     *
     * @param roleName 角色名称
     * @param roleId   角色id，编辑时传id，新建时不传
     * @return 是否重名，true：重名，false：不重名
     */
    private boolean isRoleNameRepeated(String roleName, Integer roleId, Integer userId) {
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Role::getRoleName, roleName)
                .eq(ObjectUtils.isNotNullOrZero(userId), Role::getCreateUid, userId)
                .eq(Role::getIsDel, IsDelEnum.NORMAL.getId());
        Role roleInDb = this.roleMapper.selectOne(queryWrapper);
        if (roleInDb == null) {
            return false;
        } else {
            return !roleId.equals(roleInDb.getId());
        }
    }
}
