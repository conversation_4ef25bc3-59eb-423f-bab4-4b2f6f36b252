package com.overseas.service.system.enums;

/**
 * 权限项是否默认枚举
 */
public enum PermissionIsRequiredEnum {
    NOT_REQUIRED(0, "非默认"),
    REQUIRED(1, "默认");

    private Integer key;
    private String name;

    PermissionIsRequiredEnum(final Integer key, final String name) {
        this.key = key;
        this.name = name;
    }

    /**
     * 获取枚举类型
     *
     * @param id
     * @return
     */
    public static PermissionIsRequiredEnum getById(final Integer id) {
        for (PermissionIsRequiredEnum enumValue : values()) {
            if (enumValue.getKey() == id) {
                return enumValue;
            }
        }
        return null;
    }

    /**
     * 获取名称
     *
     * @param id
     * @return
     */
    public static String getNameById(final Integer id) {
        PermissionIsRequiredEnum enumValue = PermissionIsRequiredEnum.getById(id);
        return null == enumValue ? null : enumValue.getName();
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
