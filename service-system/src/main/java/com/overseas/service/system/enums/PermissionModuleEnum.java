package com.overseas.service.system.enums;

import com.overseas.common.dto.SelectDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限模块枚举
 *
 * <AUTHOR>
 */
public enum PermissionModuleEnum {
    //
    OPERATOR(1, "管理平台"),
    MARKET(2, "投放平台"),
    DESIGNER(3, "设计师平台");

    private Integer key;
    private String name;

    PermissionModuleEnum(final Integer key, final String name) {
        this.key = key;
        this.name = name;
    }

    /**
     * 获取枚举类型
     *
     * @param id 筛选条件
     * @return 返回数据
     */
    public static PermissionModuleEnum getById(final Integer id) {
        for (PermissionModuleEnum enumValue : values()) {
            if (enumValue.getKey().equals(id)) {
                return enumValue;
            }
        }
        return null;
    }

    /**
     * 获取名称
     *
     * @param id 筛选条件
     * @return 返回数据
     */
    public static String getNameById(final Integer id) {
        PermissionModuleEnum enumValue = PermissionModuleEnum.getById(id);
        return null == enumValue ? null : enumValue.getName();
    }

    /**
     * 根据不同角色排除的权限ID，获取模块ID集合
     *
     * @param excludeIds 不同角色排除的权限结点
     * @return 模块id集合
     */
    public static List<Integer> getModuleIds(List<Integer> excludeIds) {
        List<Integer> moduleIds = new ArrayList<>();
        for (PermissionModuleEnum enumValue : values()) {
            if (!excludeIds.contains(enumValue.getKey())) {
                moduleIds.add(enumValue.getKey());
            }
        }
        return moduleIds;
    }

    /**
     * 根据角色权限列表，获取开启的模块列表
     *
     * @param permissionIds 角色下权限列表
     * @return 模块列表
     */
    public static List<SelectDTO> selectModules(List<Integer> permissionIds) {
        List<SelectDTO> modules = new ArrayList<>();
        for (PermissionModuleEnum enumValue : values()) {
            if (permissionIds.contains(enumValue.getKey())) {
                modules.add(new SelectDTO(Long.valueOf(enumValue.getKey()), enumValue.getName()));
            }
        }
        return modules;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
