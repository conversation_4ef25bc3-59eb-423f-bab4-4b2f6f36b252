package com.overseas.service.system.controller;

import com.overseas.common.dto.R;
import com.overseas.service.system.service.ConsultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "Consult-咨询相关接口")
@RestController
@RequestMapping("/sys/consult")
@RequiredArgsConstructor
public class ConsultController {

    private final ConsultService consultService;

    @ApiOperation(value = "提交咨询信息")
    @PostMapping("/save")
    public R saveConsult(@RequestParam Map<String, Object> params) {
        this.consultService.saveConsult(params);
        return R.ok();
    }

    @ApiOperation(value = "测试推送咨询信息", produces = "application/json")
    @PostMapping("/test")
    public R saveConsult() throws Exception {
        this.consultService.pushConsult();
        return R.ok();
    }
}
