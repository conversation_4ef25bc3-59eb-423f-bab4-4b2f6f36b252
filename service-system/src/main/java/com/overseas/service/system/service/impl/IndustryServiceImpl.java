package com.overseas.service.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.CascaderDTO;
import com.overseas.common.dto.sys.resource.LinkageDto;
import com.overseas.common.vo.LanguageVO;
import com.overseas.service.system.entity.Industry;
import com.overseas.service.system.entity.IndustryCategory;
import com.overseas.service.system.mapper.IndustryCategoryMapper;
import com.overseas.service.system.mapper.IndustryMapper;
import com.overseas.service.system.service.IndustryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class IndustryServiceImpl extends ServiceImpl<IndustryMapper, Industry> implements IndustryService {

    private final IndustryCategoryMapper industryCategoryMapper;

    @Override
    public List<LinkageDto> industryLinkage() {
        List<Industry> industryList = this.lambdaQuery()
                .select(Industry::getId, Industry::getParentId, Industry::getIndustryName, Industry::getIndustryLevel)
                .orderByAsc(Industry::getParentId)
                .list();
        Map<Integer, LinkageDto> res = new HashMap<>();
        industryList.forEach(ind -> {
            if (ind.getIndustryLevel() == 1) {
                res.put(ind.getId(), new LinkageDto(ind.getId(), ind.getIndustryName(), new ArrayList<LinkageDto>()));
            } else if (res.get(ind.getParentId()) != null) {
                LinkageDto linkage = res.get(ind.getParentId());
                linkage.getChildren().add(new LinkageDto(ind.getId(), ind.getIndustryName(), List.of()));
            }
        });
        return new ArrayList<>(res.values());
    }

    @Override
    public List<CascaderDTO> getIndustry(LanguageVO languageVO) {

        List<IndustryCategory> industryList = this.industryCategoryMapper.selectList(new QueryWrapper<IndustryCategory>().lambda()
                .orderByAsc(IndustryCategory::getParentId)
                .orderByAsc(IndustryCategory::getId));

        Map<Long, CascaderDTO> resMap = new LinkedHashMap<>();
        industryList.forEach(industry -> {
            String label = languageVO.getLanguage().equals("zh") ? industry.getIndustryName() : industry.getIndustryEnName();
            if (industry.getIndustryLevel() == 1) {
                resMap.put(industry.getId(), new CascaderDTO(industry.getId(), label, 1, new ArrayList<>()));
            } else if (resMap.get(industry.getParentId()) != null) {
                resMap.get(industry.getParentId()).getChildren().add(new CascaderDTO(industry.getId(), label, 1, List.of()));
            }
        });
        return new ArrayList<>(resMap.values());
    }

    @Override
    public String getIndustryName(List<Long> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return "";
        }
        return this.industryCategoryMapper.selectList(new QueryWrapper<IndustryCategory>().lambda()
                        .in(IndustryCategory::getId, ids)
                        .orderByAsc(IndustryCategory::getId))
                .stream().map(IndustryCategory::getIndustryName).collect(Collectors.joining("-"));
    }
}
