package com.overseas.service.system.common.filter;

import com.overseas.common.utils.SpringContextUtils;
import com.overseas.service.system.entity.User;
import com.overseas.service.system.service.PermissionService;
import com.overseas.service.system.common.utils.RealmUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.web.filter.PathMatchingFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.util.Set;

@Slf4j
public class URLPathMatchingFilter extends PathMatchingFilter {
    @Autowired
    PermissionService permissionService;

    @Override
    protected boolean onPreHandle(ServletRequest request, ServletResponse response, Object mappedValue) throws Exception {
        if (null == permissionService) {
            permissionService = SpringContextUtils.getContext().getBean(PermissionService.class);
        }
        String requestURI = getPathWithinApplication(request);
        log.info("requestURI:" + requestURI);

        // 如果没有登录直接过登录验证filter
        if (!SecurityUtils.getSubject().isAuthenticated()) {
            return true;
        }

        // 检查是否需要校验，不在校验列表的均放行
        boolean needInterceptor = this.permissionService.isNeedInterceptor(requestURI);
        if (!needInterceptor) {
            return true;
        } else {
            // 未放行的，判断用户权限中是否有此权限
            boolean hasPermission = false;
            User user = RealmUtils.getUser();
            // 超级管理员，直接放行
            Set<String> permissionUrls = this.permissionService.listPermissionURLs(user.getRoleId());
            for (String permissionUrl : permissionUrls) {
                // 这就表示当前用户有这个权限
                if (permissionUrl.equals(requestURI)) {
                    hasPermission = true;
                    break;
                }
            }

            // 有权限，放行
            if (hasPermission)
                return true;
            else {
                // 无操作权限，返回指定信息
                WebUtils.issueRedirect(request, response, "/common/login/unauthorized");
                return false;
            }
        }
    }
}
