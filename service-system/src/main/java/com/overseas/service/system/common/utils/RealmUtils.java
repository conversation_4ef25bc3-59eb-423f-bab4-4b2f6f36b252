package com.overseas.service.system.common.utils;

import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.SpringContextUtils;
import com.overseas.service.system.entity.User;
import com.overseas.service.system.entity.UserResource;
import com.overseas.service.system.service.PermissionService;
import com.overseas.service.system.service.UserResourceService;
import com.overseas.service.system.service.UserService;
import com.overseas.service.system.common.shiro.DatabaseRealm;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.mgt.RealmSecurityManager;
import org.apache.shiro.session.Session;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class RealmUtils {
    @Autowired
    private static PermissionService permissionService;
    @Autowired
    private static UserResourceService userResourceService;

    @Autowired
    private static UserService userService;

    public static SimpleAuthorizationInfo getSimpleAuthorizationInfo(User user) {
        if (null == permissionService) {
            permissionService = SpringContextUtils.getContext().getBean(PermissionService.class);
        }
        if (null == userResourceService) {
            userResourceService = SpringContextUtils.getContext().getBean(UserResourceService.class);
        }
        // 获取角色，用户类型作为角色，接口配合注解使用
        Set<String> roles = new HashSet<>();
        roles.add(user.getUserType().toString());

        // 获取权限项id集合，超级管理员roleId为1，获取所有权限
        List<Integer> permissionIds = permissionService.getRolePermissionIds(user.getRoleId());
        List<String> stringPermissionIds = permissionIds.stream().map(String::valueOf).collect(Collectors.toList());
        // 获取用户下所有资源
        List<UserResource> userResources = userResourceService.listUserResource(user.getId());

        // 加入原始的权限项id，接口配合注解使用
        Set<String> permissions = new HashSet<>(stringPermissionIds);
        // 加入资源，按照 资源类型_资源id 方式组合注册
        for (UserResource userResource : userResources) {
            permissions.add(userResource.getResourceType().toString() + "_" + userResource.getResourceId().toString());
        }

        // 设置授权
        SimpleAuthorizationInfo simpleAuthorizationInfo = new SimpleAuthorizationInfo();
        simpleAuthorizationInfo.setRoles(roles);
        simpleAuthorizationInfo.setStringPermissions(permissions);
        return simpleAuthorizationInfo;
    }

    /**
     * 获取shiro用户
     *
     * @return 返回用户对象cacheAuthenticationInfoIfPossible
     */
    public static User getUser() {
        Subject subject = SecurityUtils.getSubject();
        User user = (User) subject.getPrincipal();
        if (null == user) {
            throw new CustomException(ResultStatusEnum.NO_LOGIN);
        }
        return user;
    }


    /**
     * 清除当前用户权限缓存
     */
    public static void clearCacheAuthorizationInfo() {
        RealmSecurityManager securityManager = (RealmSecurityManager) SecurityUtils.getSecurityManager();
        DatabaseRealm databaseRealm = (DatabaseRealm) securityManager.getRealms().iterator().next();
        databaseRealm.clearCachedAuthorizationInfo(SecurityUtils.getSubject().getPrincipals());
    }

    /**
     * 刷新当前的用户信息
     */
    public static void refreshSubject() {
        if (null == userService) {
            userService = SpringContextUtils.getContext().getBean(UserService.class);
        }
        Subject subject = SecurityUtils.getSubject();
        User user = (User) subject.getPrincipals().asList().get(0);
        System.out.println(user);
        User newUser = userService.getById(user.getId());
        PrincipalCollection principalCollection = subject.getPrincipals();
        String realName = principalCollection.getRealmNames().iterator().next();
        PrincipalCollection newPrincipalCollection = new SimplePrincipalCollection(newUser, realName);
        subject.runAs(newPrincipalCollection);
    }

    /**
     * 踢掉登录账户
     */
    public static void stopUserLoginSession(Integer userId) {
        // 遍历得到该账户的session
        SessionDAO sessionDAO = ((DefaultWebSessionManager) ((DefaultWebSecurityManager) SecurityUtils.getSecurityManager())
                .getSessionManager()).getSessionDAO();
        Collection<Session> list = sessionDAO.getActiveSessions();
        // 循环遍历list，得到当前登录用户的其他session
        for (Session session : list) {
            Subject s = new Subject.Builder().session(session).buildSubject();
            if (s.isAuthenticated()) {
                User user = (User) s.getPrincipal();
                if (user.getId().equals(userId)) {
                    sessionDAO.delete(session);
                }
            }
        }
    }
}
