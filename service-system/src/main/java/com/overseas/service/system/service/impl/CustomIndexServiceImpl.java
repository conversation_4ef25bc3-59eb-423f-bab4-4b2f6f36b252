package com.overseas.service.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.sys.customIndex.*;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.HumpLineUtils;
import com.overseas.common.vo.market.reportField.ReportFieldGetVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexSaveVO;
import com.overseas.service.system.entity.CustomIndex;
import com.overseas.service.system.entity.User;
import com.overseas.service.system.entity.UserCustomIndex;
import com.overseas.service.system.feign.FgMarketService;
import com.overseas.service.system.mapper.CustomIndexMapper;
import com.overseas.service.system.mapper.UserCustomIndexMapper;
import com.overseas.service.system.service.CustomIndexService;
import com.overseas.service.system.service.ProjectService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CustomIndexServiceImpl extends ServiceImpl<UserCustomIndexMapper, UserCustomIndex> implements CustomIndexService {

    private final CustomIndexMapper customIndexMapper;

    private final ProjectService projectService;

    private final FgMarketService fgMarketService;

    private final SheinConfiguration sheinConfiguration;

    /**
     * 获取完整的自定义指标数据
     *
     * @param module 模块标识
     * @return 自定义指标全部数据
     */
    private CustomIndex getCustomIndex(String module, String project) throws CustomException {
        String moduleProject = StringUtils.isNotBlank(project) ? String.format("%s_%s", module, project) : module;
        CustomIndex customIndex = this.customIndexMapper.selectOne(new QueryWrapper<CustomIndex>()
                .eq("module", moduleProject));
        if (null == customIndex) {
            //如果无数据，获取默认module数据
            customIndex = this.customIndexMapper.selectOne(new QueryWrapper<CustomIndex>()
                    .eq("module", module));
            if (null == customIndex) {
                throw new CustomException("模块标识不合法，请检查后重试");
            }
        }
        return customIndex;
    }

    @Override
    public CustomIndexDTO getCustomIndex(CustomIndexGetVO getVO, User user) {
        //获取当前identify数据
        String identify = getVO.getIdentify();
        if (StringUtils.isBlank(identify)) {
            identify = projectService.projectByMasterIds(getVO.getMasterIds(), getVO.getProjectId());
        }
        // 第一步，取当前模块的总数据
        List<CustomIndexParentColumnDTO> columns = combineColumns(getVO.getModule(), identify, user);
        // 如果查询了项目ID
        if ("report_revenue_list".equals(getVO.getModule())) {
            ReportFieldGetVO reportFieldGetVO = new ReportFieldGetVO();
            reportFieldGetVO.setProjectId(getVO.getProjectId());
            columns.addAll(this.fgMarketService.getCustomIndexColumns(reportFieldGetVO).getData());
        }
        // 第二步，取当前用户自定义的排序规则
//        List<CustomIndexDraggableDTO> userCustomIndex = this.customIndexByUser(userId, getVO.getModule(), identify);
        // 第三步，组装数据
        CustomIndexDTO result = new CustomIndexDTO();
        List<CustomIndexDraggableDTO> draggableList = new ArrayList<>();
        result.setDataColumn(columns);
        result.setDraggableList(draggableList);
        return result;
    }

    @Override
    public String getCustomIndexReportSelect(CustomIndexGetVO getVO, User user) {
        return getCustomIndexReport(getVO, user).stream().map(CustomIndexChildColumnDTO::getRule).collect(Collectors.joining(","));
    }

    private List<String> getCustomIndexRules(CustomIndexGetVO getVO, User user) {
        return getCustomIndexReport(getVO, user).stream().map(CustomIndexChildColumnDTO::getRule).collect(Collectors.toList());
    }

    @Override
    public List<CustomIndexChildColumnDTO> getCustomIndexReport(CustomIndexGetVO getVO, User user) {
        if (StringUtils.isBlank(getVO.getModule())) {
            return List.of();
        }
        List<CustomIndexParentColumnDTO> columns = this.getCustomIndex(getVO, user).getDataColumn();
        if (columns.isEmpty()) {
            return List.of();
        }
        return columns.stream().filter(u -> !u.getKey().contains("info"))
                .flatMap(u -> u.getColumns().stream())
                .collect(Collectors.toList());
    }


    @Override
    public List<CustomIndexParentColumnDTO> getCustomIndexReportDownload(CustomIndexGetVO getVO, User user) {
        if (StringUtils.isBlank(getVO.getModule())) {
            return List.of();
        }
        List<CustomIndexParentColumnDTO> columns = this.getCustomIndex(getVO, user).getDataColumn();
        if (columns.isEmpty()) {
            return List.of();
        }
        return columns;
    }

    @Override
    public void save(CustomIndexSaveVO saveVO, Integer userId) {
        this.getCustomIndex(saveVO.getModule(), saveVO.getIdentify());
        LambdaUpdateWrapper<UserCustomIndex> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserCustomIndex::getUserId, userId).eq(UserCustomIndex::getModule, saveVO.getModule());
        UserCustomIndex userCustomIndex = new UserCustomIndex();
        userCustomIndex.setUserId(userId.longValue());
        userCustomIndex.setIdentify(Optional.ofNullable(saveVO.getIdentify()).orElse(""));
        userCustomIndex.setModule(saveVO.getModule());
        userCustomIndex.setContent(new Gson().toJson(saveVO.getDraggableList()));
        this.saveOrUpdate(userCustomIndex, new QueryWrapper<UserCustomIndex>().lambda()
                .eq(UserCustomIndex::getUserId, userId).eq(UserCustomIndex::getModule, saveVO.getModule()));
    }

    @Override
    public List<CustomIndexDraggableDTO> customIndexByUser(Integer userId, String module, String identify) {
        UserCustomIndex userCustomIndex = this.baseMapper.selectOne(new QueryWrapper<UserCustomIndex>().lambda()
                .eq(UserCustomIndex::getUserId, userId)
                .eq(UserCustomIndex::getModule, module)
                .eq(UserCustomIndex::getIdentify, identify)
        );
        if (null == userCustomIndex) {
            return null;
        }
        return new Gson().fromJson(userCustomIndex.getContent(), new TypeToken<List<CustomIndexDraggableDTO>>() {
        }.getType());
    }

    @Override
    public List<CustomIndexContentDTO> getCustomIndexByModule(CustomIndexGetVO getVO, User user) {
        //获取当前identify数据
        String identify = getVO.getIdentify();
        if (StringUtils.isBlank(identify)) {
            identify = projectService.projectByMasterIds(getVO.getMasterIds(), getVO.getProjectId());
        }
        // 第一步，取当前模块的总体数据
        return combineColumns(getVO.getModule(), identify, user)
                .stream().flatMap(u -> u.getColumns().stream()).map(u -> {
                    CustomIndexContentDTO customIndexContentDTO = new CustomIndexContentDTO();
                    customIndexContentDTO.setKey(u.getKey());
                    customIndexContentDTO.setRule(u.getRule());
                    customIndexContentDTO.setTitle(u.getTitle());
                    customIndexContentDTO.setInsertRule(u.getInsertRule());
                    return customIndexContentDTO;
                }).collect(Collectors.toList());
    }

    @Override
    public List<CustomIndexFieldDTO> getCustomIndexFieldMap(CustomIndexGetVO getVO, User user) {
        List<String> fieldRules = this.getCustomIndexRules(getVO, user);
        if (CollectionUtils.isEmpty(fieldRules)) {
            return List.of();
        }
        return fieldRules.stream().map(rule -> {
            CustomIndexFieldDTO customIndexFieldDTO = new CustomIndexFieldDTO();
            String[] ruleFields = rule.replaceAll("`", "").split("AS");
            customIndexFieldDTO.setRule(ruleFields[0].trim());
            if (StringUtils.countMatches(rule, "SUM") < 2) {
                // 获取对应的field
                Pattern pattern = Pattern.compile("SUM\\([a-z0-9_]*\\)");
                Matcher matcher = pattern.matcher(ruleFields[0]);
                while (matcher.find()) {
                    customIndexFieldDTO.setField(matcher.group().replace("SUM(", "").replace(")", ""));
                }
            } else {
                customIndexFieldDTO.setField(customIndexFieldDTO.getRule());
            }
            customIndexFieldDTO.setKey(HumpLineUtils.lineToHump(ruleFields[1]).trim());
            return customIndexFieldDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CustomIndexFieldDTO> getCustomIndexFieldMapV2(CustomIndexGetVO getVO, User user) {
        return this.getCustomIndex(getVO, user).getDataColumn().stream()
                .flatMap(action ->
                        action.getColumns().stream().map(u -> {
                            CustomIndexFieldDTO fieldDTO = new CustomIndexFieldDTO();
                            fieldDTO.setKey(u.getKey());
                            fieldDTO.setField(u.getTitle());
                            fieldDTO.setRule(u.getRule());
                            fieldDTO.setActionKey(action.getKey());
                            return fieldDTO;
                        })
                ).collect(Collectors.toList());
    }

    /**
     * 合并指标数据
     *
     * @param identify 指标数据
     * @param module   模块
     * @return 返回指标数据
     */
    private List<CustomIndexParentColumnDTO> combineColumns(String module, String identify, User user) {
        CustomIndex customIndex = this.getCustomIndex(module, identify);

        Map<String, CustomIndexParentColumnDTO> result = new HashMap<>();
        //如果是含有项目ID，则进行项目新报表数据获取
        if (StringUtils.isNotBlank(identify) && customIndex.getModule().equals(module)) {
            customIndex.setBaseModule(customIndex.getBaseModule().replace("report_old_base", "report_base"));
        }
        List<String> baseModules = new ArrayList<>();
        if (StringUtils.isNotBlank(customIndex.getBaseModule())) {
            baseModules = new Gson().fromJson(customIndex.getBaseModule(), new TypeToken<List<String>>() {
            }.getType());
        }
        String reportIdentify = String.format("report_%s", identify);
        if (customIndex.getBaseModule().contains("report_base") && !baseModules.contains(reportIdentify)) {
            baseModules.add(reportIdentify);
        }
        Map<String, CustomIndex> customIndexMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(baseModules)) {
            customIndexMap = customIndexMapper.selectList(new QueryWrapper<CustomIndex>().lambda()
                            .in(CustomIndex::getModule, baseModules))
                    .stream().collect(Collectors.toMap(CustomIndex::getModule, Function.identity()));
        }
        for (String baseModule : baseModules) {
            if (customIndexMap.containsKey(baseModule)) {
                combineContent(customIndexMap.get(baseModule).getContent(), result);
            }
        }
        combineContent(customIndex.getContent(), result);
        if (null != user && sheinConfiguration.isRole(user.getRoleId())) {
            result.remove("video");
        }

        //判定是否特殊角色，排除指定字段
        List<String> excludeKeys;
        // shein客户登录排除指定字段
        if (null != user && sheinConfiguration.isRole(user.getRoleId())) {
            // shein客户要从公共指标中删除的指标项
            excludeKeys = List.of("win", "winRate", "userView", "userClick", "masterSurplus", "nextBudgetDay",
                    "actionD5", "actionD6", // 曝光唤端率，曝光首唤率
                    // cpc、点击、点击率、会话去重点击、会话去重点击率、平台花费、预估收益都屏蔽，使用最新配置的规则
                    "cpm", "cpc", "click", "clickRate", "clickUniqSession", "actionD7",
                    "masterCost", "mediaCost", "settlement", "consumerCost",
                    "action56", "action57", "action58",  // 竞价加购，竞价下单，竞价购买,
                    "action59", "action60", // 互动，竞价互动
                    "action61", // 竞价实时购买
                    "action62", "action63", "action64", "action65",     // 预留字段
                    "actionD21", // 点击再互动率
                    "actionD22", // 实时花费
                    "actionD23", "actionD24", "action25", // 实时唤端成本，实时购买成本，实时唤端率
                    "actionD26", "actionD27", "actionD28", "actionD29", // 竞价加购成本，竞价下单成本，竞价购买成本，点击购买率
                    "actionD30", "actionD31", "actionD32", "actionD33", // 竞价互动成本，实时曝光唤端率，实时ROI，预估ROI
                    "actionD34", "actionD35"  // 实时购买金额，竞价实时购买金额
            );
        } else if (identify.equals("shein_project")) {
            // 内部运营排除配置给shein运营的媒体花费，点击，点击率，CPC，花费字段
            excludeKeys = List.of("actionD11", "action50", "actionD13", "actionD14", "actionD15");
        } else {
            // 不做处理
            excludeKeys = List.of();
        }
        return result.values().stream()
                .peek(
                        u -> u.setColumns(
                                u.getColumns().stream().filter(col -> !excludeKeys.contains(col.getKey()))
                                        .collect(Collectors.toList())
                        )
                )
                .sorted(Comparator.comparing(CustomIndexParentColumnDTO::getSort)).collect(Collectors.toList());
    }

    /**
     * 合并数据
     *
     * @param content 内容
     * @param result  结果
     */
    private void combineContent(String content, Map<String, CustomIndexParentColumnDTO> result) {
        if (StringUtils.isNotBlank(content)) {
            List<CustomIndexParentColumnDTO> customs = new Gson().fromJson(content, new TypeToken<List<CustomIndexParentColumnDTO>>() {
            }.getType());
            if (null != customs) {
                customs.forEach(custom -> {
                    if (result.containsKey(custom.getKey()) && CollectionUtils.isNotEmpty(custom.getColumns())) {
                        result.get(custom.getKey()).getColumns().addAll(custom.getColumns());
                    } else {
                        result.put(custom.getKey(), custom);
                    }
                });
            }
        }
    }
}
