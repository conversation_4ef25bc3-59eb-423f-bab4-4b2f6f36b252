package com.overseas.service.system.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.sys.user.UserSelectByIdDTO;
import com.overseas.common.dto.sys.user.UserMailDTO;
import com.overseas.common.exception.CustomException;
import com.overseas.common.vo.market.market.MarketMasterGetVO;
import com.overseas.common.vo.market.master.MasterBindVO;
import com.overseas.common.vo.sys.user.UserCheckGetVO;
import com.overseas.common.vo.sys.user.UserMailGetVO;
import com.overseas.common.vo.sys.user.UserSelectByIdVO;
import com.overseas.common.vo.sys.user.UserSelectGetVO;
import com.overseas.service.system.entity.User;
import com.overseas.service.system.entity.UserResource;
import com.overseas.service.system.service.UserResourceService;
import com.overseas.service.system.service.UserService;
import com.overseas.service.system.enums.user.UserTypeEnum;
import com.overseas.service.system.enums.user.resource.UserResourceTypeEnum;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Collectors;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-11 18:31
 */
@RestController
@RequestMapping("/sys/users")
@RequiredArgsConstructor
public class UserController extends AbstractController {

    private final UserService userService;

    private final UserResourceService userResourceService;

    @PostMapping("/test")
    public R test() {
        return R.ok();
    }

    @PostMapping("/saveMasterBaseInfo")
    public R saveUser(@RequestBody User user) {
        // 校验名称是否重复
        User user1 = this.userService.checkUserName(user.getCompanyName());
        if (user1 != null) {
            throw new CustomException("账户名称重复，请确认后再试");
        }
        // 保存基本信息
        user.setUserType(UserTypeEnum.MASTER.getId());
        this.userService.save(user);
        // 保存关系信息
        UserResource userResourceSave = new UserResource();
        userResourceSave.setCreateUid(user.getParentId());
        userResourceSave.setUserId(user.getParentId());
        userResourceSave.setResourceType(UserResourceTypeEnum.MASTER.getId());
        userResourceSave.setResourceId(user.getId());
        userResourceService.save(userResourceSave);

        return R.data(user.getId());
    }

    @PostMapping("/getMasterBaseInfo")
    public R getMaster(@RequestBody Integer userId) {
        return R.data(this.userService.getById(userId));
    }

    @PostMapping("/getUserBaseInfo")
    public R getUserBaseInfo(@RequestBody Integer userId) {
        return R.data(this.userService.getById(userId));
    }

    @PostMapping("/getManagerInfoByEmail")
    public R getManagerInfoByEmail(@RequestBody String email) {
        return R.data(this.userService.getManagerInfoByEmail(email));
    }

    @PostMapping("/bindManager")
    public R bindManager(@RequestBody MasterBindVO masterBindVO) {
        this.userService.bindManager(masterBindVO, getUser());
        return R.ok();
    }

    @PostMapping("/listMasterIdByAgent")
    public R listMasterId(@RequestBody Integer agentId) {
        return R.data(this.userService.listMasterIdByAgentId(agentId));
    }

    @ApiOperation(value = "获取全部财务管理员下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/all/financeManager/select")
    public R getAllFinanceManagerSelect() {
        return R.data(this.userService.getAllFinanceManagerSelect());
    }

    @ApiOperation(value = "通过邮箱获取账户信息", notes = "通过邮箱获取账户信息", produces = "application/json", response = UserMailDTO.class)
    @PostMapping("/byMail/get")
    public R getMangerByMail(@Validated @RequestBody UserMailGetVO getVO) {
        return R.data(this.userService.getMangerByMail(getVO));
    }

    @ApiOperation(value = "获取投放账户关联的管理账户", produces = "application/json", response = User.class)
    @PostMapping("/managers/get")
    public R getManagersByMaster(@Validated @RequestBody MarketMasterGetVO getVO) {
        return R.data(this.userService.getManagersByMaster(getVO));
    }

    @ApiOperation(value = "获取投放账户关联的管理账户", produces = "application/json", response = User.class)
    @PostMapping("/managers/get/select")
    public R getManagersSelectByMaster(@Validated @RequestBody MarketMasterGetVO getVO) {
        return R.data(this.userService.getManagersByMaster(getVO)
                .stream().map(u -> new SelectDTO(u.getId().longValue(), u.getRealName())).collect(Collectors.toList())
        );
    }

    @ApiOperation(value = "根据账号类型获取账号下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select")
    public R selectUser(@Validated @RequestBody UserSelectGetVO getVO) {
        return R.data(this.userService.selectUser(getVO));
    }

    @ApiOperation(value = "根据账户ID获取账户信息", produces = "application/json", response = UserSelectByIdDTO.class)
    @PostMapping("/select/by/id")
    public R selectUserByIds(@Validated @RequestBody UserSelectByIdVO byIdVO) {
        return R.data(this.userService.selectUserById(byIdVO));
    }

    @ApiOperation(value = "校验名称是否重复")
    @PostMapping("/check")
    public R checkUserIsExist(@Validated @RequestBody UserCheckGetVO getVO) {
        return R.data(this.userService.checkUserIsExist(getVO));
    }

    @ApiOperation(value = "更新用户信息")
    @PostMapping("/update")
    public R updateUser(@Validated @RequestBody User user) {
        this.userService.updateUser(user);
        return R.ok();
    }
}
