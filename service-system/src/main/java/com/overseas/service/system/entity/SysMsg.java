package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.Base;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@TableName("sys_msg")
public class SysMsg extends Base {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer msgType;

    private Integer msgIndustry;

    private String msgTitle;

    private String msgLine;

    private Integer isDel;
}
