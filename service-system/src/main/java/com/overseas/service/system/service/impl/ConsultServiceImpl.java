package com.overseas.service.system.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.sys.consult.ConsultExcelDTO;
import com.overseas.common.utils.DateUtils;
import com.overseas.service.system.entity.Consult;
import com.overseas.service.system.enums.consult.ConsultStatusEnum;
import com.overseas.service.system.mapper.ConsultMapper;
import com.overseas.service.system.service.ConsultService;
import com.overseas.service.system.service.MailService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.InputStreamSource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ConsultServiceImpl extends ServiceImpl<ConsultMapper, Consult> implements ConsultService {

    private final MailService mailService;

    private void sendEmail(List<Consult> consultList) throws Exception {
        String subject = "【海外DSP】咨询数据_" + DateUtils.format(new Date());
        String content = "<p>Hi:</p>" +
                "<p>" + DateUtils.format(new Date()) + "日咨询数据已推送，请查收</p>" +
                "<div><table style='border:1px solid black;'><thead>" +
                "<th style='border:1px solid black;'>称呼</th>" +
                "<th style='border:1px solid black;'>企业名称</th>" +
                "<th style='border:1px solid black;'>联系邮箱</th>" +
                "<th style='border:1px solid black;'>申请时间</th>" +
                "</thead><tbody>";
        List<String> consults = new ArrayList<>();
        consultList.forEach(consult -> {
            consults.add(
                    "<tr><td style='border:1px solid black;'>" + consult.getName() + "</td>" +
                    "<td style='border:1px solid black;'>" + consult.getCompany() + "</td>" +
                    "<td style='border:1px solid black;'>" + consult.getMail() + "</td>" +
                    "<td style='border:1px solid black;'>"
                            + DateUtils.format(consult.getCreateTime(), DateUtils.DATE_TIME_PATTERN)
                            + "</td></tr>"
            );
        });
        content += StringUtils.join(consults, "");
        content  += "</tbody></table></div>";
        this.mailService.sendEmail("<EMAIL>;<EMAIL>", subject, content, "", null);
    }

    @Override
    public void saveConsult(Map<String, Object> params) {
        Consult consult = new Consult();
        consult.setName(params.get("name").toString());
        consult.setCompany(params.get("company").toString());
        consult.setPhone(params.get("phone").toString());
        consult.setMail(params.get("mail").toString());
        this.baseMapper.insert(consult);
    }

    @Override
    public void pushConsult() throws Exception {

        List<Consult> consultList = this.baseMapper.selectList(
                new QueryWrapper<Consult>().lambda().eq(Consult::getStatus, ConsultStatusEnum.NOT_PUSH.getId())
        );
        HashMap<String, Consult> consults = new HashMap<>();
        consultList.forEach(consult -> {
            String key = consult.getName() + consult.getCompany() + consult.getMail();
            consults.put(key, consult);
        });
        if (consultList.isEmpty()) {
            return;
        }
        // 如果存在未推送的咨询信息，发送邮件并更新推送状态
        this.sendEmail(new ArrayList<>(consults.values()));
        List<Long> consultIds = consultList.stream().map(Consult::getId).collect(Collectors.toList());
        Consult consult = new Consult();
        consult.setStatus(ConsultStatusEnum.PUSHED.getId());
        this.baseMapper.update(consult, new QueryWrapper<Consult>().lambda().in(Consult::getId, consultIds));
    }
}
