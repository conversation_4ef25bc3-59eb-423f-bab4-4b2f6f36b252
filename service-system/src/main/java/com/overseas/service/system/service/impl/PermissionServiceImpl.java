package com.overseas.service.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.reflect.TypeToken;
import com.overseas.common.dto.sys.MenuDTO;
import com.overseas.common.dto.sys.ModuleDTO;
import com.overseas.common.dto.sys.PermissionDTO;
import com.overseas.common.dto.sys.PermissionTreeDTO;
import com.overseas.common.utils.GsonUtils;
import com.overseas.service.system.entity.Permission;
import com.overseas.service.system.entity.Role;
import com.overseas.service.system.enums.*;
import com.overseas.service.system.enums.role.RoleTypeEnum;
import com.overseas.service.system.mapper.PermissionMapper;
import com.overseas.service.system.mapper.RoleMapper;
import com.overseas.service.system.service.PermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, Permission> implements PermissionService {

    private final PermissionMapper permissionMapper;

    private final RoleMapper roleMapper;

    // 代理商排除的权限项
    @Value("${local.permission.agent}")
    private String agentExclude;

    // 广告主排除的权限项
    @Value("${local.permission.manager}")
    private String masterExclude;

    /**
     * 获取所有接口权限项集合
     *
     * @return 权限项集合
     */
    @Override
    public List<Permission> listAllApi() {
        QueryWrapper<Permission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_type", PermissionTypeEnum.API_BUTTON.getKey())
                .select("id", "pid", "permission_url");
        return this.permissionMapper.selectList(queryWrapper);
    }

    /**
     * 根据用户名获取所有权限项URL集合
     *
     * @param roleId 角色id
     * @return 权限项链接集合
     */
    @Override
    public Set<String> listPermissionURLs(Integer roleId) {
        List<Integer> permissionIds = this.getRolePermissionIds(roleId);
        List<Permission> permissions = this.listPermissionByIds(permissionIds);
        Set<String> urls = new HashSet<>();
        for (Permission permission : permissions) {
            String[] permissionUrls = permission.getPermissionUrl().split(",");
            urls.addAll(Stream.of(permissionUrls).collect(Collectors.toSet()));
        }
        return urls;
    }

    /**
     * 获取角色下权限id数组
     *
     * @param roleId 角色id，为空则获取所有权限id集合
     * @return 权限项id集合
     */
    @Override
    public List<Integer> getRolePermissionIds(Integer roleId) {
        Role role = this.roleMapper.selectById(roleId);
        if (null == role) {
            return Collections.singletonList(0);
        } else {
            return GsonUtils.fromJson(role.getPermissions(), new TypeToken<List<Integer>>() {
            });
        }
    }

    /**
     * 根据请求路径判断是否需要过滤
     *
     * @param requestURI 接口uri
     * @return 是否需要过滤，true：需要，false：不需要
     */
    @Override
    public boolean isNeedInterceptor(String requestURI) {
        List<Permission> permissions = this.listAllApi();
        for (Permission permission : permissions) {
            String[] permissionUrls = permission.getPermissionUrl().split(",");
            if (Arrays.asList(permissionUrls).contains(requestURI)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取权限项树
     *
     * @param roleType         角色类型
     * @param ownPermissionIds 自己拥有的权限项id集合
     * @param selectedIds      已分配的权限集合
     * @return 权限树结构数据
     */
    @Override
    public List<PermissionTreeDTO> getPermissionTree(Integer roleType, List<Integer> ownPermissionIds,
                                                     List<Integer> selectedIds) {
        // 根据角色类型获取配置中排除的权限项结点
        List<Integer> excludeIds = this.getExcludePermission(roleType);
        // 获取角色下模块id和拥有的权限项合并，并记录原始的权限列表
        List<Integer> moduleIds = PermissionModuleEnum.getModuleIds(excludeIds);
        List<Integer> originalSelectedIds = selectedIds == null ? null : new ArrayList<>(selectedIds);
        selectedIds = selectedIds == null ? new ArrayList<>() : selectedIds;

        QueryWrapper<Permission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_status", PermissionStatusEnum.NORMAL.getKey())
                .notIn(excludeIds.size() > 0, "id", excludeIds)
                .in(ownPermissionIds.size() > 0, "id", ownPermissionIds);

//        // 代理商广告主类型只获取可分配下去的
//        if (roleType.equals(RoleTypeEnum.AGENT_EMPLOYEE.getKey()) || roleType.equals(RoleTypeEnum.MASTER.getKey())) {
//            queryWrapper.eq("is_allot", PermissionIsAllotEnum.ALLOT.getKey());
//        }
        queryWrapper.orderByAsc("permission_level,sort");
        List<Permission> permissions = this.permissionMapper.selectList(queryWrapper);

        // 组装权限树
        List<PermissionTreeDTO> permissionTrees = new ArrayList<>();
        for (Permission permission : permissions) {
            // pid为0的，为树根
            if (permission.getPid().equals(0)) {
                PermissionTreeDTO permissionTree = new PermissionTreeDTO();
                // 判断是否是角色中关闭的模块
                if (originalSelectedIds == null) {
                    // 新建角色，默认模块开启
                    permissionTree.setModuleStatus(1);
                } else if (moduleIds.contains(permission.getId()) && originalSelectedIds.contains(permission.getId())) {
                    permissionTree.setModuleStatus(1);
                } else {
                    permissionTree.setModuleStatus(0);
                }
                List<PermissionDTO> data = new ArrayList<>();
                this.getTreeChildren(data, permission.getId(), permissions, selectedIds);
                permissionTree.setId(permission.getId());
                permissionTree.setName(permission.getPermissionName());
                permissionTree.setData(data);
                permissionTrees.add(permissionTree);
            }
        }
        return permissionTrees;
    }

    /**
     * 根据模块id和当前登录用户角色获取对应模块的菜单
     *
     * @param moduleId   模块id
     * @param userRoleId 角色id
     * @return 模块对应菜单信息
     */
    @Override
    public List<MenuDTO> getMenus(Integer moduleId, Integer userRoleId) {
        // 获取拥有的权限
        List<Integer> ownPermissionIds = this.getRolePermissionIds(userRoleId);
        List<MenuDTO> menus = new ArrayList<>();
        if (ownPermissionIds.size() > 0) {
            QueryWrapper<Permission> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("module", List.of(1, 2, 3))
                    .eq("permission_type", PermissionTypeEnum.MENU.getKey())
                    .eq("permission_status", PermissionStatusEnum.NORMAL.getKey())
                    .in("id", ownPermissionIds)
                    .select("id", "pid", "permission_name", "permission_url", "permission_icon")
                    .orderByAsc("permission_level,sort");
            List<Permission> permissionsInDb = this.permissionMapper.selectList(queryWrapper);
            menus = this.getMenuChildren(moduleId, permissionsInDb);
        }
        return menus;
    }

    @Override
    public String getFirstMenu(Integer moduleId, Integer userRoleId) {
        List<MenuDTO> menus = this.getMenus(moduleId, userRoleId);
        if (menus != null) {
            while (menus.size() > 0) {
                if (menus.get(0).getChildren() != null) {
                    menus = menus.get(0).getChildren();
                } else {
                    return menus.get(0).getMenuUrl();
                }
            }
        }
        return "";
    }

    /**
     * 获取某个权限下不同类型的权限列表
     *
     * @param permissionId 指定权限id
     * @param userRoleId   用户角色id
     * @return 不同类型权限列表
     */
    @Override
    public Map<String, List<Integer>> getPermissions(Integer permissionId, Integer userRoleId) {
        // 获取拥有的权限
        List<Integer> ownPermissionIds = this.getRolePermissionIds(userRoleId);
        List<Permission> permissionsInDb = this.listPermissionByIds(ownPermissionIds);
        List<Integer> pages = new ArrayList<>();
        List<Integer> apiButtons = new ArrayList<>();
        List<Integer> fields = new ArrayList<>();
        List<Integer> pageModules = new ArrayList<>();
        List<Integer> menus = new ArrayList<>();
        //根据数据模块给全量权限
        permissionsInDb
                .stream().filter(u -> u.getPermissionType().equals(1))
                .forEach(u ->
                        this.getPermissionChildren(u.getId(), permissionsInDb, pages, apiButtons, fields, pageModules, menus)
                );
        Map<String, List<Integer>> result = new HashMap<>();
        result.put("pages", pages);
        result.put("apiButtons", apiButtons);
        result.put("fields", fields);
        result.put("pageModules", pageModules);
        result.put("menus", menus);
        return result;
    }

    /**
     * 获取多个模块信息
     *
     * @param moduleIds 分配的模块id集合
     * @return 多模块集合
     */
    @Override
    public List<ModuleDTO> getModules(List<Integer> moduleIds) {
        QueryWrapper<Permission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_type", PermissionTypeEnum.MODULE.getKey())
                .eq("permission_status", PermissionStatusEnum.NORMAL.getKey())
                .in("id", moduleIds).eq("permission_level", 0)
                .select("id,permission_name,permission_url").orderByAsc("sort");
        List<Permission> permissionModules = this.permissionMapper.selectList(queryWrapper);
        List<ModuleDTO> modules = new ArrayList<>();
        for (Permission permissionModule : permissionModules) {
            ModuleDTO module = new ModuleDTO();
            module.setModuleId(permissionModule.getId());
            module.setModuleName(PermissionModuleEnum.getNameById(permissionModule.getId()));
            module.setModuleUrl(permissionModule.getPermissionUrl());
            modules.add(module);
        }
        return modules;
    }

    @Override
    public List<String> getNoPermissionField(List<Integer> fieldRootIds, Integer roleId) {
        List<Integer> permissionIds = this.getRolePermissionIds(roleId);
        List<Permission> fieldPermissions = this.baseMapper.selectList(
                new QueryWrapper<Permission>().lambda().in(Permission::getPid, fieldRootIds)
                        .eq(Permission::getPermissionStatus, PermissionStatusEnum.NORMAL.getKey())
        );
        List<String> noPermissionFields = new ArrayList<>();
        for (Permission fieldPermission : fieldPermissions) {
            if (!permissionIds.contains(fieldPermission.getId())) {
                noPermissionFields.add(fieldPermission.getPermissionUrl());
            }
        }
        return noPermissionFields;
    }

    @Override
    public List<MenuDTO> getUserMenu(Integer roleId) {

        List<Integer> permissionIds = JSONObject.parseArray(this.roleMapper.getRolePermissions(new QueryWrapper<Role>().lambda()
                .eq(Role::getId, roleId)), Integer.class);
        if (permissionIds.isEmpty()) {
            return List.of(new MenuDTO());
        }
        List<Permission> permissions = this.permissionMapper.selectList(new QueryWrapper<Permission>().lambda()
                .eq(Permission::getPermissionStatus, PermissionStatusEnum.NORMAL.getKey())
                .in(Permission::getId, permissionIds)
                .orderByAsc(Permission::getSort)
                .orderByDesc(Permission::getId)
        );
        List<Permission> modulePermissions = permissions.stream().filter(permission -> permission.getPermissionType().equals(1)).collect(Collectors.toList());
        List<Permission> childrenPermissions = permissions.stream().filter(permission -> permission.getPermissionType().equals(2)).collect(Collectors.toList());
        return modulePermissions.stream().map(modulePermission -> {
            MenuDTO menuDTO = new MenuDTO();
            menuDTO.setMenuId(modulePermission.getId());
            menuDTO.setMenuName(PermissionModuleEnum.getNameById(modulePermission.getId()));
            menuDTO.setMenuUrl("");
            menuDTO.setMenuIcon("");
            menuDTO.setChildren(this.getMenuChildren(modulePermission.getId(), childrenPermissions));
            return menuDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 按用户类型获取配置的排除结点数组
     *
     * @param roleType 角色类型
     * @return 排除的结点数组
     */
    private List<Integer> getExcludePermission(Integer roleType) {
        List<Integer> exclude = new ArrayList<>();
        String excludeConfig;
        if (roleType.equals(RoleTypeEnum.AGENT.getKey())) {
            excludeConfig = this.agentExclude;
        } else if (roleType.equals(RoleTypeEnum.MANAGER.getKey())) {
            excludeConfig = this.masterExclude;
        } else {
            return exclude;
        }
        String[] ids = excludeConfig.split(",");
        for (String id : ids) {
            exclude.add(Integer.parseInt(id));
        }
        return exclude;
    }

    /**
     * 根据已知的权限项ID获取一组权限
     *
     * @param permissionIds 权限项id集合
     * @return 权限详情列表
     */
    private List<Permission> listPermissionByIds(List<Integer> permissionIds) {
        QueryWrapper<Permission> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", permissionIds)
                .eq("permission_status", PermissionStatusEnum.NORMAL.getKey())
                .orderByAsc("permission_level,sort");
        return this.permissionMapper.selectList(queryWrapper);
    }

    /**
     * 根据当前权限列表，递归获取某个模块下所有子节点权限项
     *
     * @param children      权限集合
     * @param pId           父节点id
     * @param permissionAll 拥有的权限
     * @param selectedIds   已分配的权限项id集合
     */
    private void getTreeChildren(List<PermissionDTO> children, Integer pId, List<Permission> permissionAll,
                                 List<Integer> selectedIds) {
        for (Permission permission : permissionAll) {
            if (permission.getPid().equals(pId)) {
                // 设置结点信息，插入到子节点列表
                PermissionDTO permissionDto = new PermissionDTO();
                BeanUtils.copyProperties(permission, permissionDto);
                permissionDto.setPId(permission.getPid());
                permissionDto.setTitle(permission.getPermissionName());
                // 已选中或者该节点是必选权限，设置为已选
                Integer isSelected = selectedIds.contains(permission.getId())
                        || permission.getIsRequired().equals(PermissionIsRequiredEnum.REQUIRED.getKey())
                        ? PermissionIsSelectedEnum.SELECTED.getKey() : PermissionIsSelectedEnum.NOT_SELECTED.getKey();
                permissionDto.setIsSelected(isSelected);
                children.add(permissionDto);

                // 递归分支结点
                this.getTreeChildren(children, permission.getId(), permissionAll, selectedIds);
            }
        }
    }

    /**
     * 递归处理权限树结构数据
     *
     * @param pid             父节点id
     * @param permissionsInDb 权限集合
     * @return 树子节点数据
     */
    private List<MenuDTO> getMenuChildren(Integer pid, List<Permission> permissionsInDb) {
        List<MenuDTO> childrenMenus = new ArrayList<>();
        for (Permission permissionInDb : permissionsInDb) {
            if (permissionInDb.getPid().equals(pid)) {
                MenuDTO menu = new MenuDTO();
                menu.setMenuId(permissionInDb.getId());
                menu.setMenuName(permissionInDb.getPermissionName());
                menu.setMenuUrl(permissionInDb.getPermissionUrl());
                menu.setMenuIcon(permissionInDb.getPermissionIcon());
                List<MenuDTO> nodeChildrenMenus = this.getMenuChildren(permissionInDb.getId(), permissionsInDb);
                menu.setChildren(nodeChildrenMenus);
                childrenMenus.add(menu);
            }
        }
        return childrenMenus.size() > 0 ? childrenMenus : null;
    }

    /**
     * 递归获取指定节点下不同类型权限
     *
     * @param pId         父节点id
     * @param permissions 全部权限列表
     * @param pages       页面id集合
     * @param apiButtons  按钮id集合
     * @param fields      字段集合
     * @param pageModules 页面模块集合
     * @param menus       菜单集合
     */
    private void getPermissionChildren(Integer pId, List<Permission> permissions, List<Integer> pages,
                                       List<Integer> apiButtons, List<Integer> fields, List<Integer> pageModules, List<Integer> menus) {
        for (Permission permission : permissions) {
            if (permission.getPid().equals(pId)) {
                if (permission.getPermissionType().equals(PermissionTypeEnum.PAGE.getKey())) {
                    pages.add(permission.getId());
                } else if (permission.getPermissionType().equals(PermissionTypeEnum.API_BUTTON.getKey())) {
                    apiButtons.add(permission.getId());
                } else if (permission.getPermissionType().equals(PermissionTypeEnum.FIELD.getKey())) {
                    fields.add(permission.getId());
                } else if (permission.getPermissionType().equals(PermissionTypeEnum.PAGE_MODULE.getKey())) {
                    pageModules.add(permission.getId());
                } else if (permission.getPermissionType().equals(PermissionTypeEnum.MENU.getKey())) {
                    menus.add(permission.getId());
                }
                this.getPermissionChildren(permission.getId(), permissions, pages, apiButtons, fields, pageModules, menus);
            }
        }
    }
}
