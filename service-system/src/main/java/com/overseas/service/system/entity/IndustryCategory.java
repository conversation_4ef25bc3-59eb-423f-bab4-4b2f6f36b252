package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.TimeBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@TableName("d_industry_category")
@EqualsAndHashCode(callSuper = true)
public class IndustryCategory extends TimeBase {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String industryKey;

    private String industryName;

    private String industryEnName;

    private Long parentId;

    private Integer industryLevel;
}
