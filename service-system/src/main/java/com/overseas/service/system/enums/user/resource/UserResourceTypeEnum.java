package com.overseas.service.system.enums.user.resource;


import java.util.ArrayList;
import java.util.List;

public enum UserResourceTypeEnum {

    // 用户类型资源目前和用户类型id保持一致，防止混淆
    MASTER(1, "投放账号");

    private int id;

    private String name;

    UserResourceTypeEnum(final int id, final String name) {
        this.id = id;
        this.name = name;
    }

    /**
     * 根据ID 获取内容
     *
     * @param id
     * @return
     */
    public static UserResourceTypeEnum getById(final int id) {
        for (UserResourceTypeEnum userResourceTypeEnum : values()) {
            if (userResourceTypeEnum.getId() == id) {
                return userResourceTypeEnum;
            }
        }
        return null;
    }

    /**
     * 根据ID获取名称
     *
     * @param id
     * @return
     */
    public static String getNameById(final int id) {
        UserResourceTypeEnum userResourceTypeEnum = UserResourceTypeEnum.getById(id);
        return null == userResourceTypeEnum ? null : userResourceTypeEnum.getName();
    }


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取客户类型id集合
     *
     * @return 客户资源类型id集合
     */
    public static List<Integer> getCustomerIds() {
        return new ArrayList<>() {{
            add(MASTER.getId());
        }};
    }

    /**
     * 获取全部资源类型id集合
     *
     * @return 所有资源类型id集合
     */
    public static List<Integer> getAllTypeIds() {
        List<Integer> userResourceTypeIds = new ArrayList<>();
        for (UserResourceTypeEnum userResourceTypeEnum : values()) {
            userResourceTypeIds.add(userResourceTypeEnum.getId());
        }
        return userResourceTypeIds;
    }
}
