package com.overseas.service.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.sys.UserTreeDTO;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.sys.user.UserMasterGetVO;
import com.overseas.service.system.entity.User;
import com.overseas.service.system.entity.UserResource;
import com.overseas.service.system.enums.user.resource.UserResourceTypeEnum;
import com.overseas.service.system.mapper.UserMapper;
import com.overseas.service.system.mapper.UserResourceMapper;
import com.overseas.service.system.service.UserResourceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
public class UserResourceServiceImpl extends ServiceImpl<UserResourceMapper, UserResource> implements UserResourceService {

    private final UserMapper userMapper;

    @Override
    public boolean checkUserResourcePermission(Integer userId, List<Integer> resourceId, Integer resourceType) {
        List<UserResource> userResource = this.lambdaQuery().eq(UserResource::getUserId, userId)
                .in(UserResource::getResourceId, resourceId)
                .eq(UserResource::getResourceType, resourceType)
                .list();
        return userResource.size() == resourceId.size();
    }

    @Override
    public int saveByUserResource(List<Integer> resourceIds, UserResource userResource) {
        if (resourceIds == null) {
            return 0;
        }
        //生成List数组
        List<UserResource> list = new ArrayList<>();
        for (int id : resourceIds) {
            UserResource userResource1 = new UserResource();
            userResource1.setResourceId(id);
            userResource1.setCreateUid(userResource.getCreateUid());
            userResource1.setResourceType(userResource.getResourceType());
            userResource1.setUserId(userResource.getUserId());
            list.add(userResource1);
        }
        if (!list.isEmpty()) {
            if (this.saveBatch(list)) {
                return list.size();
            }
        }
        return 0;
    }

    @Override
    public List<UserTreeDTO> selectUserTreeByUserResource(QueryWrapper<UserResource> queryWrapper) {
        return this.baseMapper.selectUserTreeByUserResource(queryWrapper);
    }

    @Override
    public List<UserTreeDTO> selectCustomerTree(Integer userId, Collection<Integer> userResourceType) {
        return baseMapper.selectUserTreeByUserResource(
                new QueryWrapper<UserResource>().eq("r.user_id", userId)
                        .in("r.resource_type", userResourceType)
                        .isNotNull("u.id")
                        .select("u.id ", " u.user_name", "u.company_name as name", "u.parent_id as pId", "IF(u.parent_id =0,1,2) as `level`")

        );
    }

    @Override
    public List<SelectDTO> selectDtoByUserResource(QueryWrapper<UserResource> queryWrapper) {
        return this.baseMapper.selectDtoByUserResource(queryWrapper);
    }

    /**
     * 根据用户id获取全部资源列表
     *
     * @param userId 用户id
     * @return 资源列表
     */
    @Override
    public List<UserResource> listUserResource(Integer userId) {
        List<Integer> userResourceTypes = UserResourceTypeEnum.getAllTypeIds();
        return this.listUserResource(userId, userResourceTypes);
    }

    /**
     * 根据用户id和资源类型获取资源列表
     *
     * @param userId           用户id
     * @param userResourceType 指定资源类型集合
     * @return 资源列表
     */
    public List<UserResource> listUserResource(Integer userId, Integer userResourceType) {
        List<Integer> userResourceTypes = Lists.newArrayList(userResourceType);
        return this.listUserResource(userId, userResourceTypes);
    }

    /**
     * 根据用户id和资源类型获取资源列表
     *
     * @param userId            用户id
     * @param userResourceTypes 指定资源类型集合
     * @return 资源列表
     */
    @Override
    public List<UserResource> listUserResource(Integer userId, List<Integer> userResourceTypes) {
        QueryWrapper<UserResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserResource::getUserId, userId)
                .in(UserResource::getResourceType, userResourceTypes)
                .orderByAsc(UserResource::getResourceType);
        return this.baseMapper.selectList(queryWrapper);
    }

    /**
     * 获取用户类型资源，支持查询过滤，主要用于报表查询前过滤
     *
     * @param userId           用户id
     * @param userResourceType 资源类型
     * @param search           查询字符串
     * @return 用户资源集合
     */
    @Override
    public Map<Integer, User> selectUserResource(Integer userId, Integer userResourceType, String search) {
        QueryWrapper<UserResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("u.id,u.company_name")
                .eq("r.user_id", userId)
                .eq("r.resource_type", userResourceType)
                .and(i -> i.like("u.id", search).or().like("u.company_name", search));
        List<User> users = this.baseMapper.selectUserResource(queryWrapper);
        Map<Integer, User> userMap = new HashMap<>();
        for (User user : users) {
            userMap.put(user.getId(), user);
        }
        return userMap;
    }

    @Override
    public List<Long> getMasterIds(UserMasterGetVO getVO) {
        QueryWrapper<UserResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserResource::getUserId, getVO.getUserId())
                .eq(UserResource::getResourceType, UserResourceTypeEnum.MASTER.getId())
                .orderByAsc(UserResource::getResourceType);
        return this.baseMapper.getMasterIdByProject(new QueryWrapper<UserResource>()
                .and(q -> q.eq("pro_resource.is_del", IsDelEnum.NORMAL.getId())
                        .or().isNull("pro_resource.is_del"))
                .eq("resource.resource_type", UserResourceTypeEnum.MASTER.getId())
                .eq("resource.user_id", getVO.getUserId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getProjectId()), "pro_resource.project_id", getVO.getProjectId())
                .in(CollectionUtils.isNotEmpty(getVO.getProjectIds()), "pro_resource.project_id", getVO.getProjectIds())
                .orderByDesc("resource.resource_id"));
    }

    @Override
    public List<Long> getMasterIdByInfo(UserMasterGetVO getVO) {
        return this.baseMapper.getMasterIdByInfo(new QueryWrapper<>()
                .eq("is_del", IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getProjectId()), "project_id", getVO.getProjectId())
                .in(CollectionUtils.isNotEmpty(getVO.getProjectIds()), "project_id", getVO.getProjectIds())
                .orderByDesc("resource_id")
        );
    }

    /**
     * 根据用户id和资源类型，获取资源id集合
     *
     * @param userIds          用户id集合
     * @param userResourceType 指定资源类型集合
     * @return 资源ID集合
     */
    @Override
    public List<Integer> listUserResourceIds(List<Integer> userIds, Integer userResourceType) {
        return this.baseMapper.getResourceIdByUser(new QueryWrapper<UserResource>()
                .lambda()
                .select(UserResource::getResourceId)
                .in(UserResource::getUserId, userIds)
                .eq(UserResource::getResourceType, userResourceType)
                .orderByAsc(UserResource::getResourceId));
    }
}
