package com.overseas.service.system.controller;

import com.overseas.common.dto.R;
import com.overseas.common.enums.market.asset.IsUploadEnum;
import com.overseas.common.utils.FFmpegUtils;
import com.overseas.common.utils.FileUtils;
import com.overseas.common.utils.UploadUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "COMMON-文件上传")
@RestController
@RequestMapping("/sys/uploads")
@Slf4j
@RequiredArgsConstructor
@Valid
public class CommonUploadController extends AbstractController {


    @ApiOperation("上传文件")
    @PostMapping("/upload/{type}")
    public R upload(@RequestParam("file") MultipartFile file, @PathVariable(value = "type") String type) throws IOException {
        //增加文件夹的类型
        type = type.replaceAll("\\.", "/");
        String basePath = UploadUtils.uploadFile(type, file, null, 500 * 1024 * 1024L);
        R r = R.ok();
        r.put("originName", file.getOriginalFilename());
        r.put("filePath", basePath);
        if (List.of("creative", "behaviorApp").contains(type)) {
            r.put("data", FFmpegUtils.info(UploadUtils.getUploadPath(basePath), file.getOriginalFilename()));
        }
        return r.put("fileUrl", UploadUtils.getNetworkUrl(basePath, IsUploadEnum.NOT_UPLOAD.getId()));
    }

    @GetMapping("/download")
    @ApiOperation("下载文件")
    public void download(@RequestParam("fileName") @NotBlank(message = "文件名称不合法") String filename,
                         @RequestParam("fileUrl") @NotBlank(message = "文件地址不能为空") String fileUrl,
                         HttpServletResponse response) throws IOException {
        FileUtils.download(UploadUtils.getUploadPath(UploadUtils.getBasePath(URLDecoder.decode(fileUrl, Charset.defaultCharset()))), filename, response);
    }

}
