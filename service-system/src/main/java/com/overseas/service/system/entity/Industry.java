package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("d_industry")
public class Industry {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String industryName;

    private Integer parentId;

    private Byte industryLevel;

    private Date createTime;
}
