package com.overseas.service.system.controller;

import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.vo.sys.user.UserMasterGetVO;
import com.overseas.service.system.common.utils.RealmUtils;
import com.overseas.service.system.entity.User;
import com.overseas.service.system.entity.UserResource;
import com.overseas.service.system.service.UserResourceService;
import com.overseas.service.system.enums.user.resource.UserResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-15 19:27
 */
@ApiIgnore
@Slf4j
public abstract class AbstractController {

    protected UserResourceService userResourceService;

    @Autowired
    public void setUserResourceService(UserResourceService userResourceService) {
        this.userResourceService = userResourceService;
    }

    /**
     * 获取当前登录用户信息
     *
     * @return
     */
    protected User getUser() {
        User realm = RealmUtils.getUser();
        if (null == realm) {
            throw new CustomException(ResultStatusEnum.NO_LOGIN.getCode(), ResultStatusEnum.NO_LOGIN.getMessage());
        }
        return realm;
    }

    /**
     * 获取登陆 不提示一场
     *
     * @return 返回数据
     */
    protected User getUserNoException() {
        try {
            return RealmUtils.getUser();
        } catch (CustomException e) {
            return null;
        }
    }

    /**
     * 获取当前登录用户ID
     *
     * @return
     */
    protected Integer getUserId() {
        return getUser().getId();
    }

    /**
     * 获取登录用户的投放账户列表
     *
     * @return
     */
    protected List<Integer> listMasterId() {
        return userResourceService.listUserResource(getUserId(), UserResourceTypeEnum.MASTER.getId()).stream()
                .map(UserResource::getResourceId).collect(Collectors.toList());
    }

    protected List<Long> getMasterIds(UserMasterGetVO getVO) {
        getVO.setUserId(this.getUserId().longValue());
        return this.userResourceService.getMasterIds(getVO);
    }
}
