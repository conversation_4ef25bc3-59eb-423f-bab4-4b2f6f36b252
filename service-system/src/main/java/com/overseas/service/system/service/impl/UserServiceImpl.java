package com.overseas.service.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.sys.ModuleDTO;
import com.overseas.common.dto.sys.UserLoginDTO;
import com.overseas.common.dto.sys.user.UserMailDTO;
import com.overseas.common.dto.sys.user.UserSelectByIdDTO;
import com.overseas.common.entity.BaseServer;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.common.utils.HttpUtils;
import com.overseas.common.utils.HumpLineUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.market.MarketMasterGetVO;
import com.overseas.common.vo.market.master.MasterBindVO;
import com.overseas.common.vo.sys.UserLoginVO;
import com.overseas.common.vo.sys.user.UserCheckGetVO;
import com.overseas.common.vo.sys.user.UserMailGetVO;
import com.overseas.common.vo.sys.user.UserSelectByIdVO;
import com.overseas.common.vo.sys.user.UserSelectGetVO;
import com.overseas.service.system.entity.Role;
import com.overseas.service.system.entity.User;
import com.overseas.service.system.entity.UserResource;
import com.overseas.service.system.enums.user.UserDestroyStatusEnum;
import com.overseas.service.system.enums.user.UserTypeEnum;
import com.overseas.service.system.enums.user.resource.UserResourceTypeEnum;
import com.overseas.service.system.mapper.BaseServerMapper;
import com.overseas.service.system.mapper.RoleMapper;
import com.overseas.service.system.mapper.UserMapper;
import com.overseas.service.system.mapper.UserResourceMapper;
import com.overseas.service.system.service.PermissionService;
import com.overseas.service.system.service.UserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-13 9:21
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Value("${system.web-url}")
    private String webUrl;

    private final RoleMapper roleMapper;

    private final PermissionService permissionService;

    private final UserResourceMapper userResourceMapper;

    private final BaseServerMapper baseServerMapper;

    @Override
    public UserLoginDTO getUserLoginInfo(User user) {
        UserLoginDTO userLogin = new UserLoginDTO();
        BeanUtils.copyProperties(user, userLogin);
        UserTypeEnum userTypeEnum = ICommonEnum.get(user.getUserType(), UserTypeEnum.class);
        userLogin.setUserTypeName(userTypeEnum.getName());
        userLogin.setCompanyName(user.getCompanyName());
        // 获取角色名称
        Role roleInDb = roleMapper.selectById(user.getRoleId());
        userLogin.setRoleName(null == roleInDb ? ConstantUtils.UNKNOWN : roleInDb.getRoleName());
        // 获取分配的模块，超级管理员获取所有权限
        List<Integer> ownPermissionIds = this.permissionService.getRolePermissionIds(user.getRoleId());
        List<ModuleDTO> modules = this.permissionService.getModules(ownPermissionIds);
        // 设置链接配置
        for (ModuleDTO module : modules) {
            String firstUrl = this.permissionService.getFirstMenu(module.getModuleId(), user.getRoleId());
            // 非DMP或者IDB模块的根据角色添加链接后缀
            module.setModuleUrl(webUrl + "/#" + firstUrl);
        }
        userLogin.setModules(modules);
        return userLogin;
    }

    @Override
    public void fillLoginToken(UserLoginVO userLoginVO, UserLoginDTO userLoginDTO) {

        List<BaseServer> baseServers = this.baseServerMapper.selectList(new QueryWrapper<BaseServer>().lambda()
                .eq(BaseServer::getIsDel, IsDelEnum.NORMAL.getId())
                .ne(BaseServer::getServerKey, "xjp"));
        if (baseServers.isEmpty()) {
            return;
        }
        // 获取各个机房Token
        baseServers.forEach(baseServer -> {
            String res = HttpUtils.post(baseServer.getDomain() + "/sys/auth/login", ObjectUtils.toMap(userLoginVO));
            ObjectUtils.setObjectValue(userLoginDTO, "authToken" + HumpLineUtils.firstToUpper(baseServer.getServerKey()),
                    ObjectUtils.toMap(ObjectUtils.toMap(JSONObject.parseObject(res).get("data"))).get("authToken"));
        });
    }

    @Override
    public List<UserMailDTO> getMangerByMail(UserMailGetVO getVO) {

        List<String> email = List.of(getVO.getEmail().split(","));
        if (email.isEmpty()) {
            return List.of();
        }
        return this.baseMapper.getManagerByMail(new QueryWrapper<User>().lambda()
                .eq(User::getUserType, UserTypeEnum.MANAGER.getId())
                .eq(User::getIsDestroy, IsDelEnum.NORMAL.getId())
                .in(User::getEmail, email));
    }

    @Override
    public User getUserByUserName(String userName) {
        return this.baseMapper.selectOne(new QueryWrapper<User>().lambda().eq(User::getUserName, userName)
                .eq(User::getIsDestroy, UserDestroyStatusEnum.COMMON.getId()));
    }

    @Override
    public User getUserByEmailOrPhone(String loginName) {
        return this.baseMapper.selectOne(new QueryWrapper<User>().lambda()
                .and(i -> i.eq(User::getEmail, loginName).or().eq(User::getPhone, loginName))
                .eq(User::getIsDestroy, UserDestroyStatusEnum.COMMON.getId())
                // 只允许代理或者管理账户登录
                .in(User::getUserType, List.of(UserTypeEnum.AGENT.getId(), UserTypeEnum.MANAGER.getId(),
                        UserTypeEnum.FINANCE_MANAGER.getId(), UserTypeEnum.DESIGNER.getId(), UserTypeEnum.DEVELOP.getId())));
    }

    @Override
    public List<SelectDTO> selectUser(Integer userType) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id, real_name AS `name`").lambda()
                .eq(User::getUserType, userType)
                .eq(User::getIsDestroy, UserDestroyStatusEnum.COMMON.getId())
                .orderByDesc(User::getId);

        return this.baseMapper.selectSelectDto(queryWrapper);
    }

    @Override
    public User getManagerInfoByEmail(String email) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUserName, email).eq(User::getUserType, UserTypeEnum.MANAGER.getId());
        User manager = this.getOne(wrapper);
        if (null == manager) {
            throw new CustomException("邮箱不存在，请检查后重试");
        }
        return manager;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindManager(MasterBindVO masterBindVO, User loginUser) {

        // 绑定
        if (masterBindVO.getOperateType().equals(1)) {
            if (StringUtils.isBlank(masterBindVO.getEmail())) {
                throw new CustomException("邮箱不能为空");
            }
            List<String> emails = new ArrayList<>(List.of(masterBindVO.getEmail().split(","))).stream().distinct().collect(Collectors.toList());
            List<User> managers = this.baseMapper.selectList(new QueryWrapper<User>().lambda()
                    .in(User::getEmail, emails)
                    .eq(User::getUserType, UserTypeEnum.MANAGER.getId()));
            emails.removeAll(managers.stream().map(User::getEmail).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(emails)) {
                throw new CustomException("邮箱（" + String.join(",", emails) + "）不存在，请确认后再试");
            }
            List<UserResource> userResources = managers.stream().map(manager -> {
                UserResource userResource = new UserResource();
                userResource.setUserId(manager.getId());
                userResource.setResourceType(UserResourceTypeEnum.MASTER.getId());
                userResource.setResourceId(masterBindVO.getId());
                return userResource;
            }).collect(Collectors.toList());
            this.userResourceMapper.batchSaveUserResource(userResources, loginUser.getId());
        } else {
            if (masterBindVO.getManagerIds().isEmpty()) {
                throw new CustomException("管理账号不能为空");
            }
            // 解绑
            List<SelectDTO> selectDTOS = userResourceMapper.selectDtoByUserResource(new QueryWrapper<UserResource>()
                    .eq("r.resource_id", masterBindVO.getId())
                    .eq("u.user_type", UserTypeEnum.MANAGER.getId())
                    .in("r.user_id", masterBindVO.getManagerIds())
                    .select("u.id", "u.user_name"));
            if (CollectionUtils.isNotEmpty(selectDTOS)) {
                this.userResourceMapper.delete(new LambdaQueryWrapper<UserResource>().eq(UserResource::getResourceId, masterBindVO.getId())
                        .in(UserResource::getUserId, selectDTOS.stream().map(SelectDTO::getId).collect(Collectors.toList()))
                        .eq(UserResource::getResourceType, UserResourceTypeEnum.MASTER.getId()));
            }
        }

    }

    @Override
    public List<SelectDTO> selectPermissionAgent(Integer loginUserId) {
        return this.userResourceMapper.selectPermissionAgent(new QueryWrapper<UserResource>()
                .eq("ur.user_id", loginUserId)
                .groupBy("ag.id")
                .orderByDesc("ag.id"));
    }

    @Override
    public List<Integer> listMasterIdByAgentId(Integer agentId) {
        User agentInDb = getById(agentId);
        if (null == agentInDb || !UserTypeEnum.AGENT.getId().equals(agentInDb.getUserType())) {
            throw new CustomException("代理商ID不存在");
        }

        return this.lambdaQuery().eq(User::getParentId, agentId).list().stream().map(User::getId).collect(Collectors.toList());
    }

    @Override
    public List<SelectDTO> getAllAgentSelect() {
        return this.baseMapper.selectList(new QueryWrapper<User>().lambda()
                .eq(User::getUserType, UserTypeEnum.AGENT.getId())
                .eq(User::getIsDestroy, IsDelEnum.NORMAL.getId())
                .orderByDesc(User::getId)).stream().map(u -> new SelectDTO(u.getId().longValue(), u.getRealName())).collect(Collectors.toList());
    }

    @Override
    public List<SelectDTO> getAllFinanceManagerSelect() {
        return this.baseMapper.selectList(new QueryWrapper<User>().lambda()
                .eq(User::getUserType, UserTypeEnum.FINANCE_MANAGER.getId())
                .eq(User::getIsDestroy, IsDelEnum.NORMAL.getId())
                .orderByDesc(User::getId)).stream().map(u -> new SelectDTO(u.getId().longValue(), u.getRealName())).collect(Collectors.toList());
    }

    @Override
    public User checkUserName(String userName) {
        return this.baseMapper.selectOne(new QueryWrapper<User>().lambda()
                .eq(User::getIsDestroy, IsDelEnum.NORMAL.getId())
                .eq(User::getCompanyName, userName));
    }

    @Override
    public List<User> getManagersByMaster(MarketMasterGetVO getVO) {
        if (ObjectUtils.isNullOrZero(getVO.getMasterId()) && CollectionUtils.isEmpty(getVO.getMasterIds())) {
            throw new CustomException("账户信息获取失败");
        }
        return this.userResourceMapper.getManagersByMaster(new QueryWrapper<UserResource>()
                .eq("uu.user_type", UserTypeEnum.MANAGER.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), "uur.resource_id", getVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(getVO.getMasterIds()), "uur.resource_id", getVO.getMasterIds())
        );
    }

    @Override
    public List<SelectDTO> selectUser(UserSelectGetVO getVO) {
        return this.baseMapper.selectSelectDto(new QueryWrapper<User>()
                .select("id,company_name AS `name`").lambda()
                .eq(User::getIsDestroy, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(getVO.getUserType()), User::getUserType, getVO.getUserType())
                .orderByDesc(User::getId));
    }

    @Override
    public List<UserSelectByIdDTO> selectUserById(UserSelectByIdVO selectByIdVO) {
        return this.baseMapper.selectUserById(
                new QueryWrapper<>().in("uu.id", selectByIdVO.getIds())
        );
    }

    @Override
    public User checkUserIsExist(UserCheckGetVO getVO) {
        return this.baseMapper.selectOne(new QueryWrapper<User>().lambda()
                .eq(User::getIsDestroy, IsDelEnum.NORMAL.getId())
                .eq(User::getCompanyName, getVO.getCompanyName())
                .ne(ObjectUtils.isNotNullOrZero(getVO.getId()), User::getId, getVO.getId()));
    }

    @Override
    public void updateUser(User userVO) {
        User user = new User();
        user.setId(userVO.getId());
        user.setRealName(userVO.getRealName());
        user.setCompanyName(userVO.getCompanyName());
        user.setAddress(userVO.getAddress());
        user.setPhone(userVO.getPhone());
        user.setEmail(userVO.getEmail());
        this.baseMapper.update(user, new QueryWrapper<User>().lambda()
                .eq(User::getId, user.getId()));
    }
}
