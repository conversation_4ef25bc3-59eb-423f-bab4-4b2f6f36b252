package com.overseas.service.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.TimeBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName("d_industry_copy")
@EqualsAndHashCode(callSuper = true)
public class IndustryCopy extends TimeBase {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String industryName;

    private String industryEnName;

    private Long parentId;

    private Integer industryLevel;
}
