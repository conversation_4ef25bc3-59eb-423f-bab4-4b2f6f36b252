package com.overseas.service.system.common.filter;

import com.google.gson.Gson;
import com.overseas.common.dto.R;
import com.overseas.common.enums.ResultStatusEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Getter
@Setter
public class ShiroLoginFilter extends FormAuthenticationFilter {
    /**
     * smp地址
     */
//    private String smpUrl;
    public ShiroLoginFilter() {
        super();
    }

    /**
     * 如果isAccessAllowed返回false 则执行onAccessDenied
     *
     * @param request     请求体
     * @param response    响应体
     * @param mappedValue 匹配值
     * @return 是否允许访问
     */
    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        if (request instanceof HttpServletRequest) {
            if (((HttpServletRequest) request).getMethod().toUpperCase().equals("OPTIONS")) {
                return true;
            }
        }
        return super.isAccessAllowed(request, response, mappedValue);
    }

    /**
     * 在访问controller前判断是否登录，返回json，不进行重定向。
     *
     * @param request  请求体
     * @param response 响应体
     * @return true-继续往下执行，false-该filter过滤器已经处理，不继续执行其他过滤器
     * @throws Exception io异常
     */
    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        String origin = httpServletRequest.getHeader("Origin");
        //这里是个坑，如果不设置的接受的访问源，那么前端都会报跨域错误，因为这里还没到corsConfig里面
        httpServletResponse.setHeader("Access-Control-Allow-Origin", origin);
        httpServletResponse.setHeader("Access-Control-Allow-Credentials", "true");
        httpServletResponse.setHeader("Origin", origin);
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("application/json");
        R result = R.error(ResultStatusEnum.NO_LOGIN);
        httpServletResponse.getWriter().write(new Gson().toJson(result));
        return false;
    }
}
