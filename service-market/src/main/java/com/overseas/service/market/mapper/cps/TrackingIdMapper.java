package com.overseas.service.market.mapper.cps;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.trackingId.TrackingIdListDTO;
import com.overseas.service.market.entity.assetTask.TrackingId;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TrackingIdMapper extends BaseMapper<TrackingId> {
    @Select("SELECT DISTINCT mti.id,mti.project_id,mp.project_name,mti.cps_app_id,aa.app_name AS cps_app_name," +
            "mti.tracking_id,mti.tracking_name,mti.origin_id " +
            "FROM m_tracking_id AS mti " +
            "INNER JOIN m_project_resource AS mpr ON mpr.project_id = mti.project_id AND mpr.is_del = 0 " +
            "INNER JOIN u_user_resource AS uur ON uur.resource_id = mpr.resource_id AND uur.resource_type = 1 " +
            "INNER JOIN m_project AS mp ON mp.id = mti.project_id " +
            "INNER JOIN ae_app AS aa ON aa.id = mti.cps_app_id " +
            "LEFT JOIN u_user AS uu ON uu.id = mti.create_uid " +
            "${ew.customSqlSegment}")
    IPage<TrackingIdListDTO> listTrackingId(IPage<TrackingIdListDTO> page,
                                          @Param(ConstantUtils.WRAPPER) Wrapper<TrackingId> wrapper);

    @Select("SELECT id,tracking_name AS `name` FROM m_tracking_id ${ew.customSqlSegment}")
    List<SelectDTO> selectTrackingId(@Param(ConstantUtils.WRAPPER) Wrapper<TrackingId> wrapper);

    @Insert("<script> " +
            "INSERT INTO `m_tracking_id` (`project_id`, `cps_app_id`, `tracking_id`, `tracking_name`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.projectId}, #{item.cpsAppId}, #{item.trackingId}, #{item.trackingName}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "tracking_name = VALUES(tracking_name) " +
            "</script>")
    void insertTrackingIdByUk(@Param("list") List<TrackingId> list);
}
