package com.overseas.service.market.mapper.cps;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.cps.CpsDpaUrlListDTO;
import com.overseas.service.market.entity.cps.CpsDpaUrl;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CpsDpaUrlMapper extends BaseMapper<CpsDpaUrl> {

    @Select("SELECT cp.id AS product_id,cp.project_id,cp.product_detail_url,cdu.id,cdu.cps_app_id,cdu.cps_url " +
            "FROM cps_product AS cp " +
            "LEFT JOIN cps_dpa_url AS cdu ON cdu.product_id=cp.id " +
            "${ew.customSqlSegment}")
    List<CpsDpaUrlListDTO> listProductDpaUrl(@Param(ConstantUtils.WRAPPER) QueryWrapper<CpsDpaUrl> queryWrapper);
}
