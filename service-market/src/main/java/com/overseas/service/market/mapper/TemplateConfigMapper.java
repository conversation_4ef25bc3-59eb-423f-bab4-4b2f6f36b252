package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.market.template.TemplateListDTO;
import com.overseas.common.dto.market.template.TemplateSelectDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.entity.TemplateConfig;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TemplateConfigMapper extends BaseMapper<TemplateConfig> {

    /**
     * 模板下啦
     *
     * @param wrapper 条件
     * @return 数据
     */
    @Select("SELECT id, template_name AS `name`, template_desc FROM d_template_config ${ew.customSqlSegment}")
    List<TemplateSelectDTO> getTemplateSelect(@Param(ConstantUtils.WRAPPER) Wrapper<TemplateConfig> wrapper);

    /**
     * 获取模板分页数据
     *
     * @param iPage   分页条件
     * @param wrapper 条件
     * @return 数据
     */
    @Select("SELECT  * from d_template_config ${ew.customSqlSegment}")
    IPage<TemplateListDTO> list(IPage<?> iPage, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
