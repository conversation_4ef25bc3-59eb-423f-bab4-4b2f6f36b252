package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.service.market.entity.CreativeMaterial;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CreativeMaterialMapper extends BaseMapper<CreativeMaterial> {
    @Select("<script> " +
            "INSERT INTO m_creative_material (`master_id`,`creative_id`,`material_id`,`create_uid`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.masterId},#{item.creativeId},#{item.materialId},#{userId}) " +
            "</foreach> " +
            "</script>")
    void saveCreativeMaterial(@Param("list") List<CreativeMaterial> creativeMaterialList, Integer userId);
}
