package com.overseas.service.market.mapper.assetTask;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.assetTask.AssetTaskFeedbackDTO;
import com.overseas.service.market.entity.assetTask.AssetTaskFeedback;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AssetTaskFeedbackMapper extends BaseMapper<AssetTaskFeedback> {

    @Select("SELECT " +
            "matf.id,matf.asset_task_id,matf.feedback_content,uu.real_name AS create_user_name " +
            "FROM " +
            "m_asset_task_feedback AS matf " +
            "INNER JOIN u_user AS uu ON matf.create_uid = uu.id " +
            "${ew.customSqlSegment}")
    List<AssetTaskFeedbackDTO> getAssetTaskFeedbacks(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
