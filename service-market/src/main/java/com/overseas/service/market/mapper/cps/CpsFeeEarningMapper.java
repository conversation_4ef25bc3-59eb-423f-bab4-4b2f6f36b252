package com.overseas.service.market.mapper.cps;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.dto.market.cps.amazon.CpsAmazonEarningsExcelDTO;
import com.overseas.service.market.entity.CpsFeeEarning;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface CpsFeeEarningMapper extends BaseMapper<CpsFeeEarning> {


    /**
     * 插入数据
     *
     * @param list 数据
     */
    @Insert("<script> " +
            "INSERT INTO `cps_fee_earning` ( `cate_name`, `order_name`, `tracking_id`," +
            " `product_id`, `settlement_time`, `seller`, `price`, " +
            "`product_count`, `return_count`, `revenue`, `ad_fee`, " +
            "`device_type`,`conversion_type`, `order_status`, `idx`, `create_uid`)  " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.cateName}, #{item.orderName}, #{item.trackingId}," +
            "#{item.productId}, #{item.settlementTime},#{item.seller},  #{item.price}," +
            "#{item.productCount}, #{item.returnCount}, #{item.revenue}, #{item.adFee}," +
            "#{item.deviceType}, #{item.conversionType}, ${item.orderStatus}, #{item.idx}, #{userId}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "cate_name = VALUES(cate_name), " +
            "order_name = VALUES(order_name), " +
            "tracking_id = VALUES(tracking_id), " +
            "product_id = VALUES(product_id), " +
            "settlement_time = VALUES(settlement_time), " +
            "seller = VALUES(seller), " +
            "price = VALUES(price), " +
            "product_count = VALUES(product_count), " +
            "return_count = VALUES(return_count), " +
            "revenue = VALUES(revenue), " +
            "ad_fee = VALUES(ad_fee), " +
            "device_type = VALUES(device_type), " +
            "conversion_type = VALUES(conversion_type)," +
            "order_status = VALUES(order_status), " +
            "update_uid = VALUES(create_uid) " +
            "</script>")
    void insertByUk(@Param("list") List<CpsAmazonEarningsExcelDTO> list, @Param("userId") Integer userId);


    /**
     * 更新 feeEarningId
     *
     * @param feeOrderId 订单ID
     * @param id         主键ID
     */
    @Update("UPDATE `cps_fee_earning` SET fee_order_id = #{feeOrderId} WHERE id = #{id} AND fee_order_id = 0 ")
    void updateFeeOrderId(@Param("feeOrderId") Long feeOrderId, @Param("id") Long id);
}
