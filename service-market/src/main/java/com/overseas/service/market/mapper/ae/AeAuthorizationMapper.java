package com.overseas.service.market.mapper.ae;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.ae.AeAppAuthorizationDTO;
import com.overseas.service.market.entity.ae.AeAuthorization;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AeAuthorizationMapper extends BaseMapper<AeAuthorization> {

    @Select("SELECT aa.id,app_id,app_key,app_secret,access_token " +
            "FROM ae_authorization AS aa INNER JOIN ae_app AS app ON app.id = aa.app_id " +
            "${ew.customSqlSegment} ")
    List<AeAppAuthorizationDTO> listAppAuthorization(@Param(ConstantUtils.WRAPPER) Wrapper<AeAuthorization> wrapper);
}
