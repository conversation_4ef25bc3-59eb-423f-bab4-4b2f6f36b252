package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.service.market.entity.PlanDirect;
import com.overseas.common.utils.ConstantUtils;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface PlanDirectMapper extends BaseMapper<PlanDirect> {

    /**
     * 判断标签是否被使用，且不被删除
     *
     * @param tagId 标签iD
     * @return 返回数据
     */
    @Select("SELECT m_order.id FROM m_order_direct " +
            " LEFT JOIN m_order ON m_order.id = m_order_direct.order_id " +
            " WHERE m_order_direct.direct_id = 1006 AND m_order_direct.direct_value LIKE CONCAT('%\"',#{tagId},'\"%')" +
            " AND m_order.is_del = 0 ")
    List<Long> selectTagUseInOrder(@Param("tagId") Long tagId);

    /**
     * 判断标签是否被使用，且投放状态不为投放结束，不被删除
     *
     * @param tagId 标签ID
     * @return 返回引用账户
     */
    @Select("SELECT m_order.master_id FROM m_order_direct " +
            " LEFT JOIN m_order ON m_order.id = m_order_direct.order_id " +
            " WHERE m_order_direct.direct_id = 1006 AND m_order_direct.direct_value LIKE CONCAT('%\"',#{tagId},'\"%')" +
            " AND m_order.is_del = 0 AND m_order.order_status <> 4 ")
    List<Integer> selectTagUseInOrderNotStop(@Param("tagId") Long tagId);

    /**
     * 判断下载应用是否在未删除订单中使用
     *
     * @param behaviorAppId 应用ID
     * @return 返回数据
     */
    @Select("SELECT m_order_direct.* FROM m_order_direct " +
            " LEFT JOIN m_order ON m_order_direct.order_id = m_order.id " +
            " WHERE direct_id = 1021 AND  direct_value = #{behaviorAppId} " +
            " AND m_order.is_del = 0 limit 1")
    PlanDirect selectBehaviorAppUseInOrder(@Param("behaviorAppId") Long behaviorAppId);


    /**
     * 获取一条定向数据
     *
     * @param wrapper 筛选条件
     * @return 返回数据
     */
    @Select("select * from m_order_direct   ${ew.customSqlSegment} limit 1")
    PlanDirect limitOne(@Param(ConstantUtils.WRAPPER) Wrapper<PlanDirect> wrapper);

    @Insert("<script> " +
            "INSERT INTO m_plan_direct (`plan_id`,`direct_id`,`direct_value`,`include`,`create_uid`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.planId},#{item.directId},#{item.directValue},#{item.include},#{userId}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "direct_value = VALUES(direct_value), " +
            "update_uid = #{userId}" +
            "</script>")
    void batchSavePlanDirect(@Param("list") List<PlanDirect> list, @Param("userId") Integer userId);
}
