package com.overseas.service.market.mapper.ae;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.service.market.entity.ae.AeProduct;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AeProductMapper extends BaseMapper<AeProduct> {

    @Insert("<script> " +
            "INSERT INTO ae_product (`product_id`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.productId}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "product_status = VALUES(product_status) " +
            "</script>")
    void batchSaveAeProduct(@Param("list") List<AeProduct> list);
}
