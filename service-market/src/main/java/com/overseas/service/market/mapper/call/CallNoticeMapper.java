package com.overseas.service.market.mapper.call;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.service.market.entity.call.CallNotice;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 **/
public interface CallNoticeMapper extends BaseMapper<CallNotice> {

    /**
     * 插入计划变更记录
     *
     * @param planId 计划ID
     */
    @Insert("INSERT INTO `iflytek_overseas_dsp`.`m_call_notice` (`source_type`, `source_id`) VALUES ( 1, #{planId}); ")
    public void callSheinPlanUpdate(@Param("planId") Long planId);
}
