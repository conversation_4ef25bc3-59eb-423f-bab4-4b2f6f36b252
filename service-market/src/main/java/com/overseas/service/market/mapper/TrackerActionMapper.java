package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.service.market.entity.ProjectActionMap;
import com.overseas.service.market.entity.TrackerAction;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.common.vo.sys.project.ProjectSetActionMapVO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TrackerActionMapper extends BaseMapper<TrackerAction> {


    @Insert("<script>" +
            " INSERT INTO `iflytek_overseas_info`.`project_action_map`" +
            " ( `project`, `event_key`, `source`, `source_name`, `action`, `m_version`, `is_del`, `create_uid`) " +
            " VALUES" +
            " <foreach collection='list' item='item' index='index' separator=','> " +
            " (#{item.project}, #{item.eventKey}, #{item.source}, #{item.sourceName}, #{item.action}, 1, 0, #{item.createUid} )" +
            "</foreach> " +
            "</script>")
    void insertProjectAction(@Param("list") List<ProjectActionMap> item);

    /**
     * @param project
     * @return
     */

    @Select(" SELECT pam.event_key, pam.source, pam.source_name, pam.action, IF(dta.project_id = 0 , 1, 0) AS is_common" +
            " FROM iflytek_overseas_info.project_action_map pam " +
            " LEFT JOIN iflytek_overseas_dsp.d_tracker_action dta ON pam.action = dta.source_action " +
            " WHERE pam.project = #{project} AND pam.is_del = 0")
    List<ProjectSetActionMapVO.ActionMapVO> listProjectActionMap(@Param("project") String project);

    /**
     * 获取条件总数
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select(" SELECT count(1) FROM iflytek_overseas_info.project_action_map" +
            " ${ew.customSqlSegment}")
    long countByMapInfo(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT action.id,action.action_name AS `name` " +
            "FROM d_tracker_action AS action " +
            "LEFT JOIN m_monitor_event AS mme ON action.source_action = mme.target_type " +
            "${ew.customSqlSegment}")
    List<SelectDTO> selectOptimizeTarget(@Param(ConstantUtils.WRAPPER) Wrapper<TrackerAction> wrapper);
}
