package com.overseas.service.market.mapper.assetTask;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.assetTask.AssetTaskProductDTO;
import com.overseas.service.market.dto.assetTask.AssetTaskProductSyncDTO;
import com.overseas.service.market.entity.assetTask.AssetTaskProduct;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface AssetTaskProductMapper extends BaseMapper<AssetTaskProduct> {


    @Select("SELECT " +
            "matp.id,matp.asset_task_id,matp.product_id,cp.product_title,matp.tracking_id, mti.tracking_id as tracking_id_val," +
            "matp.product_url,matp.product_cps_url," +
            "matp.asset_product_type,matp.asset_product_type,matp.asset_product_status " +
            "FROM " +
            "m_asset_task_product AS matp " +
            "LEFT JOIN cps_product AS cp ON cp.id = matp.product_id " +
            "LEFT JOIN m_tracking_id AS mti ON matp.tracking_id = mti.id " +
            "${ew.customSqlSegment}")
    List<AssetTaskProductDTO> listAssetTaskProduct(@Param(ConstantUtils.WRAPPER) QueryWrapper<AssetTaskProduct> wrapper);


    @Select("SELECT  matp.*, mat.tracking_id as task_tracking_id , mat.project_id, mat.asset_task_type " +
            "from m_asset_task_product AS matp " +
            "LEFT JOIN m_asset_task mat ON matp.asset_task_id = mat.id " +
            "${ew.customSqlSegment} ")
    List<AssetTaskProductSyncDTO> listAssetSync(@Param(ConstantUtils.WRAPPER) QueryWrapper<?> wrapper);


    @Update("UPDATE m_asset_task_product SET product_id = #{productId}, sync_count = sync_count + 1 WHERE id= #{id} ")
    void updateAssetSync(@Param("productId") Long productId, @Param("id") Long id);
}
