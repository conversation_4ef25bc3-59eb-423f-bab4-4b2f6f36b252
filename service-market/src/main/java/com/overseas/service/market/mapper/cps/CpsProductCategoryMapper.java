package com.overseas.service.market.mapper.cps;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.cps.CpsProductAnalysisListDTO;
import com.overseas.service.market.dto.cps.productCategory.ProductCategoryDTO;
import com.overseas.service.market.entity.CpsOrder;
import com.overseas.service.market.entity.cps.CpsProductCategory;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface CpsProductCategoryMapper extends BaseMapper<CpsProductCategory> {

    @Select("SELECT category_id AS id, category_name AS `name` " +
            "FROM cps_product_category ${ew.customSqlSegment}")
    List<SelectDTO> selectFirstCategory(@Param(ConstantUtils.WRAPPER) Wrapper<CpsProductCategory> wrapper);


    @Select("SELECT unity_category_id from cps_product_category_unity_mapping " +
            "${ew.customSqlSegment} ")
    List<Long> selectCategoryLabel(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 批量添加数据
     * @param list 数据
     */
    @Insert("<script>" +
            "INSERT INTO `cps_product_category` " +
            "(`project_id`, `parent_id`, `category_id`, `category_name`, `category_level`, `category_display_name`, `category_country`)" +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.projectId},#{item.parentId},#{item.categoryId},#{item.categoryName},#{item.categoryLevel},#{item.categoryDisplayName},#{item.categoryCountry}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "category_name = VALUES(category_name), " +
            "parent_id = VALUES(parent_id), " +
            "category_level = VALUES(category_level) " +
            "</script>")
    void insertByUk(@Param("list") List<CpsProductCategory> list);

    @Select("SELECT cat1.category_id AS first_category_id,cat1.category_name AS first_category_name," +
            "cat2.category_id AS second_category_id,cat2.category_name AS second_category_name," +
            "cat3.category_id AS third_category_id,cat3.category_name AS third_category_name," +
            "cat4.category_id AS fourth_category_id,cat4.category_name AS fourth_category_name " +
            "FROM cps_product_category AS cat4 " +
            "INNER JOIN cps_product_category AS cat3 ON cat3.category_id = cat4.parent_id AND cat4.category_level=4 " +
            "INNER JOIN cps_product_category AS cat2 ON cat2.category_id = cat3.parent_id " +
            "INNER JOIN cps_product_category AS cat1 ON cat1.category_id=cat2.parent_id " +
            "${ew.customSqlSegment}")
    List<ProductCategoryDTO> listProductCategory(@Param(ConstantUtils.WRAPPER) Wrapper<CpsProductCategory> wrapper);

    @Update("UPDATE cps_product_category SET category_id = id, category_name = category_display_name " +
            "WHERE project_id = 18 AND id != category_id")
    void updateLazadaCategory();
}
