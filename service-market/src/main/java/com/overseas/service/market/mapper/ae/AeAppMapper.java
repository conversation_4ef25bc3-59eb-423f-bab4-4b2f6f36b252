package com.overseas.service.market.mapper.ae;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.cps.app.CpsAppListDTO;
import com.overseas.service.market.dto.trackingGroup.TrackingGroupListDTO;
import com.overseas.service.market.entity.AssetGroupMaster;
import com.overseas.service.market.entity.ae.AeApp;
import com.overseas.service.market.entity.assetTask.TrackingId;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AeAppMapper extends BaseMapper<AeApp> {

    @Select("SELECT aa.id AS cps_app_id,aa.project_id,mp.project_name,aa.app_name,aa.app_key,aa.app_secret,aa.email " +
            "FROM ae_app AS aa " +
            "INNER JOIN m_project AS mp ON mp.id=aa.project_id " +
            "${ew.customSqlSegment}")
    IPage<CpsAppListDTO> listCpsApp(IPage<TrackingGroupListDTO> page,
                                   @Param(ConstantUtils.WRAPPER) Wrapper<AeApp> wrapper);

    @Select("SELECT id,app_name AS `name` FROM ae_app ${ew.customSqlSegment}")
    List<SelectDTO> selectCpsApp(@Param(ConstantUtils.WRAPPER) Wrapper<AeApp> wrapper);
}
