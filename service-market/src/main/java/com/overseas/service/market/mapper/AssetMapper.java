package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.market.asset.*;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.openApi.creativeUnit.OpenApiAssetListDTO;
import com.overseas.service.market.entity.Asset;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-24 9:24
 */
public interface AssetMapper extends BaseMapper<Asset> {

    @Select("SELECT ma.id,mar.asset_name,ma.content AS `asset_path`,ma.format,ma.height,ma.width,CONCAT(width,'*',height) AS `size`, " +
            "ma.duration,ma.size AS `asset_size`,ma.cover_img_id,ma.is_upload,ma.asset_type, " +
            "mar.source_type,ma.create_time AS `upload_time`,ma.create_uid,uu.company_name AS create_name " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "INNER JOIN u_user AS uu ON ma.create_uid = uu.id " +
            "${ew.customSqlSegment}")
    List<AssetListDTO> getAssetList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ma.id,mar.asset_name,ma.content AS `asset_path`,ma.format,ma.height,ma.width,CONCAT(width,'*',height) AS `size`, " +
            "ma.duration,ma.size AS `asset_size`,ma.cover_img_id,ma.is_upload,ma.asset_type, " +
            "mar.source_type,ma.create_time AS `upload_time` " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "INNER JOIN m_material_asset AS mma ON mma.asset_id = ma.id " +
            "INNER JOIN m_creative_unit AS mcu ON mma.material_id = mcu.material_id " +
            "${ew.customSqlSegment}")
    List<AssetListDTO> getAssetListByUnit(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT DISTINCT ma.id,mar.asset_name,ma.content AS `asset_path`,ma.format,ma.height,ma.width," +
            "CONCAT(width,'*',height) AS `size`,ma.duration,ma.size AS `asset_size`,ma.is_used,ma.cover_img_id," +
            "ma.is_upload,mar.source_type,ma.create_time AS `upload_time`,ma.create_uid,uu.company_name AS create_name, " +
            "mar.is_master_used " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "INNER JOIN u_user AS uu ON ma.create_uid = uu.id " +
            "LEFT JOIN m_asset_resource AS mar2 ON mar2.asset_id = ma.id AND mar2.is_del = 0 AND mar2.source_type = 2 " +
            "${ew.customSqlSegment}")
    IPage<AssetListDTO> listAssetByPage(IPage<AssetListDTO> page, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 获取素材标签列表分页数据
     *
     * @param page    分页
     * @param wrapper 传入参数
     * @return 返回数据
     */
    @Select("SELECT ma.id,mar.asset_name,ma.content AS `asset_path`,ma.format,ma.height,ma.width,CONCAT(width,'*',height) AS `size`, " +
            "ma.duration,ma.size AS `asset_size`,ma.cover_img_id,ma.is_upload,ma.asset_type,  " +
            "mar.source_type,ma.create_time AS `upload_time`,ma.create_uid,uu.company_name AS create_name, mar.is_master_used " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "INNER JOIN u_user AS uu ON ma.create_uid = uu.id " +
            "${ew.customSqlSegment}")
    IPage<AssetListDTO> getAssetPage(IPage<AssetListDTO> page, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ma.id,mar.asset_name,ma.content AS `asset_path`,ma.format,ma.height,ma.width,CONCAT(width,'*',height) AS `size`, " +
            "ma.duration,ma.size AS `asset_size`,ma.cover_img_id,ma.is_upload,ma.asset_type, " +
            "mar.source_type,ma.create_time AS `upload_time`,ma.create_uid,uu.company_name AS create_name, mar.is_master_used " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "INNER JOIN u_user AS uu ON ma.create_uid = uu.id " +
            "INNER JOIN m_material_asset AS mma ON mma.asset_id = ma.id " +
            "INNER JOIN m_creative_unit AS mcu ON mma.material_id = mcu.material_id " +
            "${ew.customSqlSegment}")
    IPage<AssetListDTO> getAssetPageByUnit(IPage<AssetListDTO> page, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 获取素材关联的创意单元数
     *
     * @param wrapper 传入参数
     * @return 返回数据
     */
    @Select("SELECT ma.id AS `asset_id`,COUNT(ma.id) AS `creative_unit_num` " +
            "FROM `m_asset` AS ma " +
            "INNER JOIN `m_material_asset` AS mma ON ma.id = mma.asset_id " +
            "INNER JOIN `m_creative_unit` AS mcu ON mma.material_id = mcu.material_id " +
            "INNER JOIN `m_campaign` AS mc ON mcu.campaign_id = mc.id " +
            "INNER JOIN `m_plan` AS mp ON mcu.plan_id = mp.id " +
            "${ew.customSqlSegment}")
    List<AssetCreativeUnitNumDTO> getAssetCreativeUnitNumList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 获取素材尺寸下拉数据
     *
     * @param wrapper 传入参数
     * @return 返回数据
     */
    @Select("SELECT CONCAT(ma.width,'*',ma.height) AS `key`,CONCAT(ma.width,'*',ma.height) AS `title` " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "${ew.customSqlSegment}")
    List<SelectDTO3> getAssetSizeSelect(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 获取素材尺寸下拉数据
     *
     * @param wrapper 传入参数
     * @return 返回数据
     */
    @Select("SELECT ma.id AS `id`,CONCAT(ma.width,'*',ma.height) AS `name` " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "${ew.customSqlSegment}")
    List<SelectDTO> selectAssetSize(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 获取素材名称及地址
     *
     * @param wrapper 传入参数
     * @return 返回数据
     */
    @Select("SELECT ma.id,ma.content AS `asset_path`,mar.asset_name " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "${ew.customSqlSegment}")
    List<AssetNamePathDTO> getAssetPathList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 根据信息获取素材资产ID
     *
     * @param wrapper 筛选条件
     * @return 返回数据
     */
    @Select("SELECT ma.id " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "${ew.customSqlSegment}")
    List<Long> getAssetIdsByInfo(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 查询素材元素信息
     *
     * @param wrapper 查询参数
     * @return 结果集
     */
    @Select("SELECT id,content AS `name` FROM m_asset " +
            "${ew.customSqlSegment}")
    List<SelectDTO> listAsset(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ma.asset_type " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "${ew.customSqlSegment}")
    List<AssetCountDTO> getAssetCount(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ma.id " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "${ew.customSqlSegment}")
    List<Long> getAssetIdsBySearch(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ma.id,mar.asset_name,ma.content AS asset_path,ma.is_upload,ma.asset_type " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "${ew.customSqlSegment}")
    List<AssetBaseDTO> getAssetInfoByIds(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT mar.c_id " +
            "FROM m_asset AS ma " +
            "INNER JOIN m_asset_resource AS mar ON ma.id = mar.asset_id " +
            "${ew.customSqlSegment}")
    List<String> listAssetCId(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT mma.asset_id,mma.asset_name,ma.asset_type,ma.content,mma.field_type,ma.width,ma.height,ma.size " +
            "FROM m_creative_unit AS mcu " +
            "INNER JOIN m_material_asset AS mma ON mma.material_id=mcu.material_id " +
            "INNER JOIN m_asset AS ma ON ma.id=mma.asset_id " +
            "${ew.customSqlSegment}")
    List<OpenApiAssetListDTO> listOpenApiCreativeUnitAsset(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 根据文本组合获取创意单元数量
     *
     * @param titleIds  标题
     * @param descIds   描述
     * @param concatIds 标题Ids
     * @param wrapper   条件
     * @return 返回数据
     */
    @Select("SELECT count(1) as creative_unit_num ,title_id, desc_id FROM m_creative_unit mcu " +
            "LEFT JOIN ( " +
            "  SELECT " +
            "   t1.asset_id AS title_id, IFNULL(t2.asset_id, 0) AS desc_id, t1.material_id  " +
            "  FROM " +
            "   (SELECT * FROM m_material_asset WHERE asset_id IN (${titleIds}) AND field_type = 3) t1 " +
            "   LEFT JOIN (SELECT * FROM m_material_asset WHERE asset_id IN (${descIds}) AND field_type = 4) t2 ON t1.material_id = t2.material_id  " +
            "  WHERE " +
            " (t1.asset_id, IFNULL(t2.asset_id, 0)) IN ( ${concatIds})) AS ma ON mcu.material_id = ma.material_id " +
            " LEFT JOIN m_plan mp ON mcu.plan_id = mp.id " +
            " LEFT JOIN m_campaign mc ON mcu.campaign_id = mc.id  " +
            "${ew.customSqlSegment}")
    List<AssetCreativeUnitNumDTO> getTextCombineCreativeUnitNumList(@Param("titleIds") String titleIds, @Param("descIds") String descIds,
                                                                    @Param("concatIds") String concatIds, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
