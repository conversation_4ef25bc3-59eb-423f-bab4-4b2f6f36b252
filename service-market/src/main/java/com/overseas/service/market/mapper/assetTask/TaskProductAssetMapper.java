package com.overseas.service.market.mapper.assetTask;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.dto.market.asset.AssetDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.assetTask.AssetCpsAppIdDTO;
import com.overseas.service.market.dto.assetTask.AssetTaskMaterialDTO;
import com.overseas.service.market.dto.assetTask.AssetTaskNameDTO;
import com.overseas.service.market.dto.assetTask.AssetTaskProductLinkDTO;
import com.overseas.service.market.entity.assetTask.TaskProductAsset;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TaskProductAssetMapper extends BaseMapper<TaskProductAsset> {

    @Select("SELECT " +
            "mtpa.id,mtpa.task_product_id,mtpa.asset_id,ma.md5,ma.asset_type,ma.content " +
            "FROM " +
            "m_task_product_asset AS mtpa " +
            "INNER JOIN m_asset AS ma ON ma.id = mtpa.asset_id " +
            "${ew.customSqlSegment}")
    List<AssetTaskMaterialDTO> getAssetTaskMaterials(@Param(ConstantUtils.WRAPPER) Wrapper<TaskProductAsset> wrapper);


    @Select("SELECT mtpa.asset_id," +
            " matp.product_id," +
            " matp.product_url " +
            "FROM m_task_product_asset mtpa " +
            "LEFT JOIN m_asset_task_product matp ON mtpa.task_product_id = matp.id " +
            "${ew.customSqlSegment} ")
    List<AssetTaskProductLinkDTO> getAssetLinkByInfo(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT mtpa.asset_id,mat.tracking_id,mti.cps_app_id " +
            "FROM m_task_product_asset AS mtpa " +
            "INNER JOIN m_asset_task AS mat ON mat.id=mtpa.asset_task_id " +
            "INNER JOIN m_tracking_id AS mti ON mti.id=mat.tracking_id " +
            "${ew.customSqlSegment}")
    List<AssetCpsAppIdDTO> listAssetCpsAppId(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    /**
     * 获取 素材素材组名称
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT mtpa.asset_id, mtpa.asset_task_id , mat.asset_task_name " +
            "from m_task_product_asset mtpa " +
            "left join m_asset_task_product matp ON mtpa.task_product_id = matp.id " +
            "left join m_asset_task mat ON mtpa.asset_task_id = mat.id " +
            "${ew.customSqlSegment}")
    List<AssetTaskNameDTO> listAssetTaskName(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);



    /**
     * 获取素材信息
     *
     * @param wrapper 条件
     * @return 返会数据
     */
    @Select("SELECT ma.id as asset_id, mar.asset_name, ma.* " +
            "FROM m_task_product_asset mtpa " +
            "LEFT JOIN m_asset ma ON mtpa.asset_id = ma.id " +
            "LEFT JOIN m_asset_resource mar ON mar.asset_id = mtpa.asset_id AND mar.master_id = #{masterId} " +
            "LEFT JOIN m_asset_task_product matp ON matp.id = mtpa.task_product_id " +
            "${ew.customSqlSegment}")
    List<AssetDTO> getAssetsByWrapper(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper, @Param("masterId") Integer masterId);
}
