package com.overseas.service.market.mapper.cps;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.dto.market.cps.amazon.CpsAmazon2OrderDTO;
import com.overseas.common.dto.market.cps.amazon.CpsAmazonOrderExcelDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.common.vo.market.cps.CpsSaveVO;
import com.overseas.service.market.entity.CpsFeeOrder;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface CpsFeeOrderMapper extends BaseMapper<CpsFeeOrder> {

    /**
     * 插入数据
     *
     * @param list 数据
     */
    @Insert("<script> " +
            "INSERT INTO `cps_fee_order` (`country`, `time_zone`," +
            "`cate_name`, `order_name`, `tracking_id`," +
            "`product_id`, `order_time`, `product_count`, `price`," +
            "`link_type`, `indirect_sales`, `device_type`, `idx`, `is_sync`, `create_uid`)  " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{cps.country}, #{cps.timeZone}, " +
            "#{item.cateName}, #{item.orderName}, #{item.trackingId}," +
            "#{item.productId}, #{item.orderTime}, #{item.productCount}, #{item.price}," +
            "#{item.linkType}, #{item.indirectSales}, #{item.deviceType}, #{item.idx}, 0,  #{userId}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "country = VALUES(country), " +
            "time_zone = VALUES(time_zone), " +
            "cate_name = VALUES(cate_name), " +
            "order_name = VALUES(order_name), " +
            "tracking_id = VALUES(tracking_id), " +
            "product_id = VALUES(product_id), " +
            "order_time = VALUES(order_time), " +
            "product_count = VALUES(product_count), " +
            "price = VALUES(price), " +
            "link_type = VALUES(link_type), " +
            "indirect_sales = VALUES(indirect_sales), " +
            "device_type = VALUES(device_type), " +
            "is_sync = VALUES(is_sync), " +
            "update_uid = VALUES(create_uid) " +
            "</script>")
    void insertByUk(@Param("list") List<CpsAmazonOrderExcelDTO> list, @Param("cps") CpsSaveVO cpsSaveVO, @Param("userId") Integer userId);

    /**
     * 更新 earning_product
     *
     * @param earningProduct 佣金ID
     * @param id             主键ID
     */
    @Update("UPDATE `cps_fee_order` SET earning_product = earning_product + #{earningProduct}, " +
            "earning_return = earning_return + #{earningReturn} WHERE id = #{id}  ")
    void updateFeeEarningProduct(@Param("earningProduct") Integer earningProduct, @Param("earningReturn") Integer earningReturn, @Param("id") Long id);

    /***
     * 获取需要同步数据
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT " +
            " cfo.cate_name," +
            " cfo.order_name, " +
            " cfo.country, " +
            " cfo.time_zone, " +
            " cfo.tracking_id, " +
            " cfo.product_id, " +
            " cfo.order_time, " +
            " cfo.price AS product_price," +
            " cfo.indirect_sales," +
            " cfo.link_type, " +
            " cfo.device_type, " +
            " cfo.earning_product," +
            " cfo.earning_return," +
            " cfe.fee_order_id, " +
            " cfe.settlement_time, " +
            " cfe.seller," +
            " cfe.price as earning_price, " +
            " SUM( cfe.revenue ) AS revenue, " +
            " SUM( cfe.ad_fee ) AS ad_fee, " +
            " cfe.conversion_type, " +
            " cfe.order_status  " +
            "FROM " +
            " cps_fee_earning cfe " +
            "LEFT JOIN cps_fee_order cfo ON cfe.fee_order_id = cfo.id " +
            "${ew.customSqlSegment}")
    List<CpsAmazon2OrderDTO> listOrderAndEarning(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);



    /***
     * 获取需要同步数据
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT " +
            " cfo.cate_name," +
            " cfo.order_name, " +
            " cfo.country, " +
            " cfo.time_zone, " +
            " cfo.tracking_id, " +
            " cfo.product_id, " +
            " cfo.order_time, " +
            " cfo.price AS product_price," +
            " cfo.indirect_sales," +
            " cfo.link_type, " +
            " cfo.device_type, " +
            " cfo.product_count as earning_product," +
            " cfo.earning_return," +
            " cfo.id as fee_order_id, " +
            " 0 as settlement_time, " +
            " '' as seller," +
            " cfo.price as earning_price, " +
            " 0 AS revenue, " +
            " 0 AS ad_fee, " +
            " '' as conversion_type, " +
            " 2 as order_status  " +
            "FROM " +
            " cps_fee_order cfo " +
            "${ew.customSqlSegment}")
    List<CpsAmazon2OrderDTO> listOrderNoEarning(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

}
