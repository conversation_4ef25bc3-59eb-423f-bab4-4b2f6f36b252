package com.overseas.service.market.mapper.cps;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.service.market.entity.cps.CpsProductMaterial;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CpsProductMaterialMapper extends BaseMapper<CpsProductMaterial> {

    @Insert("<script> " +
            "INSERT INTO cps_product_material (`product_id`,`material_md5`,`material_type`,`material_url`," +
            "`material_width`,`material_height`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.productId},#{item.materialMd5},#{item.materialType},#{item.materialUrl},#{item.materialWidth}," +
            "#{item.materialHeight}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "material_type = VALUES(material_type), " +
            "material_width = VALUES(material_width), " +
            "material_height = VALUES(material_height) " +
            "</script>")
    void batchInsertMaterial(@Param("list") List<CpsProductMaterial> list);
}
