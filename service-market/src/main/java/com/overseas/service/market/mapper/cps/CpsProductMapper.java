package com.overseas.service.market.mapper.cps;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.cps.CpsProductAnalysisListDTO;
import com.overseas.service.market.dto.cps.CpsProductListDTO;
import com.overseas.service.market.entity.CpsOrder;
import com.overseas.service.market.entity.cps.CpsProduct;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface CpsProductMapper extends BaseMapper<CpsProduct> {

    /**
     * 获取AE商品分页数据
     *
     * @param page    分页参数对象
     * @param wrapper 查询参数对象
     * @return 分页信息结果集
     */
    @Select("SELECT cp.id,cp.project_id,cp.product_sign,cp.product_language,cp.product_type,cp.product_detail_url," +
            "cp.product_title,cp.product_price,cp.product_price_currency,cp.product_discount,cp.discounted_price," +
            "cp.discounted_price_currency,cp.product_brand,cp.first_category_id,cp.second_category_id," +
            "0 AS third_category_id,0 AS fourth_category_id,cp.inventory," +
            "cp.shop_id,cpi.shop_url,cp.create_time,cpi.lastest_volume AS sales_volume,'-' AS feedback_count " +
            "FROM cps_product AS cp " +
            "INNER JOIN cps_product_ae AS cpi ON cpi.product_id = cp.id " +
            "INNER JOIN m_project AS mp ON mp.id = cp.project_id " +
            "${ew.customSqlSegment}")
    IPage<CpsProductListDTO> listAeProduct(IPage<CpsProductListDTO> page,
                                           @Param(ConstantUtils.WRAPPER) Wrapper<CpsProduct> wrapper);

    /**
     * 获取亚马逊商品分页数据
     *
     * @param page    分页参数对象
     * @param wrapper 查询参数对象
     * @return 分页信息结果集
     */
    @Select("SELECT cp.id,cp.project_id,cp.product_sign,cp.product_language,cp.product_type,cp.product_detail_url," +
            "cp.product_title,cp.product_price,cp.product_price_currency,cp.product_discount,cp.discounted_price," +
            "cp.discounted_price_currency,cp.product_brand,cp.first_category_id,cp.second_category_id," +
            "cp.third_category_id,cp.fourth_category_id,cp.inventory," +
            "cp.shop_id,cp.create_time,'' AS shop_url,'-' AS sales_volume,cpi.merchant_feedback_count AS feedback_count " +
            "FROM cps_product AS cp " +
            "INNER JOIN cps_product_amazon AS cpi ON cpi.product_id = cp.id " +
            "INNER JOIN m_project AS mp ON mp.id = cp.project_id " +
            "${ew.customSqlSegment}")
    IPage<CpsProductListDTO> listAmazonProduct(IPage<CpsProductListDTO> page,
                                               @Param(ConstantUtils.WRAPPER) Wrapper<CpsProduct> wrapper);


    /**
     * 获取亚马逊商品分页数据
     *
     * @param page    分页参数对象
     * @param wrapper 查询参数对象
     * @return 分页信息结果集
     */
    @Select("SELECT cp.id,cp.project_id,cp.product_sign,cp.product_language,cp.product_type,cp.product_detail_url," +
            "cp.product_title,cp.product_price,cp.product_price_currency,cp.product_discount,cp.discounted_price," +
            "cp.discounted_price_currency,cp.product_brand,cp.first_category_id,cp.second_category_id," +
            "cp.third_category_id,cp.fourth_category_id,cp.inventory," +
            "cp.shop_id,cp.create_time,'' AS shop_url,'-' AS sales_volume " +
            "FROM cps_product AS cp " +
            "INNER JOIN m_project AS mp ON mp.id = cp.project_id " +
            "${ew.customSqlSegment}")
    IPage<CpsProductListDTO> listLazadaProduct(IPage<CpsProductListDTO> page,
                                               @Param(ConstantUtils.WRAPPER) Wrapper<CpsProduct> wrapper);

    /**
     * 列表数据
     *
     * @param page    分页
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("select * from (" +
            "select " +
            "co.project_id,co.plan_id,co.creative_unit_id,co.order_status,mp.plan_name,co.tracking_id, " +
            "co.product_id, cp.product_title as product_name," +
            "FROM_UNIXTIME(co.order_date, '%Y-%m-%d') as order_date," +
            "mp.campaign_id,mc.campaign_name,co.country as country," +
            "cp.first_category_id,cp.second_category_id,cp.third_category_id,cp.fourth_category_id," +
            "cp.leaf_category_id,co.conversion_type," +
            "count(1) as order_count," +
            "sum(co.product_count) as product_count," +
            "sum(co.estimate_amount) as estimate_amount," +
            "sum(co.estimate_commission) as estimate_commission," +
            "sum(co.actual_amount) as actual_amount," +
            "sum(co.actual_commission) as actual_commission," +
            "sum(co.new_buyer_commission) as new_buyer_commission," +
            "SUM(IF(order_status!=4, 1, 0)) as valid_order_count, " +
            "(sum(co.estimate_amount) - sum(IF(order_status=4,estimate_amount,0))) as valid_amount, " +
            "ROUND((sum(co.estimate_amount) - sum(IF(order_status=4,estimate_amount,0))) / SUM(IF(order_status!=4, 1, 0)),2) as valid_order_amount, " +
            "count(DISTINCT cp.first_category_id) as first_product_category_count, " +
            "count(DISTINCT cp.second_category_id) as sencond_product_category_count ," +
            "SUM(IF(order_status=4, 1, 0)) as return_order_count," +
            "SUM(IF(order_status=4, estimate_amount, 0)) as return_order_amount, " +
            "SUM(IF(order_status=4, estimate_commission, 0)) as return_order_commission," +
            "SUM(IF(conversion_type = 1, 1, 0)) as direct_count, " +
            "ROUND(SUM(IF(conversion_type = 1, 1, 0)) * 100 / count(1),2) as direct_rate," +
            "ROUND(SUM(actual_commission) * 100 / SUM(estimate_commission)) AS commission_rate " +
            "from " +
            "cps_order co " +
            "inner join m_plan mp on mp.id = co.plan_id " +
            "inner join m_campaign mc on mc.id = mp.campaign_id " +
            "left join cps_product cp on cp.product_sign = co.product_id " +
            "${ew.customSqlSegment} " +
            ") as r ${ew2.customSqlSegment} ")
    IPage<CpsProductAnalysisListDTO> listProductAnalysis(IPage<CpsProductAnalysisListDTO> page,
                                                         @Param(ConstantUtils.WRAPPER) Wrapper<CpsOrder> wrapper,
                                                         @Param("ew2") Wrapper<?> wrapper2);

    /**
     * 总计
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("select " +
            "0 as project_id, 0 as plan_id, 0 as creative_unit_id, co.order_status, mp.plan_name, 0 as tracking_id, " +
            "0 as product_id, '' as order_date," +
            "0 as campaign_id, mc.campaign_name,co.country as country," +
            "0 as first_category_id, 0 as second_category_id, 0 as leaf_category_id," +
            "0 as third_category_id, 0 as fourth_category_id," +
            "co.conversion_type," +
            "count(1) as order_count," +
            "sum(co.product_count) as product_count," +
            "sum(co.estimate_amount) as estimate_amount," +
            "sum(co.estimate_commission) as estimate_commission," +
            "sum(co.actual_amount) as actual_amount," +
            "sum(co.actual_commission) as actual_commission," +
            "sum(co.new_buyer_commission) as new_buyer_commission, " +
            "SUM(IF(order_status!=4, 1, 0)) as valid_order_count," +
            "(sum(co.estimate_amount) - sum(IF(order_status=4,estimate_amount,0))) as valid_amount, " +
            "ROUND((sum(co.estimate_amount) - sum(IF(order_status=4,estimate_amount,0))) / SUM(IF(order_status!=4, 1, 0)),2) as valid_order_amount, " +
            "count(DISTINCT cp.first_category_id) as first_product_category_count, " +
            "count(DISTINCT cp.second_category_id) as sencond_product_category_count ," +
            "SUM(IF(order_status=4, 1, 0)) as return_order_count," +
            "SUM(IF(order_status=4, estimate_amount, 0)) as return_order_amount, " +
            "SUM(IF(order_status=4, estimate_commission, 0)) as return_order_commission," +
            "SUM(IF(conversion_type = 1, 1, 0)) as direct_count, " +
            "ROUND(SUM(IF(conversion_type = 1, 1, 0)) * 100 / count(1),2) as direct_rate, " +
            "ROUND(SUM(actual_commission) * 100 / SUM(estimate_commission)) AS commission_rate " +
            "from " +
            "cps_order co " +
            "inner join m_plan mp on mp.id = co.plan_id " +
            "inner join m_campaign mc on mc.id = mp.campaign_id " +
            "left join cps_product cp on cp.product_sign = co.product_id " +
            "${ew.customSqlSegment}")
    CpsProductAnalysisListDTO totalProductAnalysis(@Param(ConstantUtils.WRAPPER) Wrapper<CpsOrder> wrapper);

    @Update("UPDATE cps_order SET order_date = UNIX_TIMESTAMP(FROM_UNIXTIME(pay_time, '%Y-%m-%d')) " +
            "WHERE project_id = 25 AND order_date = 0;")
    void updateAeCpsOrder();

    @Update("UPDATE cps_order SET order_date = UNIX_TIMESTAMP(FROM_UNIXTIME(order_time, '%Y-%m-%d')) " +
            "WHERE project_id = 26 AND order_date = 0;")
    void updateAmazonCpsOrder();
}
