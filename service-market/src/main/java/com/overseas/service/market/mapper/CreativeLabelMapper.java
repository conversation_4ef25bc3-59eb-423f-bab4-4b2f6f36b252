package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.service.market.entity.CreativeLabel;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.ConstantUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CreativeLabelMapper extends BaseMapper<CreativeLabel> {

    @Select("SELECT id,creative_label_name AS `name` FROM m_creative_label ${ew.customSqlSegment}")
    List<SelectDTO> selectCreativeLabel(@Param(ConstantUtils.WRAPPER) Wrapper<CreativeLabel> wrapper);
}
