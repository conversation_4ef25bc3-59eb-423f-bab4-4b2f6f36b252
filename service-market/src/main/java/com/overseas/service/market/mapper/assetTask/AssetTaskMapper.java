package com.overseas.service.market.mapper.assetTask;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.asset.AssetListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.market.dto.assetTask.AssetTaskListDTO;
import com.overseas.service.market.entity.assetTask.AssetTask;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AssetTaskMapper extends BaseMapper<AssetTask> {

    @Select("SELECT " +
            "mat.id,mat.master_id,master.company_name AS master_name,mat.asset_task_name,mat.asset_task_type," +
            "mat.asset_task_status,mat.tracking_id,mti.tracking_name,asset_size_id," +
            "COUNT(matp.id) AS product_count,mat.create_uid,uu.real_name AS create_user_name, " +
            "mat.designer_id,designer.real_name AS designer_user_name,mat.create_time " +
            "FROM " +
            "m_asset_task AS mat " +
            "LEFT JOIN m_asset_task_product AS matp ON matp.asset_task_id = mat.id " +
            "AND matp.asset_product_type != 4 AND matp.is_del = 0 AND matp.asset_product_status = 1 " +
            "LEFT JOIN m_tracking_id AS mti ON mti.id = mat.tracking_id " +
            "LEFT JOIN u_user AS uu ON uu.id = mat.create_uid " +
            "LEFT JOIN u_user AS master ON master.id = mat.master_id " +
            "LEFT JOIN u_user AS designer ON mat.designer_id = designer.id  " +
            "${ew.customSqlSegment}")
    IPage<AssetTaskListDTO> listAssetTask(IPage<AssetListDTO> page,
                                          @Param(ConstantUtils.WRAPPER) Wrapper<AssetTask> wrapper);

    @Select("SELECT id, asset_task_name AS `name` FROM m_asset_task ${ew.customSqlSegment}")
    List<SelectDTO> selectAssetTask(@Param(ConstantUtils.WRAPPER) Wrapper<AssetTask> wrapper);


    @Select("SELECT uu.id , uu.real_name as name from m_asset_task mat " +
            "left join u_user uu ON mat.create_uid = uu.id " +
            "${ew.customSqlSegment}")
    List<SelectDTO> selectAssetTaskUser(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT distinct mat.id FROM tm_asset_task mat " +
            "LEFT JOIN m_tracking_id mti ON mat.tracking_id = mti.id " +
            "${ew.customSqlSegment} ")
    List<Long> getTaskIdByAppId(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
