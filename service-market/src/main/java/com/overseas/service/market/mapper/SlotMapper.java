package com.overseas.service.market.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.service.market.dto.slot.SlotDirectDTO;
import com.overseas.service.market.entity.Slot;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.sys.resource.SlotMediaDTO;
import com.overseas.common.utils.ConstantUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SlotMapper extends BaseMapper<Slot> {

    /**
     * 查询广告位定向分页数据
     *
     * @param iPage   分页条件
     * @param wrapper 查询条件
     * @return 分页数据
     */
    @Select("SELECT DISTINCT slot.id AS `key`,slot.slot_name AS title,slot.adx_id,slot.os_type," +
            "FLOOR(slot.request_quantity/10000) AS request_quantity," +
            "adx.adx_name,media.id AS media_id,media.media_name,slot.floor_price " +
            "FROM d_slot slot " +
            "LEFT JOIN d_slot_app AS app ON app.slot_id = slot.id " +
            "LEFT JOIN d_media AS media ON app.media_id = media.id " +
            "LEFT JOIN d_adx AS adx ON slot.adx_id = adx.id " +
            "${ew.customSqlSegment}")
    IPage<SlotDirectDTO> listSlotDirect(IPage<SlotDirectDTO> iPage, @Param(ConstantUtils.WRAPPER) Wrapper<Slot> wrapper);

    @Select("SELECT ds.id,ds.slot_name AS `name` " +
            "FROM d_slot AS ds " +
            "LEFT JOIN d_slot_app AS dsa ON ds.id = dsa.slot_id " +
            "${ew.customSqlSegment}")
    List<SelectDTO> getSlotSelect(@Param(ConstantUtils.WRAPPER) Wrapper<Slot> wrapper);

    /**
     * 获取广告位对应的媒体信息
     *
     * @param wrapper 查询条件
     * @return 结果集
     */
    @Select("SELECT DISTINCT d_slot.id AS slot_id, d_slot.slot_name, d_media.id AS media_id,d_media.media_name " +
            "FROM d_slot INNER JOIN d_slot_app ON d_slot.id = d_slot_app.slot_id " +
            "INNER JOIN d_media ON d_media.id = d_slot_app.media_id " +
            "${ew.customSqlSegment}")
    List<SlotMediaDTO> getSlotMediaListBySlotIds(@Param(ConstantUtils.WRAPPER) Wrapper<Slot> wrapper);
}
