package com.overseas.service.monitor.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 **/
@Configuration
@Slf4j
public class ThreadConfig {


    @Value("${monitor_thread.pool.max_pool_size:20}")
    private Integer maxPoolSize;

    @Value("${monitor_thread.pool.core_pool_size:10}")
    private Integer corePoolSize;

    @Value("${monitor_thread.pool.alive_seconds:100000}")
    private Integer aliveSeconds;

    @Value("${monitor_thread.pool.queue_capacity:100}")
    private Integer queueCapacity;

    @Bean("putMonitorThreadPool")
    public ThreadPoolTaskExecutor threadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(maxPoolSize);
        executor.setCorePoolSize(corePoolSize);
        executor.setKeepAliveSeconds(aliveSeconds);
        executor.setQueueCapacity(queueCapacity);
        executor.setRejectedExecutionHandler((r, executor1) -> {
            log.error("task is too more, please check config");
            new ThreadPoolExecutor.CallerRunsPolicy().rejectedExecution(r, executor1);
        });
        return executor;
    }

}
