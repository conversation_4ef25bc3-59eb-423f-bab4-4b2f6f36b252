package com.overseas.service.monitor.vo.monitor.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 **/
@Data
public class PutMonitorChargeTimeVO {

    @ApiModelProperty("开始小时")
    @NotNull(message = "开始小时不能为空")
    @Range(min = 0, max = 23, message = "开始小时不合法")
    private Integer start;

    @ApiModelProperty("结束小时")
    @NotNull(message = "结束小时不能为空")
    @Range(min = 0, max = 23, message = "结束小时不合法")
    private Integer end;
}
