package com.overseas.service.monitor.vo.monitor.task;

import com.overseas.common.validation.annotation.MpEnum;
import com.overseas.service.monitor.enums.MonitorPutStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
public class PutMonitorTaskPutStatusVO {

    @ApiModelProperty("投放监控ID")
    @NotNull(message = "投放监控ID不能为空")
    private Long id;

    @ApiModelProperty("投放状态")
    @MpEnum(clazz = MonitorPutStatusEnum.class, message = "投放状态不合法")
    private Integer putStatus;

}
