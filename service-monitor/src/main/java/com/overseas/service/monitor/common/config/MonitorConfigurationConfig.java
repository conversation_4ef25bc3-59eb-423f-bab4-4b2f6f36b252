package com.overseas.service.monitor.common.config;

import com.overseas.service.monitor.common.configuration.AeConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 **/
@Configuration
@RefreshScope
public class MonitorConfigurationConfig {

    @Bean
    @ConfigurationProperties(value = "ae.config")
    public AeConfiguration aeConfiguration() {
        return new AeConfiguration();
    }
}
