package com.overseas.service.monitor.vo.monitor.task;

import com.overseas.common.validation.annotation.MpDate;
import com.overseas.common.vo.ListVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
public class PutMonitorTaskLogListVO extends ListVO {

    @ApiModelProperty("监控ID")
    @NotNull(message = "监控ID不能为空")
    private Long putMonitorId;

    @ApiModelProperty("开始时间")
    @MpDate(message = "开始时间不合法")
    private String startDate;

    @ApiModelProperty("结束时间")
    @MpDate(message = "结束时间不合法")
    private String endDate;

}
