package com.overseas.service.monitor.dto.monitor.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.overseas.service.monitor.entity.PutMonitorNoticeReset;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@ApiModel
public class PutMonitorTaskResetLogListDTO extends PutMonitorNoticeReset {

    private Integer masterId;

    private String masterName;

    private Long campaignId;

    private String campaignName;

    private String planName;

    private String isResetName;

    private String setVal;

    private String setValContent;

    @JSONField(serialize = false)
    private String condition;

    private List<String> conditionContent;

    private Integer operateType;

}
