package com.overseas.service.monitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.Base;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 **/
@TableName("tm_put_monitor_notice")
@Getter
@Setter
@ToString
public class PutMonitorNotice extends Base {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long putMonitorId;

    private Integer masterId;

    private String dimensionInfo;

    private String dimensionId;

    private String dimensionName;

    private Long noticeTime;

    private String noticeData;

    private Integer isRestore;

    @TableField(exist = false)
    private String operateVal;
}
