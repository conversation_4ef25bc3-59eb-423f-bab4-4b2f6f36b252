package com.overseas.service.monitor.dto.monitor.asset;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 **/
@Data
public class AssetGetDTO {

    private Long id;

    private String md5;

    private Integer assetType;

    private String content;

    private Long duration;

    private Integer width;

    private Integer height;

    private Long size;

    private Long positionX;

    private Long positionY;

    private Long fillWidth;

    private String format;

    private Long videoType;

    private Long creativeDirection;

    private Long goodsCategory;

    private Long assetCategory;

    private Long coverImgId;

    private Integer isUpload;

    private Long compressSize;

    private Integer isCompress;

    private Integer codeRate;

    @TableField(exist = false)
    private String httpUrl;

    @TableField(exist = false)
    private Long assetId;

    @TableField(exist = false)
    private String coverImgUrl;

    private Integer isUsed;

    private Integer isDel;

}
