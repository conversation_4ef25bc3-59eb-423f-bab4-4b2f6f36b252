package com.overseas.service.monitor.utils;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023-07-2023/7/27 14:03
 **/
public class ShaUtils {

    public static final String SHA_256 = "HmacSHA256";

    /**
     * SHA 256 加密
     *
     * @param text 内容
     * @return 返回加密后结果
     * @throws Exception 异常
     */
    public static String sha256_fs(String text) throws Exception {
        Mac mac = Mac.getInstance(SHA_256);
        mac.init(new SecretKeySpec(text.getBytes(StandardCharsets.UTF_8), SHA_256));
        byte[] signData = mac.doFinal(new byte[]{});
        return new String(Base64.encodeBase64(signData));
    }

    /**
     * SHA 256 加密
     *
     * @param text   内容
     * @param secret 密钥
     * @return 返回加密后结果
     * @throws Exception 异常
     */
    public static String sha256_dd(String text, String secret) throws Exception {
        Mac mac = Mac.getInstance(SHA_256);
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), SHA_256));
        byte[] signData = mac.doFinal(text.getBytes(StandardCharsets.UTF_8));
        return URLEncoder.encode(new String(Base64.encodeBase64(signData)), StandardCharsets.UTF_8);
    }


}
