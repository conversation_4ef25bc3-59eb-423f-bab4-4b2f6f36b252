package com.overseas.service.monitor.service.impl.operate;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.utils.FeignR;
import com.overseas.common.vo.market.creative.CreativeUnitAttributeOperateMonitorVO;
import com.overseas.common.vo.market.creative.CreativeUpdateAssetWaitCountVO;
import com.overseas.common.vo.market.plan.PlanByInfoGetVO;
import com.overseas.service.monitor.dto.monitor.put.PutMonitorDTO;
import com.overseas.service.monitor.entity.PutMonitorNotice;
import com.overseas.service.monitor.enums.MonitorIdentifyEnum;
import com.overseas.service.monitor.feign.FgMarketService;
import com.overseas.service.monitor.service.PutMonitorNoticeService;
import com.overseas.service.monitor.service.impl.AbstractPutMonitorOperateDealService;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class PutMonitorOperatePauseServiceImpl extends AbstractPutMonitorOperateDealService {

    private final PutMonitorNoticeService putMonitorNoticeService;

    private final FgMarketService fgMarketService;

    @Override
    public void dealAfter(PutMonitorDTO putMonitor, List<PutMonitorNotice> notices) {
        Pause pause = JSONObject.parseObject(putMonitor.getOperateVal(), Pause.class);
        List<PauseVO> pauseVOList = this.findDimensionMap(
                notices,
                putMonitor.getMonitorDimensionList(),
                pause
        );
        if (pauseVOList.isEmpty()) {
            log.info("此次后续操作无内容");
            return;
        }
        //执行自动上新流程
        if (1 == pause.getIsAssetUpdate()) {
            pauseVOList.stream().map(u -> u.getInfo().getPlanId()).distinct()
                    .forEach(planId -> {
                        try {
                            fgMarketService.assetCrativeAddPlan(planId, putMonitor.getId());
                        } catch (Exception e) {
                            log.info("监控ID:{} 计划ID:{} 执行自动上新失败 {}", putMonitor.getId(), planId, e.getMessage(), e);
                        }
                    });
        }
        //如果设置了最低素材数量，则设置，如果未设置，则复制 -1
        int surplusCount;
        if (null != pause.getIsSurplusNotice() && 1 == pause.getIsSurplusNotice()) {
            surplusCount = pause.getSurplusCount();
        } else {
            surplusCount = -1;
        }
        Map<Long, Integer> planAssetUpdateMap = new HashMap<>();
        Map<Long, List<String>> noticePlanMap = new HashMap<>();
        //暂停
        MonitorIdentifyEnum identifyEnum = ICommonEnum.get(pause.getReplaceDimension(), MonitorIdentifyEnum.class);
        pauseVOList.forEach(pauseVO -> {
            switch (identifyEnum) {
                case CREATIVE_UNIT:
                    try {
                        CreativeUnitAttributeOperateMonitorVO attributeOperateVO = new CreativeUnitAttributeOperateMonitorVO();
                        attributeOperateVO.setId(pauseVO.getInfo().getCreativeUnitId());
                        attributeOperateVO.setMasterId(pauseVO.getMasterId());
                        attributeOperateVO.setOperateType("status");
                        attributeOperateVO.setValue("3");
                        attributeOperateVO.setSurplusCount(surplusCount);
                        log.info("监控通知ID:{}，执行创意暂停操作，条件 ：{}", pauseVO.getNoticeId(), JSONObject.toJSONString(attributeOperateVO));
                        FeignR<Integer> feignR = fgMarketService.changeCreativeStatusMonitor(attributeOperateVO);
                        switch (feignR.getCode()) {
                            case 500024:
                                noticePlanMap.computeIfAbsent(pauseVO.getInfo().getPlanId(), k -> new ArrayList<>())
                                        .add(pauseVO.getInfo().getCreativeUnitId().toString());
                                break;
                            case 0:
                                if (feignR.getData() == 1) {
                                    int count = planAssetUpdateMap.computeIfAbsent(pauseVO.getInfo().getPlanId(), v -> 0);
                                    planAssetUpdateMap.put(pauseVO.getInfo().getPlanId(), count + 1);
                                }
                            default:
                        }
                    } catch (Exception e) {
                        log.error("监控通知ID:{} 归档创意失败:{}", pauseVO.getNoticeId(), e.getMessage(), e);
                    }
                    break;
                default:
            }
            log.info("监控通知ID:{} 执行成功结果成功", pauseVO.getNoticeId());
        });
        //
        if (!planAssetUpdateMap.isEmpty()) {
            planAssetUpdateMap.forEach((planId, count) -> {
                try {
                    fgMarketService.updateAssetWaitCount(CreativeUpdateAssetWaitCountVO.builder().count(count).planId(planId).build());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
        }
        //发送告警信息
        if (!noticePlanMap.isEmpty()) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("【暂停操作执行告警】\n").append("由于计划下投放中创意不足").append(surplusCount).append("个，请人工介入 \n");
            noticePlanMap.forEach((planId, creativeUnitIds) ->
                    stringBuilder.append("计划:").append(planId).append(" 下创意单元: ").append(String.join(",", creativeUnitIds)).append(" \n")
            );
            this.putMonitorNoticeService.sendNotice(putMonitor.getNoticeConfig(), stringBuilder.toString());
        }
        //再执行一次上新功能，补充归档的创意
        if (1 == pause.getIsAssetUpdate()) {
            pauseVOList.stream().map(u -> u.getInfo().getPlanId()).distinct()
                    .forEach(planId -> {
                        try {
                            fgMarketService.assetCrativeAddPlan(planId, putMonitor.getId());
                        } catch (Exception e) {
                            log.info("监控ID:{} 计划ID:{} 执行自动上新失败 {}", putMonitor.getId(), planId, e.getMessage(), e);
                        }
                    });
        }
    }

    /**
     * 根据信息获取数据
     *
     * @param notices 设置
     * @param dimList 维度
     * @param pause   暂停设置
     * @return 返回数据
     */
    private List<PutMonitorOperatePauseServiceImpl.PauseVO> findDimensionMap(List<PutMonitorNotice> notices, List<Integer> dimList, Pause pause) {
        List<PutMonitorOperatePauseServiceImpl.PauseVO> result = new ArrayList<>();
        for (PutMonitorNotice notice : notices) {
            String dimInfoId = putMonitorNoticeService.buildKey(notice.getDimensionInfo(), notice.getDimensionId());
            PlanByInfoGetVO planByInfoGetVO = new PlanByInfoGetVO();
            PauseVO pauseVO = new PauseVO();
            pauseVO.setNoticeId(notice.getId());
            pauseVO.setMasterId(notice.getMasterId());
            String[] splits = putMonitorNoticeService.splitKey(dimInfoId);
            int set = 0;
            for (int i = 0; i < dimList.size(); i++) {
                String val = splits[i];
                Integer dim = dimList.get(i);
                if (pause.getReplaceDimension().equals(dim)) {
                    pauseVO.setExclude(val);
                }
                MonitorIdentifyEnum identifyEnum = ICommonEnum.get(dimList.get(i), MonitorIdentifyEnum.class);
                switch (identifyEnum) {
                    case MASTER:
                        planByInfoGetVO.setMasterId(Integer.parseInt(val));
                        set++;
                        break;
                    case PLAN:
                        planByInfoGetVO.setPlanId(Long.parseLong(val));
                        set++;
                        break;
                    case CAMPAIGN:
                        planByInfoGetVO.setCampaignId(Long.parseLong(val));
                        set++;
                        break;
                    case ADX:
                        planByInfoGetVO.setAdxId(Integer.parseInt(val));
                        set++;
                        break;
                    case EP:
                        planByInfoGetVO.setEpIds(List.of(Integer.parseInt(val)));
                        set++;
                        break;
                    case SLOT:
                        planByInfoGetVO.setSlotType(Integer.parseInt(val));
                        set++;
                        break;
                    case CREATIVE_UNIT:
                        planByInfoGetVO.setCreativeUnitId(Long.parseLong(val));
                        set++;
                        break;
                    default:
                }
            }
            if (set != 0 && StringUtils.isNotBlank(pauseVO.getExclude())) {
                pauseVO.setInfo(planByInfoGetVO);
                result.add(pauseVO);
            }
        }
        return result;
    }

    @Data
    public static class PauseVO {

        private PlanByInfoGetVO info;

        private String exclude;

        private Long noticeId;

        private Integer masterId;
    }

    @Data
    public static class Pause {

        private List<Integer> searchDimension;

        private Integer replaceDimension;

        private Integer isAssetUpdate;

        private Integer isSurplusNotice;

        private Integer surplusCount;
    }

    @Data
    @Builder
    public static class PauseRest {

        private HashSet<Long> noticeIds;

        private HashSet<Long> planIds;
    }
}
