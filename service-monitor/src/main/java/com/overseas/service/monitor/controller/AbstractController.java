package com.overseas.service.monitor.controller;

import com.overseas.common.entity.User;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.common.utils.FeignR;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.service.monitor.feign.FgSystemService;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2022-08-17 11:31
 */

@Slf4j
@ApiIgnore
public abstract class AbstractController {

    protected FgSystemService fgSystemService;

    @Resource
    public void setFgSystemService(FgSystemService fgSystemService) {
        this.fgSystemService = fgSystemService;
    }

    /**
     * 获取用户登录ID
     *
     * @return 登陆用户ID
     */
    protected Integer getUserId() {
        FeignR<Integer> userIdR = fgSystemService.getUserId();
        log.info("userIdR {}", userIdR);
        if (!userIdR.getCode().equals(0) || ObjectUtils.isNullOrZero(userIdR.getData())) {
            throw new CustomException(userIdR.getCode(), userIdR.getMsg());
        }
        return userIdR.getData();
    }

    /**
     * 获取用户
     *
     * @return 登陆用户信息
     */
    protected User getUser() {
        FeignR<User> user = fgSystemService.getUser();
        if (!user.getCode().equals(0) && null == user.getData()) {
            throw new CustomException(user.getCode(), user.getMsg());
        }
        return user.getData();
    }

    /**
     * 获取用户登录ID
     *
     * @return 登陆用户 账户信息
     */
    protected List<Integer> listMasterId() {
        List<Integer> masterIds = new ArrayList<>();
        masterIds.add(ConstantUtils.INTEGER_UNKNOWN);
        FeignR<List<Integer>> listFeignR = fgSystemService.listMasterId();
        if (listFeignR.getCode().equals(0)) {
            masterIds.addAll(listFeignR.getData());
            return masterIds;
        } else {
            throw new CustomException(listFeignR.getCode(), listFeignR.getMsg());
        }
    }

    /**
     * 校验投放账号是否存在
     *
     * @param masterId 账户ID
     */
    protected void checkMasterId(Integer masterId) {
        if (!listMasterId().contains(masterId)) {
            throw new CustomException("投放账号ID不存在，请检查后重试");
        }
    }

}
