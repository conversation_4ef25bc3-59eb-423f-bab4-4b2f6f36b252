package com.overseas.service.monitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.Base;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@TableName("tm_put_monitor_notice_config")
public class PutMonitorNoticeConfig extends Base {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String configName;

    private String configVal;

    private Integer isDel;
}
