package com.overseas.service.monitor.enums;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum MonitorDataCycleTypeEnum implements ICommonEnum {

    //
    SAME_TIME_CYCLE(1, "与判定周期一致"),
    ONE_DAY(2, "当天"),
    ONE_HOUR(3, "当前小时"),
    SET_TIME(4, "设置周期"),
    LEI_JI(5, "累计时长");

    private final Integer id;

    private final String name;
}
