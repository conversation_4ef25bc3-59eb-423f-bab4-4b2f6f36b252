package com.overseas.service.monitor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.master.MasterTimeZoneDTO;
import com.overseas.common.dto.market.plan.PlanDimensionDTO;
import com.overseas.common.dto.monitor.put.CheckDateDTO;
import com.overseas.common.dto.monitor.put.ConditionDTO;
import com.overseas.common.dto.monitor.put.ConditionRDTO;
import com.overseas.common.dto.monitor.put.ErrConditionDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexFieldDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.master.MasterTimeZoneGetVO;
import com.overseas.common.vo.market.plan.PlanDimensionSelectVO;
import com.overseas.common.vo.monitor.put.PutDataVO;
import com.overseas.common.vo.report.DimensionTotalVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.service.monitor.dto.monitor.put.PutMonitorDTO;
import com.overseas.service.monitor.entity.PutMonitor;
import com.overseas.service.monitor.entity.PutMonitorData;
import com.overseas.service.monitor.entity.PutMonitorNotice;
import com.overseas.service.monitor.entity.PutMonitorResource;
import com.overseas.service.monitor.enums.*;
import com.overseas.service.monitor.feign.FgMarketService;
import com.overseas.service.monitor.feign.FgMonitorService;
import com.overseas.service.monitor.feign.FgReportService;
import com.overseas.service.monitor.feign.FgSystemService;
import com.overseas.service.monitor.mapper.PutMonitorDataMapper;
import com.overseas.service.monitor.mapper.PutMonitorMapper;
import com.overseas.service.monitor.mapper.PutMonitorNoticeMapper;
import com.overseas.service.monitor.mapper.PutMonitorResourceMapper;
import com.overseas.service.monitor.service.*;
import com.overseas.service.monitor.vo.monitor.task.PutMonitorChargeTimeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class PutMonitorServiceImpl implements PutMonitorService {

    private final PutMonitorNoticeService putMonitorNoticeService;

    private final FgSystemService fgSystemService;

    private final FgReportService fgReportService;

    private final FgMarketService fgMarketService;

    private final FgMonitorService fgMonitorService;

    private final PutMonitorDataMapper putMonitorDataMapper;

    private final PutMonitorMapper putMonitorMapper;

    private final PutMonitorResourceMapper putMonitorResourceMapper;

    private final PutMonitorOperateService putMonitorOperateService;

    private final PutMonitorNoticeMapper putMonitorNoticeMapper;

    private final PutMonitorSpecialConditionService putMonitorSpecialConditionDealService;

    public final static String CUSTOM_MODULE = "put_monitor_report";

    @Override
    public void putMonitor(Long id, Long day) {
        PutMonitorDTO putMonitor = findPutMonitor(id);
        switch (putMonitor.getMonitorType()) {
            case 1:
            case 3:
                if (!isNeedData(putMonitor, day)) {
                    log.info("监控执行结束：{}，时间：{},不在执行周期 ", id, day);
                    return;
                }
                //如果有回溯周期
                if (ObjectUtils.isNotNullOrZero(putMonitor.getPostbackCycle())) {
                    MonitorDataCycleUnitEnum unitEnum = ICommonEnum.get(putMonitor.getPostbackCycleUnit(), MonitorDataCycleUnitEnum.class);
                    if (null == unitEnum) {
                        throw new CustomException("回传周期设置unit不正确");
                    }
                    day = this.timeAfterByUnit(unitEnum, DateUtils.long2Date(day), -putMonitor.getPostbackCycle());
                }
                //清空数据
                putMonitorDataMapper.delete(new LambdaQueryWrapper<PutMonitorData>()
                        .eq(PutMonitorData::getPutMonitorId, id)
                        .eq(PutMonitorData::getDataTime, day)
                );
                this.putData(id, PutDataVO.builder()
                        .putMonitorId(putMonitor.getId())
                        .monitorDimensions(putMonitor.getMonitorDimensionList())
                        .day(day)
                        //当日数据
                        .dataCycleType(putMonitor.getDataCycleType().equals(MonitorDataCycleTypeEnum.LEI_JI.getId()) ? putMonitor.getDataCycleType() : 1)
                        .build()
                );
                this.putCheck(id, day);
                break;
            case 2:
                fgMonitorService.noticeDay(id, day);
                break;
            default:
        }
    }

    @Override
    public void putData(Long id, PutDataVO putDataVO) {
        PutMonitorDTO putMonitor = findPutMonitor(id);
        List<Long> dimensionIds = putMonitorResourceMapper.selectList(new LambdaQueryWrapper<PutMonitorResource>()
                        .eq(PutMonitorResource::getPutMonitorId, id)
                        .ne(PutMonitorResource::getDimensionId, 0)
                ).stream().map(PutMonitorResource::getDimensionId)
                .collect(Collectors.toList()
                );
        if (dimensionIds.isEmpty()) {
            return;
        }
        MonitorIdentifyEnum identifyEnum = ICommonEnum.get(putMonitor.getDimension(), MonitorIdentifyEnum.class);
        switch (identifyEnum) {
            case ALL:
                List<Long> masterIds = fgMarketService.allMasterSelect().getData()
                        .stream().map(SelectDTO::getId).collect(Collectors.toList());
                //插入账户数据
                putMonitorMapper.updateMasterById(JSONObject.toJSONString(masterIds), putMonitor.getId());
                masterIds.forEach(masterId -> {
                    try {
                        putDataVO.setMasterId(masterId);
                        fgMonitorService.monitorData(putDataVO);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                });
            case MASTER:
                //插入账户数据
                putMonitorMapper.updateMasterById(JSONObject.toJSONString(dimensionIds), putMonitor.getId());
                dimensionIds.forEach(masterId -> {
                    try {
                        putDataVO.setMasterId(masterId);
                        fgMonitorService.monitorData(putDataVO);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                });
                break;
            case CAMPAIGN:
                Map<Long, List<Long>> campaignMap = new HashMap<>(8);
                Map<Long, Long> agentMap1 = new HashMap<>(8);
                fgMarketService.planDimension(PlanDimensionSelectVO.builder()
                        .campaignIds(dimensionIds).build()
                ).getData().forEach(u -> {
                    campaignMap.computeIfAbsent(u.getMasterId(), k -> new ArrayList<>()).add(u.getCampaignId());
                    agentMap1.computeIfAbsent(u.getMasterId(), k -> u.getAgentId());
                });
                //插入账户数据
                putMonitorMapper.updateMasterById(JSONObject.toJSONString(campaignMap.keySet()), putMonitor.getId());
                campaignMap.forEach((masterId, campaignIds) -> {
                    try {
                        putDataVO.setAgentId(agentMap1.get(masterId));
                        putDataVO.setMasterId(masterId);
                        putDataVO.setCampaignIds(campaignIds.stream().distinct().collect(Collectors.toList()));
                        fgMonitorService.monitorData(putDataVO);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                });
                break;
            case PLAN:
                Map<Long, List<PlanDimensionDTO>> planMap = new HashMap<>(8);
                Map<Long, Long> agentMap2 = new HashMap<>(8);
                fgMarketService.planDimension(PlanDimensionSelectVO.builder()
                        .planIds(dimensionIds).build()
                ).getData().forEach(u -> {
                    planMap.computeIfAbsent(u.getMasterId(), k -> new ArrayList<>()).add(u);
                    agentMap2.computeIfAbsent(u.getMasterId(), k -> u.getAgentId());
                });
                //插入账户数据
                putMonitorMapper.updateMasterById(JSONObject.toJSONString(planMap.keySet()), putMonitor.getId());
                planMap.forEach((masterId, plans) -> {
                    try {
                        putDataVO.setAgentId(agentMap2.get(masterId));
                        putDataVO.setMasterId(masterId);
                        putDataVO.setCampaignIds(plans.stream().map(PlanDimensionDTO::getCampaignId).distinct().collect(Collectors.toList()));
                        putDataVO.setPlanIds(plans.stream().map(PlanDimensionDTO::getPlanId).distinct().collect(Collectors.toList()));
                        fgMonitorService.monitorData(putDataVO);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                });
                break;
            default:
        }
    }

    @Override
    public void putData(PutDataVO putDataVO) {
        List<PutMonitorData> list = getData(putDataVO);
        if (list.isEmpty()) {
            return;
        }
        Long saveDay = ObjectUtils.isNotNullOrZero(putDataVO.getSaveDay()) ? putDataVO.getSaveDay() : putDataVO.getDay();
        putMonitorDataMapper.batchInsert(putDataVO.getPutMonitorId(),
                putDataVO.getMonitorDimensions().stream().sorted().map(Object::toString).collect(Collectors.joining(",")),
                saveDay, list);
    }

    @Override
    public List<PutMonitorData> getData(PutDataVO putDataVO) {
        //获取账户时区
        MasterTimeZoneGetVO masterTimeZoneGetVO = new MasterTimeZoneGetVO();
        masterTimeZoneGetVO.setMasterId(putDataVO.getMasterId());
        masterTimeZoneGetVO.setTimeZone(100);
        List<MasterTimeZoneDTO> timeZones = fgMarketService.getTimezoneInfo(masterTimeZoneGetVO).getData();
        if (timeZones.isEmpty()) {
            log.error("监测ID:{}，监测账户:{} 无法获取时区", putDataVO.getPutMonitorId(), putDataVO.getMasterId());
            return List.of();
        }
        Integer timeZone = timeZones.get(0).getTimeZone();
        findDate(putDataVO.getDataCycleType(), timeZone, putDataVO);
        Date today = DateUtils.long2Date(putDataVO.getDay());
        //时区时间今日是多少
        Date timeZoneToday = TimeZoneEnum.getTimeZoneDate(DateUtils.long2Date(putDataVO.getDay()), timeZone, 0);
        //拼接查询数据条件
        DimensionTotalVO totalVO = DimensionTotalVO.builder()
                .day(DateUtils.format(timeZoneToday))
                .startHour(null == putDataVO.getStartHour() ? 0 : putDataVO.getStartHour())
                .endHour(null == putDataVO.getEndHour() ? DateUtils.getDateHour(timeZoneToday, 0) : putDataVO.getEndHour())
                .agentId(putDataVO.getAgentId())
                .module(CUSTOM_MODULE)
                .campaignIds(putDataVO.getCampaignIds())
                .planIds(putDataVO.getPlanIds())
                .epIds(putDataVO.getEpIds())
                .masterId(putDataVO.getMasterId())
                .timezone(timeZone)
                .top(putDataVO.getTop())
                .groupBys(new ArrayList<>())
                .monitorDimension(findMonitorIdentify(putDataVO.getMonitorDimensions()))
                .orderBys(putDataVO.getOrderBys())
                .build();
        //设置where数据
        if (CollectionUtils.isNotEmpty(putDataVO.getWheres())) {
            totalVO.setWheres(putDataVO.getWheres());
        }
        //如果是凌晨，查看昨天的数据
        if (isZero(putDataVO.getDay(), totalVO.getTimezone())) {
            totalVO.setDay(DateUtils.format(DateUtils.afterDay(today, -1)));
            totalVO.setEndHour(DateUtils.getDateHour(timeZoneToday, -1));
        }
        List<String> dimensionFields = new ArrayList<>();
        for (Integer oneDimension : putDataVO.getMonitorDimensions()) {
            //添加指标数据
            MonitorIdentifyEnum monitorIdentifyEnum = ICommonEnum.get(oneDimension, MonitorIdentifyEnum.class);
            switch (monitorIdentifyEnum) {
                case MASTER:
                    dimensionFields.add("`dim_master_id`");
                    totalVO.getGroupBys().add("dim_master_id");
                    break;
                case CAMPAIGN:
                    dimensionFields.add("`dim_campaign_id`");
                    totalVO.getGroupBys().add("dim_campaign_id");
                    break;
                case PLAN:
                    dimensionFields.add("`dim_plan_id`");
                    totalVO.getGroupBys().add("dim_plan_id");
                    break;
                case PACKAGE:
                    dimensionFields.add("`dim_pkg`");
                    totalVO.getGroupBys().add("dim_pkg");
                    break;
                case COUNTRY:
                    dimensionFields.add("`dim_country_id`");
                    totalVO.getGroupBys().add("dim_country_id");
                    break;
                case EP:
                    dimensionFields.add("`dim_ep_id`");
                    totalVO.getGroupBys().add("dim_ep_id");
                    break;
                case ADX:
                    dimensionFields.add("`dim_adx_id`");
                    totalVO.getGroupBys().add("dim_adx_id");
                    break;
                case SLOT:
                    dimensionFields.add("`slot_type`");
                    totalVO.getGroupBys().add("slot_type");
                    break;
                case CREATIVE_UNIT:
                    dimensionFields.add("`dim_creative_unit_id`");
                    totalVO.getGroupBys().add("dim_creative_unit_id");
                    break;
                default:
            }
        }
        //  设置 info 和 id
        if (dimensionFields.size() > 1) {
            totalVO.setDimensionFields(
                    String.format("CONCAT(%s) AS `dimension_info` , %s AS `dimension_id`",
                            String.join(",' -- ',", dimensionFields.subList(0, dimensionFields.size() - 1)),
                            dimensionFields.get(dimensionFields.size() - 1)
                    )
            );
        } else {
            totalVO.setDimensionFields(String.format("%s AS `dimension_id`", dimensionFields.get(dimensionFields.size() - 1)));
        }
        //自定义指标
        String fields;
        //如果包含项目指标，代表支持直接过滤数据，不需要入库
        if (StringUtils.isNotBlank(putDataVO.getIdentify())) {
            fields = fgSystemService.listCustomIndexReportSelect(CustomIndexGetVO
                    .builder().identify(putDataVO.getIdentify())
                    .module(CUSTOM_MODULE)
                    .build()).getData();

        } else {
            //如果统一指标，则标识需要入库
            fields = this.syncFields();
        }
        totalVO.setDimensionFields(totalVO.getDimensionFields() + "," + fields);
        PutMonitor putMonitor = findPutMonitor(putDataVO.getPutMonitorId());

        //如果是监控告警类型，且为无对比数据情况下，包含账户维度，则把判定条件也带入
        if (null != putMonitor && putMonitor.getMonitorType().equals(MonitorTypeEnum.MONITOR.getId())
                && putMonitor.getCompareCycle().equals(MonitorCompareCycleEnum.NO_CHAIN.getId())
                && putDataVO.getMonitorDimensions().contains(MonitorIdentifyEnum.MASTER.getId())) {
            totalVO.setWheres(new HashMap<>());
            List<ConditionDTO> conditions = JSONObject.parseArray(putMonitor.getConditions(), ConditionDTO.class);
            Map<String, String> ruleMap = fgSystemService.getCustomIndexFieldMap(CustomIndexGetVO
                    .builder().identify(putDataVO.getIdentify())
                    .module(CUSTOM_MODULE)
                    .build()).getData().stream().collect(Collectors.toMap(CustomIndexFieldDTO::getKey, CustomIndexFieldDTO::getRule));
            conditions.forEach(cond -> {
                if (ruleMap.containsKey(cond.getTarget())) {
                    if (!totalVO.getDimensionFields().contains(cond.getTarget())) {
                        totalVO.setDimensionFields(String.format("%s,%s AS %s", totalVO.getDimensionFields(),
                                ruleMap.get(cond.getTarget()), cond.getTarget()));
                    }
                    switch (cond.getJudge()) {
                        case "大于":
                            totalVO.getWheres().computeIfAbsent("gt", k -> new HashMap<>(8))
                                    .put(cond.getTarget(), cond.getValue().toString());
                            break;
                        case "小于":
                            totalVO.getWheres().computeIfAbsent("lt", k -> new HashMap<>(8))
                                    .put(cond.getTarget(), cond.getValue().toString());
                            break;
                        default:
                    }
                }
            });
        }
        //获取查询数据
        return fgReportService.dimensionTotal(totalVO).getData()
                .stream().map(u -> {
                    PutMonitorData putMonitorData = new PutMonitorData();
                    putMonitorData.setPutMonitorId(putDataVO.getPutMonitorId());
                    putMonitorData.setDimensionId(u.getDimensionId());
                    BeanUtils.copyProperties(u, putMonitorData);
                    if (null == putMonitorData.getDimensionInfo()) {
                        putMonitorData.setDimensionInfo("");
                    }
                    return putMonitorData;
                }).collect(Collectors.toList());
    }

    @Override
    public void putCheck(Long id, Long day) {
        PutMonitorDTO putMonitor = findPutMonitor(id);
        if (!isNeedCheck(putMonitor, day)) {
            return;
        }
        log.info("监控判定开始：{} ", id);
        List<ConditionDTO> conditions = JSONObject.parseArray(putMonitor.getConditions(), ConditionDTO.class);
        if (conditions.isEmpty()) {
            return;
        }
        //获取字段
        String fields = "put_monitor_id, dimension, dimension_info, dimension_id, " +
                fgSystemService.listCustomIndexReportSelect(
                        CustomIndexGetVO.builder().identify(putMonitor.getModuleIdentify())
                                .module(CUSTOM_MODULE).build()
                ).getData();
        CheckDateDTO checkDateDTO = new CheckDateDTO();
        checkDateDTO.setTimestamp(day);
        Date dayTime = DateUtils.long2Date(day);
        //上期时间
        MonitorDataCycleTypeEnum monitorDataCycleTypeEnum = ICommonEnum.get(putMonitor.getDataCycleType(), MonitorDataCycleTypeEnum.class);
        switch (monitorDataCycleTypeEnum) {
            case SAME_TIME_CYCLE:
            case SET_TIME:
                int cycle;
                MonitorDataCycleUnitEnum unitEnum;
                if (MonitorDataCycleTypeEnum.SET_TIME.equals(monitorDataCycleTypeEnum)) {
                    cycle = putMonitor.getDataCycle();
                    unitEnum = ICommonEnum.get(putMonitor.getDataCycleUnit(), MonitorDataCycleUnitEnum.class);
                } else {
                    cycle = putMonitor.getTimeCycleMinute();
                    unitEnum = ICommonEnum.get(putMonitor.getTimeCycleUnit(), MonitorDataCycleUnitEnum.class);
                }
                checkDateDTO.setCycle(cycle);
                checkDateDTO.setCycleUnit(unitEnum.getId());
                checkDateDTO.setStart1(this.timeAfterByUnit(unitEnum, dayTime, -cycle));
                checkDateDTO.setEnd1(day);
                break;
            case ONE_HOUR:
                long dayMinute = DateUtils.getCalendar(day).get(Calendar.MINUTE);
                if (dayMinute == 0) {
                    dayMinute = 60;
                }
                checkDateDTO.setStart1(DateUtils.afterMinuteByTimestamp(dayTime, -(int) dayMinute));
                checkDateDTO.setEnd1(day);
                break;
            case ONE_DAY:
                checkDateDTO.setStart1(0L);
                checkDateDTO.setEnd1(day);
                break;
            case LEI_JI:
                //设置累计
                checkDateDTO.setStart1(-101L);
                checkDateDTO.setEnd1(day);
                break;
            default:
        }
        if (checkDateDTO.getStart1() == null) {
            log.error("当前未查询到起始周期时间");
            throw new CustomException("当前未查询到起始周期时间");
        }
        //当前期数据
        Map<String, PutMonitorData> diffData1 = findPutData(putMonitor, checkDateDTO.getEnd1(), checkDateDTO.getStart1(), fields);
        //对比期数据（同比：昨日，环比：今日上期）
        Map<String, PutMonitorData> diffData2;
        MonitorCompareCycleEnum monitorCompareCycleEnum = ICommonEnum.get(putMonitor.getCompareCycle(), MonitorCompareCycleEnum.class);
        switch (monitorCompareCycleEnum) {
            case CHAIN:
            default:
                if (ObjectUtils.isNotNullOrZero(checkDateDTO.getCycle())) {
                    MonitorDataCycleUnitEnum unitEnum = ICommonEnum.get(checkDateDTO.getCycleUnit(), MonitorDataCycleUnitEnum.class);
                    checkDateDTO.setStart2(0L == checkDateDTO.getStart1() ? 0L : this.timeAfterByUnit(unitEnum, dayTime, -2 * checkDateDTO.getCycle()));
                    checkDateDTO.setEnd2(checkDateDTO.getStart1());
                } else {
                    checkDateDTO.setStart2(0L == checkDateDTO.getStart1() ? 0L : DateUtils.afterMinuteByTimestamp(DateUtils.long2Date(checkDateDTO.getStart1()), -60));
                    checkDateDTO.setEnd2(DateUtils.afterMinuteByTimestamp(DateUtils.long2Date(checkDateDTO.getEnd1()), -60));
                }
                //获取今日数据
                diffData2 = findPutData(putMonitor, checkDateDTO.getEnd2(), checkDateDTO.getStart2(), fields);
                break;
            case SAME_CHAIN_DAY:
                checkDateDTO.setStart2(0L == checkDateDTO.getStart1() ? 0L : DateUtils.afterMinuteByTimestamp(DateUtils.long2Date(checkDateDTO.getStart1()), -60 * 24));
                checkDateDTO.setEnd2(DateUtils.afterMinuteByTimestamp(DateUtils.long2Date(checkDateDTO.getEnd1()), -60 * 24));
                //获取昨日数据
                diffData2 = findPutData(putMonitor, checkDateDTO.getEnd2(), checkDateDTO.getStart2(), fields);
                break;
            case NO_CHAIN:
                diffData2 = new HashMap<>();
                break;
        }
        log.info("监控：{},判定时间：{}", putMonitor.getId(), JSONObject.toJSON(checkDateDTO));
        Map<String, ErrConditionDTO> err = new HashMap<>(8);
        Map<String, ErrConditionDTO> suc = new HashMap<>(8);
        for (Map.Entry<String, PutMonitorData> entry : diffData1.entrySet()) {
            JSONObject diff1 = ObjectUtils.toJSONObject(diffData1.get(entry.getKey()));
            JSONObject diff2 = ObjectUtils.toJSONObject(diffData2.getOrDefault(entry.getKey(), null));
            for (ConditionDTO condition : conditions) {
                try {
                    ConditionRDTO conditionR = new ConditionRDTO();
                    conditionR.init(condition, diff1, diff2);
                    boolean flag = conditionR.isFlag();
                    log.info("指定ID:{},比较结果:{}", entry.getKey(), JSONObject.toJSONString(conditionR));
                    if (!flag) {
                        log.info("这条条件不适合:{}", JSONObject.toJSON(conditionR));
                        suc.computeIfAbsent(entry.getKey(), k -> ErrConditionDTO
                                        .builder()
                                        .dimensionId(entry.getValue().getDimensionId())
                                        .dimensionInfo(entry.getValue().getDimensionInfo())
                                        .masterId(findMasterId(entry.getKey(), putMonitor.getMonitorDimensionList()))
                                        .conditions(new ArrayList<>())
                                        .build())
                                .getConditions().add(conditionR);
                        err.remove(entry.getKey());
                        break;
                    }
                    err.computeIfAbsent(entry.getKey(), k -> ErrConditionDTO.builder()
                                    .dimensionId(entry.getValue().getDimensionId())
                                    .dimensionInfo(entry.getValue().getDimensionInfo())
                                    .masterId(findMasterId(entry.getKey(), putMonitor.getMonitorDimensionList()))
                                    .conditions(new ArrayList<>())
                                    .diff1(diff1)
                                    .diff2(diff2)
                                    .build())
                            .getConditions().add(conditionR);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        //特殊判定条件处理
        List<ConditionDTO> specialConditions = JSONObject.parseArray(putMonitor.getSpecialConditions(), ConditionDTO.class);
        if (CollectionUtils.isNotEmpty(specialConditions) && CollectionUtils.isNotEmpty(err)) {
            this.putMonitorSpecialConditionDealService.specialCondition(specialConditions, putMonitor, checkDateDTO, err);
        }
        //处理需要处理提前行为
        putMonitorOperateService.dealBefore(putMonitor, checkDateDTO, err);
        //告警信息处理
        List<PutMonitorNotice> notices = putMonitorNoticeService.notice(checkDateDTO, putMonitor, err);
        //处理需要处理后续行为
        putMonitorOperateService.dealAfter(putMonitor, notices);
        //进行后续操作行为获取map
        Map<Long, PutMonitorNotice> putMonitorNoticeMap = notices.stream().collect(Collectors.toMap(PutMonitorNotice::getId, Function.identity()));
        //后续操作内容添加至告警信息
        err.values().forEach(er -> {
            if (putMonitorNoticeMap.containsKey(er.getNoticeId())) {
                er.setOperateVal(putMonitorNoticeMap.get(er.getNoticeId()).getOperateVal());
            }
        });
        //监控告警
        putMonitorNoticeService.noticeBuild("【监控告警】\n", checkDateDTO, putMonitor, err);
        //更新维度名称
        err.forEach((k, v) -> {
            if (ObjectUtils.isNotNullOrZero(v.getNoticeId()) && StringUtils.isNotBlank(v.getDimensionName())) {
                PutMonitorNotice update = new PutMonitorNotice();
                update.setDimensionName(v.getDimensionName());
                this.putMonitorNoticeMapper.update(update, new LambdaQueryWrapper<PutMonitorNotice>()
                        .eq(PutMonitorNotice::getId, v.getNoticeId()));
            }
        });
        //恢复告警
        if (1 == putMonitor.getIsRestore()) {
            putMonitorNoticeService.restore(checkDateDTO, putMonitor, suc);
        }
        log.info("监控判定结束：{} ", id);
    }


    /**
     * 获取投放监控数据
     *
     * @param id 监控ID
     * @return 返回监控信息
     */
    @Override
    public PutMonitorDTO findPutMonitor(Long id) {
        PutMonitorDTO putMonitor = putMonitorMapper.findById(id);
        if (null != putMonitor && Objects.equals(MonitorPutStatusEnum.ENABLE.getId(), putMonitor.getPutStatus())) {
            return putMonitor;
        }
        throw new CustomException("投放监控 ID:" + id + "不存在");
    }


    /**
     * 获取监控报表数据偏差
     *
     * @param putMonitor 监控信息
     * @param dataTime1  时间1
     * @param dataTime2  时间2
     * @param fields     获取字段
     * @return 返回数据
     */
    private Map<String, PutMonitorData> findPutData(PutMonitorDTO putMonitor, Long dataTime1, Long dataTime2, String fields) {
        String monitorDimension = getMonitorDimension(putMonitor.getMonitorDimensionList());
        long todayZero = DateUtils.string2Long(DateUtils.long2String(dataTime1));
        List<PutMonitorData> result;
        if (dataTime2 == 0 || dataTime2 == -101) {
            result = putMonitorDataMapper.selectDiff(new QueryWrapper<PutMonitorData>()
                            .select(fields)
                            .groupBy("dimension_info")
                            .groupBy(" dimension_id"),
                    diffSql1(putMonitor.getId(), monitorDimension, dataTime1)
            );
        } else if (todayZero < dataTime1 && todayZero > dataTime2) {
            result = putMonitorDataMapper.selectDiffWithZero(new QueryWrapper<PutMonitorData>()
                            .select(fields)
                            .groupBy("dimension_info")
                            .groupBy(" dimension_id"),
                    diffSql1(putMonitor.getId(), monitorDimension, dataTime1),
                    diffSql2(putMonitor.getId(), monitorDimension, todayZero, dataTime2),
                    //将处理开始至0点数据和结束至0点时间数据集合相加
                    diffSelect().replaceAll("-", "+")
            );
        } else {
            result = putMonitorDataMapper.selectDiff(new QueryWrapper<PutMonitorData>()
                            .select(fields)
                            .groupBy("dimension_info")
                            .groupBy("dimension_id"),
//                    //如果时间戳2是凌晨0点，则date1的全部数据为当前消耗数据，否则则消耗数据= date1的数据- date2的时间数据。
//                    isZero(dataTime2, TimeZoneEnum.UTC_8.getId()) ?
//                            diffSql1(putMonitor.getId(), putMonitor.getMonitorDimension(), dataTime1) :
                    diffSql2(putMonitor.getId(), monitorDimension, dataTime1, dataTime2));
        }
        return result.stream()
                .filter(u -> null != u && null != u.getDimensionId())
                .collect(Collectors.toMap(u -> putMonitorNoticeService.buildKey(u.getDimensionInfo(), u.getDimensionId()),
                        Function.identity()));
    }

    /**
     * 判定是否需要写数据
     *
     * @param putMonitor 监控信息
     * @param day        时间
     * @return 返回数据
     */
    private Boolean isNeedData(PutMonitor putMonitor, Long day) {
        try {
            LocalDateTime localDateTime = LocalDateTime.parse(DateUtils.long2String(day, DateUtils.DATE_TIME_PATTERN),
                    DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN)
                            .withZone(ZoneId.systemDefault()));
            int minute = localDateTime.getMinute() + localDateTime.getHour() * 60;
            boolean flag = minute % putMonitor.getTimeCycleMinute() == 0;
            log.info("monitor_id :{} ,minute: {}, cycle:{} is need data :{}", putMonitor.getId(), minute, putMonitor.getTimeCycleMinute(), flag);
            return flag;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    /**
     * 判定是否需要检查
     *
     * @param putMonitor 监控信息
     * @param day        时间
     * @return 返回数据
     */
    private Boolean isNeedCheck(PutMonitor putMonitor, Long day) {
        try {
            LocalDateTime localDateTime = LocalDateTime.parse(DateUtils.long2String(day, DateUtils.DATE_TIME_PATTERN),
                    DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN).withZone(ZoneId.systemDefault()));
            int minute = localDateTime.getMinute() + (localDateTime.getHour() == 0 ? 24 : localDateTime.getHour()) * 60;
            List<PutMonitorChargeTimeVO> chargeTime = StringUtils.isBlank(putMonitor.getChargeTime()) ?
                    List.of() : JSONObject.parseArray(putMonitor.getChargeTime(), PutMonitorChargeTimeVO.class);
            boolean flag;
            int hour = localDateTime.getHour();
            //判断是否满足条件，满足开始结束时间前开后闭
            if (!chargeTime.isEmpty()) {
                boolean needCheck = chargeTime.stream().anyMatch(u -> {
                    //23点的闭环为 0:00
                    if (hour == 0 && u.getEnd() == 23) {
                        return minute == 0;
                    }
                    if (u.getStart() <= hour && u.getEnd() + 1 >= hour) {
                        if (u.getStart().equals(hour)) {
                            //如果开始 0分，则允许判定
                            return minute % 60 == 0;
                        }
                        //截止时间的下个小时0分，可在进行一次检查
                        if (u.getEnd() + 1 == hour) {
                            return minute % 60 == 0;
                        }
                        return true;
                    }
                    return false;
                });
                if (!needCheck) {
                    log.info("monitor_id :{} ,hour: {}, minute:{} need hour:{} is not need check", putMonitor.getId(), localDateTime.getHour(), minute,
                            putMonitor.getChargeTime());
                    return false;
                }
            }
            flag = minute % putMonitor.getTimeCycleMinute() == 0;
            log.info("monitor_id :{} ,minute:{}, cycle:{} is need check :{}", putMonitor.getId(), minute, putMonitor.getTimeCycleMinute(), flag);
            return flag;
        } catch (
                Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    /**
     * 判断是否是零点
     *
     * @param day 时间戳
     * @return 返回结果
     */
    private boolean isZero(Long day, Integer timezone) {
        Date data = TimeZoneEnum.getTimeZoneDate(DateUtils.long2Date(day), timezone, 0);
        return "00:00:00".equals(DateUtils.format(data, "HH:mm:ss"));
    }

    /**
     * 获取sql 1
     *
     * @param putMonitor 监测ID
     * @param dimension  维度ID
     * @param dataTime   时间
     * @return 返回sql
     */
    private String diffSql1(Long putMonitor, String dimension, Long dataTime) {
        return String.format("SELECT * FROM tm_put_monitor_data WHERE put_monitor_id = %s AND dimension = '%s' AND data_time = %s",
                putMonitor, dimension, dataTime);
    }

    /**
     * diff sql 语句
     *
     * @return 返回数据
     */
    private String diffSql2(Long putMonitor, String dimension, Long dataTime1, Long dataTime2) {
        return String.format(" SELECT %s " +
                " FROM " +
                " ( SELECT * FROM tm_put_monitor_data WHERE put_monitor_id = %s AND dimension = '%s' AND data_time = %s) cur1 " +
                " LEFT JOIN " +
                " ( SELECT * FROM tm_put_monitor_data WHERE put_monitor_id = %s AND dimension = '%s' AND data_time = %s ) cur2" +
                " ON cur1.dimension_id = cur2.dimension_id AND cur1.dimension_info = cur2.dimension_info ", diffSelect(), putMonitor, dimension, dataTime1, putMonitor, dimension, dataTime2);
    }


    private String diffSelect() {
        return " IFNULL(cur1.put_monitor_id, cur2.put_monitor_id) AS put_monitor_id," +
                " IFNULL(cur1.dimension, cur2.dimension) AS dimension," +
                " IFNULL(cur1.dimension_info, cur2.dimension_info) AS dimension_info," +
                " IFNULL(cur1.dimension_id, cur2.dimension_id) AS dimension_id," +
                Stream.of("idx_bid", "idx_inner_bid", "idx_win", "idx_view", "idx_user_view",
                        "idx_click", "idx_user_click", "idx_media_cost", "idx_platform_cost", "idx_report_cost",
                        "idx_consumer_cost", "idx_reach", "idx_action", "idx_num_action",
                        "idx_action1", "idx_action2", "idx_action3", "idx_action4", "idx_action5", "idx_action6",
                        "idx_action7", "idx_action8", "idx_action9", "idx_action10", "idx_action11", "idx_action12",
                        "idx_action13", "idx_action14", "idx_action15", "idx_action16", "idx_action17", "idx_action18",
                        "idx_action19", "idx_action20", "idx_action21", "idx_action22", "idx_action23", "idx_action24",
                        "idx_action25", "idx_action26", "idx_action27", "idx_action28", "idx_action29", "idx_action30",
                        "idx_action31", "idx_action32", "idx_action33", "idx_action34", "idx_action35",
                        "idx_action36", "idx_action37", "idx_action38", "idx_action39", "idx_action40",
                        "idx_settlement", "idx_click_uniq_session"
                ).map(u -> String.format(" IF(cur1.%s < cur2.%s , cur1.%s ,(cur1.%s - cur2.%s)) AS %s ", u, u, u, u, u, u)).collect(Collectors.joining(","));
    }

    /**
     * 同步字段
     *
     * @return 返回数据
     */
    private String syncFields() {
        return "SUM(`idx_bid`) AS bid, SUM(`idx_inner_bid`) AS inner_bid," +
                " SUM(`idx_win`) AS win,  SUM(`idx_view`) AS view, SUM(`idx_user_view`) AS user_view, SUM(`idx_click`) AS click, " +
                " SUM(`idx_user_click`) AS user_click, SUM(`idx_media_cost`) AS media_cost, SUM(`idx_platform_cost`) AS platform_cost," +
                " SUM(`idx_report_cost`) AS report_cost, SUM(`idx_consumer_cost`) AS consumer_cost, SUM(`idx_reach`) AS reach, SUM(`idx_action`) AS action," +
                " SUM(`idx_num_action`) AS num_action, SUM(`idx_action1`) AS action1, SUM(`idx_action2`) AS action2, " +
                " SUM(`idx_action3`) AS action3, SUM(`idx_action4`) AS action4, SUM(`idx_action5`) AS action5, SUM(`idx_action6`) AS action6," +
                " SUM(`idx_action7`) AS action7, SUM(`idx_action8`) AS action8, SUM(`idx_action9`) AS action9," +
                " SUM(`idx_action10`) AS action10,  SUM(`idx_action11`) AS action11, SUM(`idx_action12`) AS action12, SUM(`idx_action13`) AS action13, " +
                " SUM(`idx_action14`) AS action14, SUM(`idx_action15`) AS action15, SUM(`idx_action16`) AS action16," +
                " SUM(`idx_action17`) AS action17, SUM(`idx_action18`) AS action18, SUM(`idx_action19`) AS action19, SUM(`idx_action20`) AS action20, " +
                " SUM(`idx_action21`) AS action21, SUM(`idx_action22`) AS action22, SUM(`idx_action23`) AS action23," +
                " SUM(`idx_action24`) AS action24, SUM(`idx_action25`) AS action25, SUM(`idx_action26`) AS action26, SUM(`idx_action27`) AS action27, " +
                " SUM(`idx_action28`) AS action28, SUM(`idx_action29`) AS action29, SUM(`idx_action30`) AS action30, SUM(`idx_action31`) AS action31, " +
                " SUM(`idx_action32`) AS action32, SUM(`idx_action33`) AS action33, SUM(`idx_action34`) AS action34, SUM(`idx_action35`) AS action35, " +
                " SUM(`idx_action36`) AS action36, SUM(`idx_action37`) AS action37, SUM(`idx_action38`) AS action38, SUM(`idx_action39`) AS action39, " +
                " SUM(`idx_action40`) AS action40, " +
                " SUM(`idx_settlement`)  AS settlement, SUM(`idx_click_uniq_session`)  AS click_uniq_session";
    }

    /**
     * 获取当前维度
     *
     * @param dimensions 维度
     * @return 返回维度 string
     */
    private String getMonitorDimension(List<Integer> dimensions) {
        return dimensions.stream().sorted().map(Objects::toString).collect(Collectors.joining(","));
    }


    /**
     * 设置当前日期
     *
     * @param dataCycleType 设置时间维度 : 1 ：当日；2:昨日；3:当前小时
     */
    private void findDate(Integer dataCycleType, Integer timezone, PutDataVO putDataVO) {
        if (null == dataCycleType) {
            return;
        }
        Long date = putDataVO.getDay();
        //首先获取本地的时间
        Date localDate = TimeZoneEnum.getTimeZoneDate(DateUtils.long2Date(date), timezone, 0);
        switch (dataCycleType) {
            case 2:
                //获取本地时间的昨日
                Date localYesterDay = DateUtils.afterDay(localDate, -1);
                //设置本地时间的昨日的 23点数据
                localYesterDay = DateUtils.setHour(localYesterDay, 23);
                //获取utf-8的时间
                putDataVO.setDay(DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(localYesterDay, timezone, 0)));
                putDataVO.setStartHour(0);
                putDataVO.setEndHour(23);
                break;
            case 1:
                //当天数据
                putDataVO.setStartHour(0);
                putDataVO.setEndHour(DateUtils.getCalendar(localDate).get(Calendar.HOUR_OF_DAY) - 1);
                break;
            case 3:
                //当前小时
                putDataVO.setStartHour(DateUtils.getCalendar(localDate).get(Calendar.HOUR_OF_DAY) - 1);
                putDataVO.setEndHour(putDataVO.getStartHour());
                break;
            case 5:
                //当天数据
                putDataVO.setStartHour(-101);
                putDataVO.setEndHour(DateUtils.getCalendar(localDate).get(Calendar.HOUR_OF_DAY) - 1);
                break;
            default:
        }
    }

    /**
     * 获取所筛选的表格
     *
     * @param identifies 维度数组
     * @return 返回数据
     */
    private Integer findMonitorIdentify(List<Integer> identifies) {
        if (identifies.contains(MonitorIdentifyEnum.COUNTRY.getId())) {
            return MonitorIdentifyEnum.COUNTRY.getId();
        }
        if (identifies.contains(MonitorIdentifyEnum.PACKAGE.getId())) {
            return MonitorIdentifyEnum.PACKAGE.getId();
        }
        if (identifies.contains(MonitorIdentifyEnum.EP.getId()) || identifies.contains(MonitorIdentifyEnum.ADX.getId())) {
            return MonitorIdentifyEnum.EP.getId();
        }
        if (identifies.contains(MonitorIdentifyEnum.CREATIVE_UNIT.getId())) {
            return MonitorIdentifyEnum.CREATIVE_UNIT.getId();
        }
        return MonitorIdentifyEnum.PLAN.getId();
    }


    /**
     * 获取账户ID
     *
     * @param key  key数据
     * @param dims 维度
     * @return 返回数据
     */
    private Integer findMasterId(String key, List<Integer> dims) {
        String[] keys = putMonitorNoticeService.splitKey(key);
        for (int i = 0; i < dims.size(); i++) {
            if (MonitorIdentifyEnum.MASTER.getId().equals(dims.get(i))) {
                return Integer.parseInt(keys[i]);
            }
        }
        return 0;
    }

    /**
     * 根据单位获取时间
     *
     * @param unitEnum 单位
     * @param dayTime  当前时间
     * @param cycle    调整时间
     * @return 返回时间
     */
    private Long timeAfterByUnit(MonitorDataCycleUnitEnum unitEnum, Date dayTime, int cycle) {
        switch (unitEnum) {
            case MINUTE:
                return DateUtils.afterMinuteByTimestamp(dayTime, cycle);
            case HOUR:
                return DateUtils.date2Long(DateUtils.afterHour(dayTime, cycle));
            case DAY:
                return DateUtils.date2Long(DateUtils.afterDay(dayTime, cycle));
            default:
                throw new CustomException("时间异常");
        }
    }

}
