package com.overseas.service.monitor.feign;

import com.overseas.common.utils.FeignR;
import com.overseas.common.vo.monitor.put.PutDataVO;
import com.overseas.service.monitor.entity.PutMonitorData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 **/
@FeignClient("ai-overseas-service-monitor")
public interface FgMonitorService {


    @PostMapping("/monitor/puts/start")
    FeignR<?> monitorStart(@RequestParam("id") Long id, @RequestParam("day") Long day,
                           @RequestHeader("Access-Token") String token);

    @PostMapping("/monitor/puts/data")
    FeignR<?> monitorData(PutDataVO putDataVO, @RequestHeader("Access-Token") String token);

    @PostMapping("/monitor/puts/data")
    FeignR<List<PutMonitorData>> monitorData(PutDataVO putDataVO);

    @PostMapping("/monitor/puts/data/{id}")
    FeignR<?> monitorData(@PathVariable("id") Long id, PutDataVO putDataVO, @RequestHeader("Access-Token") String token);

    @PostMapping("/monitor/puts/get/data")
    FeignR<List<PutMonitorData>> monitorGetData(PutDataVO putDataVO, @RequestHeader("Access-Token") String token);

    @PostMapping("/monitor/puts/check")
    FeignR<?> monitorCheck(@RequestParam("id") Long id, @RequestParam("day") Long day);

    @PostMapping("/monitor/puts/notice/day")
    FeignR<?> noticeDay(@RequestParam("id") Long id, @RequestParam("day") Long day,
                        @RequestHeader("Access-Token") String token);

    @PostMapping("/monitor/puts/notice/day")
    FeignR<?> noticeDay(@RequestParam("id") Long id, @RequestParam("day") Long day);

    @PostMapping("/monitor/puts/adx/notice")
    FeignR<?> adxNotice(@RequestParam("date") String date, @RequestHeader("Access-Token") String token);

    @PostMapping("/monitor/puts/rta/notice")
    FeignR<?> rtaNotice(@RequestParam("date") Long date, @RequestHeader("Access-Token") String token);

    @PostMapping("/monitor/puts/reset/day/time")
    FeignR<?> resetDay(@RequestParam("day") Long day, @RequestHeader("Access-Token") String token);
}
