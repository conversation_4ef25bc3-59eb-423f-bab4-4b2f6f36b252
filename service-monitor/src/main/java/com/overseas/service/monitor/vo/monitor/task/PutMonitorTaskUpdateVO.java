package com.overseas.service.monitor.vo.monitor.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
public class PutMonitorTaskUpdateVO extends PutMonitorTaskSaveVO {

    @ApiModelProperty("投放监控ID")
    @NotNull(message = "投放监控ID不能为空")
    private Long id;

}
