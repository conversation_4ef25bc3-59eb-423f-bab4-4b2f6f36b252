package com.overseas.service.monitor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.overseas.common.dto.monitor.put.CheckDateDTO;
import com.overseas.common.dto.monitor.put.ErrConditionDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.SpringContextUtils;
import com.overseas.service.monitor.dto.monitor.put.PutMonitorDTO;
import com.overseas.service.monitor.entity.PutMonitor;
import com.overseas.service.monitor.entity.PutMonitorNotice;
import com.overseas.service.monitor.entity.PutMonitorNoticeReset;
import com.overseas.service.monitor.enums.MonitorIsResetEnum;
import com.overseas.service.monitor.enums.MonitorOperateTypeEnum;
import com.overseas.service.monitor.mapper.PutMonitorMapper;
import com.overseas.service.monitor.mapper.PutMonitorNoticeMapper;
import com.overseas.service.monitor.mapper.PutMonitorNoticeResetMapper;
import com.overseas.service.monitor.service.PutMonitorOperateDealService;
import com.overseas.service.monitor.service.PutMonitorNoticeService;
import com.overseas.service.monitor.service.PutMonitorOperateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Service
@Slf4j
public class PutMonitorOperateServiceImpl implements PutMonitorOperateService {

    private final PutMonitorNoticeMapper putMonitorNoticeMapper;

    private final PutMonitorMapper putMonitorMapper;

    private final PutMonitorNoticeService putMonitorNoticeService;

    private final PutMonitorNoticeResetMapper putMonitorNoticeResetMapper;

    @Override
    public void dealBefore(PutMonitorDTO putMonitor, CheckDateDTO checkDateDTO, Map<String, ErrConditionDTO> errMap) {
        if (isNeedDeal(putMonitor)) {
            return;
        }
        if (errMap.isEmpty()) {
            return;
        }
        try {
            PutMonitorOperateDealService operateService = findServiceById(putMonitor.getOperateType());
            if (!operateService.isBeforeDeal()) {
                return;
            }
            log.info("监测ID:{}开始执行后续操作", putMonitor.getId());
            operateService.dealBefore(putMonitor, checkDateDTO, errMap);
        } catch (Exception e) {
            putMonitorNoticeService.sendNotice(String.format("监控ID:%s 监控后续操作失败，原因:%s", putMonitor.getId(), e.getMessage()));
            log.error(e.getMessage(), e);
        }
        log.info("监测ID:{}结束执行后续操作", putMonitor.getId());
    }

    @Override
    public void dealAfter(PutMonitorDTO putMonitor, List<PutMonitorNotice> notices) {
        if (isNeedDeal(putMonitor)) {
            return;
        }
        if (notices.isEmpty()) {
            return;
        }
        try {
            PutMonitorOperateDealService operateService = findServiceById(putMonitor.getOperateType());
            if (operateService.isBeforeDeal()) {
                return;
            }
            log.info("监测ID:{}开始执行后续操作", putMonitor.getId());
            operateService.dealAfter(putMonitor, notices);
        } catch (Exception e) {
            putMonitorNoticeService.sendNotice(String.format("监控ID:%s 监控后续操作失败，原因:%s", putMonitor.getId(), e.getMessage()));
            log.error(e.getMessage(), e);
        }
        log.info("监测ID:{}结束执行后续操作", putMonitor.getId());
    }


    @Override
    public void reset(Date today) {
        //0点时区
        TimeZoneEnum zeroZone = TimeZoneEnum.getTimeZoneByCurrentHour(Integer.parseInt(DateUtils.format(today, "HH")));
        if (null == zeroZone) {
            log.info("当前没有0点时区，执行结束");
            return;
        }
        today = DateUtils.string2Date(DateUtils.format(today, DateUtils.DATE_PATTERN + " HH:00:00"));
        //获取时区当日天数据，且往前推一天
        Date localStart = TimeZoneEnum.getTimeZoneDate(today, zeroZone.getId(), -24);
        Date localEnd = DateUtils.afterDay(localStart, 1);
        Map<Long, List<PutMonitorNotice>> putNoticeMap = putMonitorNoticeMapper.findNoticeByNeedReset(new QueryWrapper<>()
                .eq("tpm.is_operate", 1)
                .gt("tpmn.notice_time",
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(localStart, zeroZone.getId(), 0)))
                .le("tpmn.notice_time",
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(localEnd, zeroZone.getId(), 0)))
                .eq("tpmn.is_del", 0)
                .eq("tpmn.is_restore", MonitorIsResetEnum.NORMAL.getId())
                .eq("mm.time_zone", zeroZone.getId())
        ).stream().collect(Collectors.groupingBy(PutMonitorNotice::getPutMonitorId));
        if (putNoticeMap.isEmpty()) {
            log.info("当前当前没有需要恢复的监控，执行结束");
            return;
        }
        String todayStr = DateUtils.format(today, DateUtils.DATE_PATTERN + " HH:00:00");
        putNoticeMap.forEach((putMonitorId, notices) -> {
            try {
                if (notices.isEmpty()) {
                    return;
                }
                PutMonitorDTO putMonitor = putMonitorMapper.findById(putMonitorId);
                log.info("监控{}后续行为恢复开始处理", putMonitor.getId());
                //无后续操作，则进行过期处理
                if (putMonitor.getIsOperateReset() == 0) {
                    this.dealNotReset(putMonitor, notices, todayStr);
                    return;
                }
                //后续恢复处理
                findServiceById(putMonitor.getOperateType()).reset(putMonitor, notices, todayStr);
                log.info("监控{}后续行为恢复结束", putMonitor.getId());
            } catch (Exception e) {
                putMonitorNoticeService.sendNotice(String.format("监控ID:%s 恢复失败，原因:%s", putMonitorId, e.getMessage()));
                log.error(e.getMessage(), e);
            }
        });
    }


    /***
     * 获取当前处理interface
     * @param operateType 实验西安
     * @return 返回数据
     */
    @Override
    public PutMonitorOperateDealService findServiceById(Integer operateType) {
        MonitorOperateTypeEnum operateTypeEnum = ICommonEnum.get(operateType, MonitorOperateTypeEnum.class);
        if (null == operateTypeEnum) {
            throw new CustomException("后续操作类型不合法");
        }
        return (PutMonitorOperateDealService) SpringContextUtils.getBean(operateTypeEnum.getClazz());
    }

    /**
     * 判断是否需要后续行为
     *
     * @param putMonitor 监控信息
     * @return 返回数据
     */
    private boolean isNeedDeal(PutMonitor putMonitor) {
        return putMonitor.getIsOperate() != 1;
    }

    /**
     * 处理不需要恢复的数据
     *
     * @param putMonitor 监测
     * @param notices    告警
     * @param todayStr   今日
     */
    private void dealNotReset(PutMonitorDTO putMonitor, List<PutMonitorNotice> notices, String todayStr) {
        log.debug("监控:{}后续行为恢复过期处理", putMonitor.getId());
        List<Long> noticeIds = notices.stream().map(PutMonitorNotice::getId).collect(Collectors.toList());
        PutMonitorNoticeReset putMonitorNoticeReset = new PutMonitorNoticeReset();
        putMonitorNoticeReset.setIsReset(MonitorIsResetEnum.OVERDUE.getId());
        putMonitorNoticeReset.setResetTime(todayStr);
        //修改恢复日志状态
        this.putMonitorNoticeResetMapper.update(putMonitorNoticeReset, new LambdaQueryWrapper<PutMonitorNoticeReset>()
                .in(PutMonitorNoticeReset::getPutNoticeId, noticeIds)
        );
        PutMonitorNotice restoreNotice = new PutMonitorNotice();
        restoreNotice.setIsRestore(MonitorIsResetEnum.RESET.getId());
        putMonitorNoticeMapper.update(restoreNotice, new LambdaQueryWrapper<PutMonitorNotice>()
                .in(PutMonitorNotice::getId, noticeIds)
                .eq(PutMonitorNotice::getPutMonitorId, putMonitor.getId())
        );
    }
}
