package com.overseas.service.monitor.vo.monitor.task;


import com.alibaba.fastjson.JSONObject;
import com.overseas.common.dto.monitor.put.ConditionDTO;
import com.overseas.common.dto.monitor.put.NoticeConfigDTO;
import com.overseas.common.validation.annotation.MpEnum;
import com.overseas.common.validation.annotation.MpIn;
import com.overseas.service.monitor.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
public class PutMonitorTaskSaveVO {

    @ApiModelProperty("项目标识")
    @NotNull(message = "项目标识不能为空")
    private String moduleIdentify;

    @ApiModelProperty("监控名称")
    @NotBlank(message = "监控名称不能为空")
    @Length(max = 200, message = "监控名称不能超过200个字符")
    private String putMonitorName;

    @ApiModelProperty("监控类型：1:投放监控；2:数据通知")
    @NotNull(message = "监控类型不合法")
    @MpEnum(clazz = MonitorTypeEnum.class, message = "监控类型不合法")
    private Integer monitorType;

    @ApiModelProperty("账户ID")
    @NotNull(message = "监控账户不能为空")
    private List<Long> masterIds;

    @ApiModelProperty("监控指定数据维度")
    @MpEnum(clazz = MonitorIdentifyEnum.class, message = "监控指定数据维度不合法")
    private Integer dimension;

    @ApiModelProperty("指定数据维度ID")
    private List<Long> dimensionIds;

    @ApiModelProperty("监控维度")
    @Size(min = 1, message = "监控维度不能为空")
    private List<Integer> monitorDimension;

    @ApiModelProperty("时间间隔")
    @NotNull(message = "监控时间间隔不能为空")
    @Min(value = 0, message = "监控时间间隔不合法")
    private Integer timeCycle;

    @ApiModelProperty("时间间隔单位")
    @NotNull(message = "时间间隔单位不能为空")
    @MpEnum(zeroIsTrue = false, clazz = MonitorTimeCycleUnitEnum.class, message = "时间间隔单位不合法")
    private Integer timeCycleUnit;

    @ApiModelProperty("数据类型")
    private Integer dataCycleType;

    @ApiModelProperty("数据时间")
    private Integer dataCycle;

    @ApiModelProperty("数据时间单位")
    @MpEnum(clazz = MonitorDataCycleUnitEnum.class, message = "数据时间单位不合法")
    private Integer dataCycleUnit;

    @ApiModelProperty("数据回传时间")
    private Integer postbackCycle;

    @ApiModelProperty("数据回传时间单位")
    @MpEnum(clazz = MonitorDataCycleUnitEnum.class, message = "数据回传时间单位不合法")
    private Integer postbackCycleUnit;

    @ApiModelProperty("比较维度")
    @NotNull(message = "比较维度")
    @MpEnum(clazz = MonitorCompareCycleEnum.class, message = "比较维度不合法")
    private Integer compareCycle;

    @ApiModelProperty("监控条件")
    private List<ConditionDTO> conditions;

    @ApiModelProperty("特殊监控条件")
    private List<ConditionDTO> specialConditions;

    @ApiModelProperty("控制指标")
    private List<String> noticeIndicators;

    @ApiModelProperty("备注信息")
    @Length(max = 500, message = "备注信息不能超过500个字符")
    private String notice;

    @ApiModelProperty("通知配置信息")
    private List<NoticeConfigDTO> noticeConfig;

    @ApiModelProperty("数据通知是否需要top")
    private Integer isTop;

    @ApiModelProperty("数据top配置")
    private JSONObject topConfig;

    @ApiModelProperty("展示汇总数据")
    private Integer isTotal;

    @ApiModelProperty("判定时间段")
    @NotNull(message = "判定时间段不能为NULL")
    @Valid
    private List<PutMonitorChargeTimeVO> chargeTime;

    @ApiModelProperty("是否发送恢复告警")
    @NotNull(message = "是否发送恢复告警不能为空")
    @MpIn(values = {"0", "1"}, message = "是否发送恢复告警不合法")
    private Integer isRestore;

    @ApiModelProperty("是否开启操作后续行为：0:否；1:是")
    @MpIn(values = {"0", "1"}, message = "是否开启操作后续行为不合法")
    private Integer isOperate;

    @ApiModelProperty("操作后续行为类型")
    @MpEnum(clazz = MonitorOperateTypeEnum.class, message = "操作后续行为不合法")
    private Integer operateType;

    @ApiModelProperty("操作后续行为操作内容, 默认空")
    private String operateVal = "";

    @ApiModelProperty("操作后续行为是否需要恢复")
    private Integer isOperateReset;


}
