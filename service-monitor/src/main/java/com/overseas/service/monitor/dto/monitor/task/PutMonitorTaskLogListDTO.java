package com.overseas.service.monitor.dto.monitor.task;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@ApiModel
public class PutMonitorTaskLogListDTO {

    private Long id;

    private Long putMonitorId;

    @ApiModelProperty("维度ID")
    private String dimensionId;

    @ApiModelProperty("维度名称")
    private String dimensionName;

    @ApiModelProperty("告警时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date noticeTime;

    @ApiModelProperty("是否恢复")
    private Integer isRestore;

    @ApiModelProperty("恢复时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date restoreTime;

    @ApiModelProperty("告警当时指标")
    private List<String> noticeContent;

}
