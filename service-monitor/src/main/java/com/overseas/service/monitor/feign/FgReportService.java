package com.overseas.service.monitor.feign;

import com.overseas.common.dto.report.adx.AdxDailyCostListDTO;
import com.overseas.common.dto.report.DimensionTotalDTO;
import com.overseas.common.dto.report.rta.RtaReportListDTO;
import com.overseas.common.utils.FeignR;
import com.overseas.common.vo.report.AdxDailyCostListVO;
import com.overseas.common.vo.report.DimensionResetVO;
import com.overseas.common.vo.report.DimensionTotalVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 **/
@FeignClient("ai-overseas-service-report")
public interface FgReportService {

    @PostMapping("/report/dimension/total")
    FeignR<List<DimensionTotalDTO>> dimensionTotal(DimensionTotalVO dimensionTotalVO);


    @PostMapping("/report/dimension/reset")
    FeignR<BigDecimal> dimensionReset(DimensionResetVO dimensionResetVO);

    /**
     * Adx报表数据
     *
     * @param listVO 筛选数据
     * @return 返回数据
     */
    @PostMapping("/report/adxDailyCost/list")
    FeignR<List<AdxDailyCostListDTO>> getAdxDailyCostList(AdxDailyCostListVO listVO);

    /**
     * rta数据
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 返回数据
     */
    @PostMapping("/report/rta/pass/data")
    FeignR<List<RtaReportListDTO>> getRtaData(@RequestParam("start") Long start, @RequestParam("end") Long end);

}
