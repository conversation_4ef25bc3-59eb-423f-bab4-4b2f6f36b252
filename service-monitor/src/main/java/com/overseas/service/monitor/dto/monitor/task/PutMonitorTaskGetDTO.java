package com.overseas.service.monitor.dto.monitor.task;

import com.alibaba.fastjson.JSONObject;
import com.overseas.common.dto.monitor.put.ConditionDTO;
import com.overseas.common.dto.monitor.put.NoticeConfigDTO;
import com.overseas.service.monitor.vo.monitor.task.PutMonitorChargeTimeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@ApiModel
public class PutMonitorTaskGetDTO {

    @ApiModelProperty("监控ID")
    private Long id;

    @ApiModelProperty("项目维度")
    private String moduleIdentify;

    @ApiModelProperty("监控名称")
    private String putMonitorName;

    @ApiModelProperty("监控类型：1:投放监控；2:数据通知")
    private Integer monitorType;

    @ApiModelProperty("监控指定数据维度")
    private Integer dimension;

    @ApiModelProperty("监控维度")
    private List<Integer> monitorDimension;

    @ApiModelProperty("账户ID")
    private List<Long> masterIds;

    @ApiModelProperty("时间间隔")
    private Integer timeCycle;

    @ApiModelProperty("时间间隔单位")
    private Integer timeCycleUnit;

    @ApiModelProperty("数据类型")
    private Integer dataCycleType;

    @ApiModelProperty("数据时间")
    private Integer dataCycle;

    @ApiModelProperty("数据时间单位")
    private Integer dataCycleUnit;

    @ApiModelProperty("数据回传时间")
    private Integer postbackCycle;

    @ApiModelProperty("数据回传时间单位")
    private Integer postbackCycleUnit;

    @ApiModelProperty("判定时间段")
    private List<PutMonitorChargeTimeVO> chargeTime;

    @ApiModelProperty("比较维度")
    private Integer compareCycle;

    @ApiModelProperty("控制指标")
    private List<String> noticeIndicators;

    @ApiModelProperty("备注信息")
    private String notice;

    @ApiModelProperty("通知配置信息")
    private List<NoticeConfigDTO> noticeConfig;

    @ApiModelProperty("数据通知是否需要top")
    private Integer isTop;

    @ApiModelProperty("数据top配置")
    private JSONObject topConfig;

    @ApiModelProperty("是否需要汇总")
    private Integer isTotal;

    @ApiModelProperty("指定数据维度ID")
    private List<Long> dimensionIds;

    @ApiModelProperty("监控条件")
    private List<ConditionDTO> conditions;

    @ApiModelProperty("监控条件")
    private List<ConditionDTO> specialConditions;

    @ApiModelProperty("投放状态")
    private Integer putStatus;

    @ApiModelProperty("投放状态名称")
    private String putStatusName;

    @ApiModelProperty("是否发送恢复告警")
    private Integer isRestore;

    @ApiModelProperty("是否开启操作后续行为：0:否；1:是")
    private Integer isOperate;

    @ApiModelProperty("操作后续行为类型")
    private Integer operateType;

    @ApiModelProperty("操作后续行为操作内容, 默认空")
    private String operateVal = "";

    @ApiModelProperty("操作后续行为是否需要恢复")
    private Integer isOperateReset;

}
