package com.overseas.service.monitor.service.impl.operate;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.common.dto.market.market.MarketDimensionInfoDTO;
import com.overseas.common.dto.market.plan.PlanByInfoGetDTO;
import com.overseas.common.dto.market.plan.PlanReportDirectUpdateDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.monitor.put.MonitorDimensionEnum;
import com.overseas.common.enums.report.ReportDirectEnum;
import com.overseas.common.enums.report.ReportTypeEnum;
import com.overseas.common.vo.market.market.MarketDimensionInfoVO;
import com.overseas.common.vo.market.plan.PlanByInfoGetVO;
import com.overseas.common.vo.market.plan.PlanDirectionChangeGetVO;
import com.overseas.service.monitor.dto.monitor.put.PutMonitorDTO;
import com.overseas.service.monitor.entity.PutMonitorNotice;
import com.overseas.service.monitor.entity.PutMonitorNoticeReset;
import com.overseas.service.monitor.enums.MonitorIdentifyEnum;
import com.overseas.service.monitor.feign.FgMarketService;
import com.overseas.service.monitor.mapper.PutMonitorNoticeMapper;
import com.overseas.service.monitor.mapper.PutMonitorNoticeResetMapper;
import com.overseas.service.monitor.service.PutMonitorNoticeService;
import com.overseas.service.monitor.service.impl.AbstractPutMonitorOperateDealService;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Service
@Slf4j
public class PutMonitorOperateExpelServiceImpl extends AbstractPutMonitorOperateDealService {

    private final PutMonitorNoticeService putMonitorNoticeService;

    private final FgMarketService fgMarketService;

    private final PutMonitorNoticeResetMapper putMonitorNoticeResetMapper;

    private final PutMonitorNoticeMapper putMonitorNoticeMapper;

    @Override
    public void dealAfter(PutMonitorDTO putMonitor, List<PutMonitorNotice> notices) {
        Expel expel = JSONObject.parseObject(putMonitor.getOperateVal(), Expel.class);
        List<ExpelVO> expelVOList = this.findDimensionMap(
                notices,
                putMonitor.getMonitorDimensionList(),
                expel
        );
        if (expelVOList.isEmpty()) {
            log.info("此次后续操作无内容");
            return;
        }
        expelVOList.forEach(expelVO -> {
            List<PlanByInfoGetDTO> infos = fgMarketService.getPlanByInfo(expelVO.getInfo())
                    .getData();
            if (infos.isEmpty()) {
                return;
            }
            PlanDirectionChangeGetVO updateVO = new PlanDirectionChangeGetVO();
            updateVO.setMasterId(expelVO.getMasterId().longValue());
            updateVO.setCampaignIds(infos.stream().map(PlanByInfoGetDTO::getCampaignId).collect(Collectors.toList()));
            updateVO.setPlanIds(infos.stream().map(PlanByInfoGetDTO::getPlanId).collect(Collectors.toList()));
            updateVO.setReportType(ReportTypeEnum.PACKAGE_NAME.getId());
            updateVO.setInclude(2);
            updateVO.setDirectId(ReportDirectEnum.PACKAGE.getId());
            updateVO.setDirectValue(expelVO.getExclude());
            log.info("监控通知ID:{} 监控判定更改包数据条件 ：{}", expelVO.getNoticeId(), JSONObject.toJSONString(updateVO));
            List<PlanReportDirectUpdateDTO> updateDTO = fgMarketService.reportDirectUpdate(updateVO).getData();
            List<PutMonitorNoticeReset> resets = updateDTO
                    .stream().map(u -> {
                        PutMonitorNoticeReset reset = new PutMonitorNoticeReset();
                        reset.setPutNoticeId(expelVO.noticeId);
                        reset.setSetType(3);
                        reset.setSetId(u.getPlanId());
                        return reset;
                    }).collect(Collectors.toList());
            log.info("监控通知ID:{} 执行成功结果 :{}", expelVO.getNoticeId(), JSONObject.toJSONString(resets));
            if (!resets.isEmpty()) {
                putMonitorNoticeResetMapper.insertList(resets);
            }
        });
    }

    @Override
    public void reset(PutMonitorDTO putMonitor, List<PutMonitorNotice> notices, String todayStr) {
        Expel expel = JSONObject.parseObject(putMonitor.getOperateVal(), Expel.class);
        Map<Long, String> updateMap = notices.stream().collect(Collectors.toMap(PutMonitorNotice::getId,
                v -> this.findUpdateVal(putMonitorNoticeService.buildKey(v.getDimensionInfo(), v.getDimensionId()),
                        putMonitor.getMonitorDimensionList(),
                        expel)
        ));
        Map<String, ExpelRest> pkgMap = new HashMap<>();
        notices.forEach(notice -> {
            String updateVal = updateMap.get(notice.getId());
            if (StringUtils.isBlank(updateVal)) {
                return;
            }
            putMonitorNoticeResetMapper.selectList(new LambdaQueryWrapper<PutMonitorNoticeReset>()
                    .eq(PutMonitorNoticeReset::getPutNoticeId, notice.getId())
                    .eq(PutMonitorNoticeReset::getIsReset, 0)
            ).forEach(reset -> {
                ExpelRest expelRest = pkgMap.computeIfAbsent(updateVal, k -> ExpelRest.builder().noticeIds(new HashSet<>()).planIds(new HashSet<>()).build());
                expelRest.getNoticeIds().add(notice.getId());
                expelRest.getPlanIds().add(reset.getSetId());
            });
        });
        List<MarketDimensionInfoDTO> infos = fgMarketService.marketDimensionInfo(MarketDimensionInfoVO.builder()
                .ids(new ArrayList<>(pkgMap.values().stream().flatMap(u -> u.getPlanIds().stream()).distinct().collect(Collectors.toList())))
                .dimension(MonitorDimensionEnum.PLAN.getId())
                .build()).getData();
        if (CollectionUtils.isEmpty(infos)) {
            return;
        }
        Map<Long, Long> planCampMap = infos.stream().collect(Collectors.toMap(MarketDimensionInfoDTO::getId, MarketDimensionInfoDTO::getCampaignId));

        pkgMap.forEach((updateVal, expelRest) -> {
            PlanDirectionChangeGetVO updateVO = new PlanDirectionChangeGetVO();
            updateVO.setCampaignIds(expelRest.getPlanIds().stream().map(planCampMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            updateVO.setPlanIds(new ArrayList<>(expelRest.getPlanIds()));
            updateVO.setReportType(ReportTypeEnum.PACKAGE_NAME.getId());
            updateVO.setInclude(1);
            updateVO.setDirectId(ReportDirectEnum.PACKAGE.getId());
            updateVO.setDirectValue(updateVal);
            fgMarketService.reportDirectUpdate(updateVO).valid();
            PutMonitorNoticeReset putMonitorNoticeReset = new PutMonitorNoticeReset();
            putMonitorNoticeReset.setIsReset(1);
            putMonitorNoticeReset.setResetTime(todayStr);
            //修改恢复日志状态
            this.putMonitorNoticeResetMapper.update(putMonitorNoticeReset, new LambdaQueryWrapper<PutMonitorNoticeReset>()
                    .in(PutMonitorNoticeReset::getPutNoticeId, expelRest.getNoticeIds())
                    .in(PutMonitorNoticeReset::getSetId, expelRest.getPlanIds())
            );
            //修改日志为恢复
            PutMonitorNotice putMonitorNotice = new PutMonitorNotice();
            putMonitorNotice.setIsRestore(1);
            this.putMonitorNoticeMapper.update(putMonitorNotice, new LambdaQueryWrapper<PutMonitorNotice>()
                    .in(PutMonitorNotice::getId, expelRest.getNoticeIds())
                    .eq(PutMonitorNotice::getPutMonitorId, putMonitor.getId())
            );
        });
    }


    /**
     * 根据信息获取数据
     *
     * @param notices 设置
     * @param dimList 维度
     * @param expel   排除设置
     * @return 返回数据
     */
    private List<ExpelVO> findDimensionMap(List<PutMonitorNotice> notices, List<Integer> dimList, Expel expel) {
        List<ExpelVO> result = new ArrayList<>();
        for (PutMonitorNotice notice : notices) {
            String dimInfoId = putMonitorNoticeService.buildKey(notice.getDimensionInfo(), notice.getDimensionId());
            PlanByInfoGetVO planByInfoGetVO = new PlanByInfoGetVO();
            ExpelVO expelVO = new ExpelVO();
            expelVO.setNoticeId(notice.getId());
            expelVO.setMasterId(notice.getMasterId());
            String[] splits = putMonitorNoticeService.splitKey(dimInfoId);
            int set = 0;
            for (int i = 0; i < dimList.size(); i++) {
                String val = splits[i];
                Integer dim = dimList.get(i);
                if (expel.getReplaceDimension().equals(dim)) {
                    expelVO.setExclude(val);
                }
                MonitorIdentifyEnum identifyEnum = ICommonEnum.get(dimList.get(i), MonitorIdentifyEnum.class);
                switch (identifyEnum) {
                    case MASTER:
                        planByInfoGetVO.setMasterId(Integer.parseInt(val));
                        set++;
                        break;
                    case PLAN:
                        planByInfoGetVO.setPlanId(Long.parseLong(val));
                        set++;
                        break;
                    case CAMPAIGN:
                        planByInfoGetVO.setCampaignId(Long.parseLong(val));
                        set++;
                        break;
                    case ADX:
                        planByInfoGetVO.setAdxId(Integer.parseInt(val));
                        set++;
                        break;
                    case EP:
                        planByInfoGetVO.setEpIds(List.of(Integer.parseInt(val)));
                        set++;
                        break;
                    case SLOT:
                        planByInfoGetVO.setSlotType(Integer.parseInt(val));
                        set++;
                        break;
                    default:
                }
            }
            if (set != 0 && StringUtils.isNotBlank(expelVO.getExclude())) {
                expelVO.setInfo(planByInfoGetVO);
                result.add(expelVO);
            }
        }
        return result;
    }

    /**
     * 获取更新字段
     *
     * @param dims    维度数据
     * @param dimList 维度数组
     * @param expel   排除设置
     * @return 返回排除内容
     */
    private String findUpdateVal(String dims, List<Integer> dimList, Expel expel) {
        String[] splits = putMonitorNoticeService.splitKey(dims);
        for (int i = 0; i < dimList.size(); i++) {
            if (expel.getReplaceDimension().equals(dimList.get(i))) {
                return splits[i];
            }
        }
        return "";
    }


    @Data
    public static class ExpelVO {

        private PlanByInfoGetVO info;

        private String exclude;

        private Long noticeId;

        private Integer masterId;
    }

    @Data
    public static class Expel {

        private List<Integer> searchDimension;

        private Integer replaceDimension;
    }

    @Data
    @Builder
    public static class ExpelRest {

        private HashSet<Long> noticeIds;

        private HashSet<Long> planIds;
    }
}
