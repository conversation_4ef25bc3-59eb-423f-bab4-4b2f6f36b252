package com.overseas.service.monitor.feign;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitPutStatusDTO;
import com.overseas.common.dto.market.market.MarketDimensionInfoDTO;
import com.overseas.common.dto.market.master.MasterTimeZoneDTO;
import com.overseas.common.dto.market.plan.PlanByInfoGetDTO;
import com.overseas.common.dto.market.plan.PlanDimensionDTO;
import com.overseas.common.dto.market.plan.PlanReportDirectUpdateDTO;
import com.overseas.common.utils.FeignR;
import com.overseas.common.vo.market.asset.AssetAttributeGetVO;
import com.overseas.common.vo.market.creative.CreativeUnitAttributeOperateMonitorVO;
import com.overseas.common.vo.market.creative.CreativeUnitAttributeOperateVO;
import com.overseas.common.vo.market.creative.CreativeUnitPutStatusVO;
import com.overseas.common.vo.market.creative.CreativeUpdateAssetWaitCountVO;
import com.overseas.common.vo.market.market.MarketDimensionInfoVO;
import com.overseas.common.vo.market.master.MasterTimeZoneGetVO;
import com.overseas.common.vo.market.plan.PlanAttributeOperateVO;
import com.overseas.common.vo.market.plan.PlanByInfoGetVO;
import com.overseas.common.vo.market.plan.PlanDimensionSelectVO;
import com.overseas.common.vo.market.plan.PlanDirectionChangeGetVO;
import com.overseas.common.vo.market.plan.direct.AdxSelectGetVO;
import com.overseas.common.vo.market.plan.direct.EpSelectGetVO;
import com.overseas.common.vo.market.plan.direct.RtaSelectGetVO;
import com.overseas.service.monitor.dto.monitor.asset.AssetGetDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 **/
@FeignClient("ai-overseas-service-market")
public interface FgMarketService {

    @PostMapping("/market/plans/dimension/select")
    FeignR<List<PlanDimensionDTO>> planDimension(PlanDimensionSelectVO planDimensionSelectVO);

    @PostMapping("/market/masters/timezone/get")
    FeignR<List<MasterTimeZoneDTO>> getTimezoneInfo(MasterTimeZoneGetVO masterTimeZoneGetVO);

    @PostMapping("/market/market/dimension/info")
    FeignR<List<MarketDimensionInfoDTO>> marketDimensionInfo(MarketDimensionInfoVO marketDimensionInfoVO);

    @PostMapping("/market/planDirects/ep/select")
    FeignR<List<SelectDTO>> epSelect(EpSelectGetVO epSelectGetVO);

    @PostMapping("/market/planDirects/adx/select")
    FeignR<List<SelectDTO>> adxSelect(AdxSelectGetVO adxSelectGetVO);

    @PostMapping("/market/masters/all/select")
    FeignR<List<SelectDTO>> allMasterSelect();

    @PostMapping("/market/plans/get/plan/by/info")
    FeignR<List<PlanByInfoGetDTO>> getPlanByInfo(PlanByInfoGetVO getVO);

    @PostMapping("/market/plans/report/direct/update")
    FeignR<List<PlanReportDirectUpdateDTO>> reportDirectUpdate(PlanDirectionChangeGetVO updateVO);

    @PostMapping("/market/creatives/attributes/update")
    FeignR<?> changeCreativeStatus(CreativeUnitAttributeOperateVO operateVO);

    @PostMapping("/market/creatives/attributes/update/monitor")
    FeignR<Integer> changeCreativeStatusMonitor(CreativeUnitAttributeOperateMonitorVO operateVO);

    @PostMapping("/market/creatives/unit/put/status")
    FeignR<List<CreativeUnitPutStatusDTO>> creativeUnitPutStatus(CreativeUnitPutStatusVO unitPutStatusVO);

    @PostMapping("/market/creatives/update/asset/wait/count")
    FeignR<?> updateAssetWaitCount(CreativeUpdateAssetWaitCountVO countVO);

    @PostMapping("/market/assets/creative/add/plan")
    FeignR<?> assetCrativeAddPlan(@RequestParam("planId") Long planId, @RequestParam("monitorId") Long monitorId);

    @PostMapping("/market/assets/attribute/get")
    FeignR<AssetGetDTO> assetGet(AssetAttributeGetVO getVO);

    @PostMapping("/market/rtaStrategies/select")
    FeignR<List<SelectDTO>> selectRtaStrategy(RtaSelectGetVO getVO);

    @PostMapping("/market/plans/attributes/update")
    FeignR<Integer> planAttributesUpdate(PlanAttributeOperateVO planAttributeOperateVO);


}
