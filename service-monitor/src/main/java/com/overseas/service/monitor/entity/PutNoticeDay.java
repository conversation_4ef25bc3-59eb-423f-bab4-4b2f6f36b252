package com.overseas.service.monitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 **/
@Data
@TableName("tm_put_notice_day")
public class PutNoticeDay {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long putMonitorId;

    private String identify;

    private Integer monitorIdentify;

    private String masterIds;

    private Integer noticeCycle;

    private String noticeHour;

    private String fields;

    private String noticeContent;

    private String noticeTotal;

    private String noticeConfig;

    private Integer isCompare;

    private Integer topCount;

    private Integer putStatus;

    private Integer isDel;
}
