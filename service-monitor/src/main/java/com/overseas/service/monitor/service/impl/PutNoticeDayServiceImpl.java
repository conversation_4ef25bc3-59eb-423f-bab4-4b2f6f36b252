package com.overseas.service.monitor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.overseas.common.dto.monitor.put.ConditionDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.HumpLineUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.monitor.put.PutDataVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.service.monitor.entity.PutMonitor;
import com.overseas.service.monitor.entity.PutMonitorData;
import com.overseas.service.monitor.feign.FgMonitorService;
import com.overseas.service.monitor.feign.FgSystemService;
import com.overseas.service.monitor.vo.monitor.task.PutMonitorChargeTimeVO;
import com.overseas.service.monitor.vo.monitor.task.PutMonitorNoticeConfigVO;
import com.overseas.service.monitor.mapper.PutMonitorDataMapper;
import com.overseas.service.monitor.dto.monitor.put.PutMonitorDTO;
import com.overseas.service.monitor.enums.MonitorIdentifyEnum;
import com.overseas.service.monitor.service.PutMonitorNoticeService;
import com.overseas.service.monitor.service.PutMonitorService;
import com.overseas.service.monitor.service.PutNoticeDayService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Slf4j
@Service
public class PutNoticeDayServiceImpl implements PutNoticeDayService {

    private final PutMonitorNoticeService putMonitorNoticeService;

    private final PutMonitorService putMonitorService;

    private final FgMonitorService fgMonitorService;

    private final FgSystemService fgSystemService;

    private final PutMonitorDataMapper putMonitorDataMapper;

    @Override
    public void notice(Long id, String token, Date todayDate) {
        PutMonitorDTO putMonitor = putMonitorService.findPutMonitor(id);
        this.notice(putMonitor, token, todayDate);
    }

    @Override
    public void notice(PutMonitorDTO putMonitor, String token, Date todayDate) {
        if (!DateUtils.format(todayDate, DateUtils.DATE_TIME_PATTERN).endsWith("00:00")) {
            log.info("时间不是整点，通知不需要执行");
            return;
        }
        String projectName = putMonitor.getModuleIdentify().toUpperCase();
        log.info("【{}-{}】报表正式开始", projectName, putMonitor.getId());
        Integer hour = DateUtils.getDateHour(todayDate, 0);
        if (!isNeedCheck(putMonitor, hour)) {
            log.info("【{}-{}】报表数据执行结束, 当前时间：{} 不符合", projectName, putMonitor.getId(), hour);
            return;
        }
        List<MonitorIdentifyEnum> identifyEnums = putMonitor.getMonitorDimensionList()
                .stream().map(u -> ICommonEnum.get(u, MonitorIdentifyEnum.class)).collect(Collectors.toList());
        List<String> fields = JSONObject.parseArray(putMonitor.getNotice(), String.class);
        //今日数据
        List<JSONObject> result = getNeedSaveInfo(putMonitor, token, fields, todayDate, null);
        //昨日数据
        Date yesterdayDate = null;
        switch (putMonitor.getCompareCycle()) {
            case 1:
                yesterdayDate = DateUtils.afterHour(todayDate, -1);
                break;
            case 2:
                yesterdayDate = DateUtils.afterDay(todayDate, -1);
                break;
            case 3:
            default:
        }
        List<JSONObject> yesterday = null;
        if (null != yesterdayDate && !result.isEmpty()) {
            yesterday = getNeedSaveInfo(putMonitor, token, fields, yesterdayDate,
                    result.stream()
                            .map(u -> MapKey.builder()
                                    .dimensionInfo(u.getString("dimensionInfo"))
                                    .dimensionId(u.getString("dimensionId"))
                                    .build()
                            ).collect(Collectors.toList())
            );
        }
        try {
            String title = "";
            switch (putMonitor.getDataCycleType()) {
                case 1:
                    title = String.format("【%s】项目分%s当日汇总数据-当前：%s日%s时（UTC+8）\n", projectName,
                            identifyEnums.stream().map(MonitorIdentifyEnum::getName).collect(Collectors.joining("")),
                            DateUtils.getTodayStringDate(), DateUtils.getHour());
                    break;
                case 2:
                    title = String.format("【%s】项目分%s昨日汇总数据-数据时间：%s日（UTC+8）\n", projectName,
                            identifyEnums.stream().map(MonitorIdentifyEnum::getName).collect(Collectors.joining("")),
                            DateUtils.format(DateUtils.afterDay(todayDate, -1)));
                    todayDate = DateUtils.afterDay(todayDate, -1);
                    if (null != yesterdayDate) {
                        yesterdayDate = DateUtils.afterDay(todayDate, -1);
                    }
                    break;
                case 3:
                    title = String.format("【%s】项目分%s当前小时数据-当前：%s日%s时（UTC+8）\n", projectName,
                            identifyEnums.stream().map(MonitorIdentifyEnum::getName).collect(Collectors.joining("")),
                            DateUtils.getTodayStringDate(), DateUtils.getHour() - 1);
                default:
            }
            //重新获取一次账户信息
            putMonitor = putMonitorService.findPutMonitor(putMonitor.getId());
            putMonitorNoticeService.sendNotice(SendNoticeConfig.builder()
                            .configVO(JSONObject.parseArray(putMonitor.getNoticeConfig(), PutMonitorNoticeConfigVO.class))
                            .content(title)
                            .todayDate(todayDate)
                            .yesterdayDate(yesterdayDate)
                            .build(),
                    result, yesterday, putMonitor);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("【{}-{}】报表数据执行结束", projectName, putMonitor.getId());
    }

    /**
     * 获取需要保存数据
     *
     * @param putMonitor 信息
     * @param token      token
     * @param fields     字段
     * @return 返回数据
     */
    private List<JSONObject> getNeedSaveInfo(PutMonitorDTO putMonitor, String token,
                                             List<String> fields, Date todayDate, List<MapKey> mapKeys) {
        return getData(putMonitor, token, todayDate, mapKeys)
                .stream().map(u -> {
                    JSONObject obj = ObjectUtils.toJSONObject(u);
                    JSONObject rej = new JSONObject();
                    for (String field : fields) {
                        BigDecimal newObj = null == obj.getBigDecimal(field) ? new BigDecimal("0") : obj.getBigDecimal(field);
                        rej.put(field, newObj);
                    }
                    rej.put("dimensionId", u.getDimensionId());
                    rej.put("dimensionInfo", u.getDimensionInfo());
                    return rej;
                }).collect(Collectors.toList());
    }

    /**
     * 数据获取
     * @param putMonitor
     * @param token
     * @param todayDate
     * @param mapKeys
     * @return
     */
    private List<PutMonitorData> getData(PutMonitorDTO putMonitor, String token,
                                         Date todayDate, List<MapKey> mapKeys) {
        //存储数据
        Long saveDay = date2Long(todayDate);
        // 获取查询字段
        String select = findIdentifySelect(putMonitor.getModuleIdentify());
        QueryWrapper<?> mapQuery = null;
        List<ConditionDTO> conditions = JSONObject.parseArray(putMonitor.getConditions(), ConditionDTO.class);
        JSONObject topConfig = null;
        PutDataVO putDataVO = PutDataVO.builder()
                .putMonitorId(putMonitor.getId())
                .monitorDimensions(putMonitor.getMonitorDimensionList())
                .day(saveDay)
                .dataCycleType(putMonitor.getDataCycleType())
                .saveDay(saveDay)
                .putMonitorId(putMonitor.getId())
                .build();
        if (CollectionUtils.isNotEmpty(mapKeys)) {
            mapQuery = new QueryWrapper<>().inSql("(`dimension_info`, `dimension_id`)",
                    mapKeys.stream()
                            .map(u -> String.format("('%s','%s')", u.getDimensionInfo(), u.getDimensionId()))
                            .collect(Collectors.joining(","))
            );
        } else {
            if (CollectionUtils.isNotEmpty(conditions)) {
                mapQuery = new QueryWrapper<>();
                for (ConditionDTO cond : conditions) {
                    switch (cond.getJudge()) {
                        case "大于":
                            mapQuery.gtSql(HumpLineUtils.humpToLine2(cond.getTarget()), cond.getValue().toString());
                            if (cond.getTarget().equals("masterCost")) {
                                putDataVO.setWheres(Map.of("gt", Map.of("media_cost", cond.getValue().toString())));
                            }
                            break;
                        case "小于":
                            mapQuery.ltSql(HumpLineUtils.humpToLine2(cond.getTarget()), cond.getValue().toString());
                            if (cond.getTarget().equals("masterCost")) {
                                putDataVO.setWheres(Map.of("lt", Map.of("media_cost", cond.getValue().toString())));
                            }
                            break;
                        default:
                    }
                }
            }
            //排序数据
            if (1 == putMonitor.getIsTop()) {
                topConfig = JSONObject.parseObject(putMonitor.getTopConfig());
            }
            if (null != topConfig) {
                if (null == mapQuery) {
                    mapQuery = new QueryWrapper<>();
                }
                for (JSONObject u : topConfig.getJSONArray("fields").toJavaList(JSONObject.class)) {
                    mapQuery.orderBy(true, "asc".equalsIgnoreCase(u.getString("sortType")),
                            HumpLineUtils.humpToLine2(u.getString("sortField")));
                }
                mapQuery.last(" limit " + topConfig.getInteger("count"));
            }
        }
        QueryWrapper<?> dataQueryWrapper = this.findPutMonitorQuery(select, putMonitor.getId(), saveDay, null);
        List<PutMonitorData> putMonitorData = this.findPutMonitorData(dataQueryWrapper, mapQuery);
        if (putMonitorData.isEmpty()) {
            fgMonitorService.monitorData(putMonitor.getId(), putDataVO, token);
        }
        List<String> dimensionInfos = findDistinctDimensionInfo(putMonitor.getId(), saveDay);
        //特殊类型限制
        if (null != topConfig && dimensionInfos.size() > 2) {
            List<PutMonitorData> finalPutMonitorData = new ArrayList<>();
            for (String dimensionInfo : dimensionInfos) {
                finalPutMonitorData.addAll(findPutMonitorData(this.findPutMonitorQuery(select, putMonitor.getId(), saveDay, dimensionInfo), mapQuery));
            }
            putMonitorData = finalPutMonitorData;
        } else {
            if (putMonitorData.isEmpty()) {
                putMonitorData.addAll(this.findPutMonitorData(dataQueryWrapper, mapQuery));
            }
            if (1 == putMonitor.getIsTotal()) {
                List<PutMonitorData> finalPutMonitorData = new ArrayList<>();
                PutMonitorData total = this.findPutMonitorData(this.findPutMonitorQueryTotal(select, putMonitor.getId(), saveDay, null),
                        mapQuery).get(0);
                total.setDimensionInfo("-1");
                total.setDimensionId("-1");
                finalPutMonitorData.add(total);
                finalPutMonitorData.addAll(putMonitorData);
                putMonitorData = finalPutMonitorData;
            }
        }
        return putMonitorData;
    }

    /**
     * 将数据转换为时间戳
     *
     * @param date 时间
     * @return 返回时间戳
     */
    private Long date2Long(Date date) {
        return DateUtils.string2Long(DateUtils.format(date, DateUtils.DATE_TIME_PATTERN).substring(0, 17) + "00");
    }

    /**
     * 根据 查询字段， 监测ID 和时间获取数据
     *
     * @param select    查询字段
     * @param monitorId 监测ID
     * @param time      时间
     * @return 返回数据
     */
    private QueryWrapper<?> findPutMonitorQuery(String select, Long monitorId, Long time, String dimensionInfo) {
        //获取数据
        return new QueryWrapper<>().select(select)
                .groupBy("dimension_info")
                .groupBy("dimension_id")
                .eq("put_monitor_id", monitorId)
                .eq(StringUtils.isNotBlank(dimensionInfo), "dimension_info", dimensionInfo)
                .eq("data_time", time)
                .orderByAsc("dimension_info")
                .orderByDesc("master_cost");
    }

    /**
     * 获取总计数据
     *
     * @param select        字段
     * @param monitorId     监测id
     * @param time          时间
     * @param dimensionInfo 维度
     * @return 返回数据
     */
    private QueryWrapper<?> findPutMonitorQueryTotal(String select, Long monitorId, Long time, String dimensionInfo) {
        //获取数据
        return new QueryWrapper<>().select(select)
                .eq("put_monitor_id", monitorId)
                .eq(StringUtils.isNotBlank(dimensionInfo), "dimension_info", dimensionInfo)
                .eq("data_time", time);
    }

    /**
     * 查询结果
     *
     * @return 返回数据
     */
    private List<PutMonitorData> findPutMonitorData(QueryWrapper<?> queryWrapper, QueryWrapper<?> mapQuery) {
        if (null == mapQuery) {
            return this.putMonitorDataMapper.selectData(queryWrapper);
        } else {
            return this.putMonitorDataMapper.selectDataByWhere(queryWrapper, mapQuery);
        }
    }

    /**
     * 指标数据
     *
     * @param identify 标识
     * @return 指标字段
     */
    private String findIdentifySelect(String identify) {
        return "put_monitor_id, dimension, dimension_info, dimension_id, " +
                fgSystemService.listCustomIndexReportSelect(
                        CustomIndexGetVO.builder().identify(identify)
                                .module(PutMonitorServiceImpl.CUSTOM_MODULE).build()
                ).getData();
    }

    /**
     * 获取维度数据
     *
     * @param monitorId 监测数据
     * @param time      时间
     * @return 返回数据
     */
    private List<String> findDistinctDimensionInfo(Long monitorId, Long time) {
        return putMonitorDataMapper.selectDistinctDimensionInfo(new LambdaQueryWrapper<PutMonitorData>()
                .eq(PutMonitorData::getPutMonitorId, monitorId)
                .eq(PutMonitorData::getDataTime, time)
        );
    }


    /**
     * 是否需要监测
     *
     * @param putMonitor 监测
     * @param hour       时间
     * @return 返回数据
     */
    private boolean isNeedCheck(PutMonitor putMonitor, Integer hour) {
        if (StringUtils.isBlank(putMonitor.getChargeTime())) {
            return false;
        }
        List<PutMonitorChargeTimeVO> chargeTimeVO = JSONObject.parseArray(putMonitor.getChargeTime(),
                PutMonitorChargeTimeVO.class);
        for (PutMonitorChargeTimeVO chargeTime : chargeTimeVO) {
            if (hour >= chargeTime.getStart() && hour <= chargeTime.getEnd()) {
                return true;
            }
        }
        return false;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class MapKey {

        private String dimensionInfo;

        private String dimensionId;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class SendNoticeConfig {

        private String content;

        private Date todayDate;

        private Date yesterdayDate;

        private List<PutMonitorNoticeConfigVO> configVO;
    }
}
