package com.overseas.service.monitor.vo.monitor.task;

import com.overseas.common.vo.ListVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
public class PutMonitorTaskResetLogListVO extends ListVO {

    @ApiModelProperty("监控ID")
    @NotNull(message = "监控ID不能为空")
    private Long putMonitorId;

    @ApiModelProperty("是否恢复 0: 否；1:是；-1：全部")
    private Integer isReset;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;
}
