package com.overseas.service.monitor.service.impl;

import com.overseas.common.dto.report.adx.AdxDailyCostListDTO;
import com.overseas.common.utils.FeignR;
import com.overseas.common.utils.MailUtils;
import com.overseas.common.utils.redis.RedisUtils;
import com.overseas.common.vo.report.AdxDailyCostListVO;
import com.overseas.service.monitor.feign.FgReportService;
import com.overseas.service.monitor.service.PutAdxService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Slf4j
@Service
public class PutAdxServiceImpl implements PutAdxService {

    private final FgReportService fgReportService;

    private final RedisUtils redisUtils;

    @Override
    public void checkAdxData(String date) {
        AdxDailyCostListVO listVO = new AdxDailyCostListVO();
        listVO.setStartDate(date);
        listVO.setEndDate(date);
        listVO.setPage(1L);
        listVO.setPageNum(1000L);
        FeignR<List<AdxDailyCostListDTO>> result = fgReportService.getAdxDailyCostList(listVO);
        List<Dif> difList = new ArrayList<>();
        for (AdxDailyCostListDTO datum : result.getData()) {
            // adx侧数据未拉取的不做判断
            if (datum.getAdxView().equals(0L) && datum.getAdxCost().equals(0D)) {
                continue;
            }
            if (StringUtils.isBlank(datum.getAdxName())) {
                continue;
            }
            Dif dif = Dif.builder()
                    .adxCost(new BigDecimal(datum.getAdxCost().toString()))
                    .dspCost(new BigDecimal(datum.getDspCost().toString()))
                    .dspUnqCost(new BigDecimal(datum.getDspUniqueCost().toString()))
                    .difCostRate(datum.getCostDiffRate())
                    .adxView(datum.getAdxView())
                    .dspView(datum.getDspView())
                    .dspUnqView(datum.getDspUniqueView())
                    .adxName(datum.getAdxName())
                    .build();
            dif.setDifCost(dif.getDspCost().subtract(dif.getAdxCost()));
            dif.setDifView(dif.getDspView() - dif.getAdxView());
            dif.setDifUnqCost(dif.getDspUnqCost().subtract(dif.getAdxCost()));
            dif.setDifUnqView(dif.getDspUnqView() - dif.getAdxView());
            BigDecimal ratio = dif.getDifUnqCost().multiply(BigDecimal.valueOf(100)).divide(dif.getAdxCost(), 2, RoundingMode.HALF_UP);
            //判断是否需要告警
            if (ratio.abs().compareTo(new BigDecimal(5)) > 0
                    || dif.getDifUnqCost().abs().compareTo(new BigDecimal(50)) > 0) {
                if (checkIsNotice(date, dif)) {
                    difList.add(dif);
                }
            }
        }
        if (!difList.isEmpty()) {
            //发送邮件
            String subject = String.format("%s日ADX对账消耗偏差告警", date);
            StringBuilder mailBuffer = new StringBuilder(
                    String.format("<html><body><p>Dear：</p><p>如下为%s信息，请知悉：</p>", subject));
            mailBuffer.append("<table border=\"5\" style=\"border:solid 1px #000000;font-size:18px;border-collapse: collapse;\">");
            mailBuffer.append("<tr><th>ADX名称</th><th>ADX消耗</th><th>DSP消耗</th><th>消耗偏差</th><th>消耗偏差比例</th>");
            mailBuffer.append("<th>DSP去重消耗</th><th>去重消耗偏差</th><th>ADX曝光</th><th>DSP曝光</th>");
            mailBuffer.append("<th>曝光偏差</th><th>DSP去重曝光</th><th>去重曝光偏差</th>");
            for (Dif dif : difList) {
                mailBuffer.append("<tr>");
                mailBuffer.append("<td>").append(dif.getAdxName()).append("</td>");
                mailBuffer.append("<td>").append(dif.getAdxCost()).append("</td>");
                mailBuffer.append("<td>").append(dif.getDspCost()).append("</td>");
                mailBuffer.append("<td>").append("<p style=\"color:")
                        .append(dif.getDifCost().compareTo(BigDecimal.ZERO) > 0 ? "green" : "red").append("\">")
                        .append(dif.getDifCost()).append("</p>").append("</td>");
                mailBuffer.append("<td>").append("<p style=\"color:")
                        .append(dif.getDifCostRate().compareTo(0D) > 0 ? "green" : "red").append("\">")
                        .append(dif.getDifCostRate()).append("</p>").append("</td>");
                mailBuffer.append("<td>").append(dif.getDspUnqCost()).append("</td>");
                mailBuffer.append("<td>").append("<p style=\"color:")
                        .append(dif.getDifUnqCost().compareTo(BigDecimal.ZERO) > 0 ? "green" : "red").append("\">")
                        .append(dif.getDifUnqCost()).append("</p>").append("</td>");
                mailBuffer.append("<td>").append(dif.getAdxView()).append("</td>");
                mailBuffer.append("<td>").append(dif.getDspView()).append("</td>");
                mailBuffer.append("<td>").append("<p style=\"color:")
                        .append(dif.getDifView() > 0 ? "green" : "red").append("\">")
                        .append(dif.getDifView()).append("</p>").append("</td>");
                mailBuffer.append("<td>").append(dif.getDspUnqView()).append("</td>");
                mailBuffer.append("<td>").append("<p style=\"color:")
                        .append(dif.getDifUnqView() > 0 ? "green" : "red").append("\">")
                        .append(dif.getDifUnqView()).append("</p>").append("</td>");
                mailBuffer.append("</tr>");
            }
            mailBuffer.append("</table><p>注：红色为ADX比DSP高，绿色为ADX比DSP低，如有疑问，请联系相关研发负责人<p>");
            mailBuffer.append("<p>告警触发条件：去重消耗偏差比例绝对值大于5%或去重消耗绝对值大于50美金。</p>");
            mailBuffer.append("</body></html>");
            //发送邮件
            MailUtils.sendHtmlEmail(List.of(
                    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                            "<EMAIL>", "<EMAIL>", "<EMAIL>"
                    ),
                    subject, mailBuffer.toString(), null);
        }
    }


    /**
     * 校验是否需要告警
     *
     * @param date 日期
     * @param dif  数据
     * @return 返回数据
     */
    private boolean checkIsNotice(String date, Dif dif) {
        String key = String.format("adx-monitor-%s-%s", date, dif.getAdxName());
        String val = redisUtils.get(key);
        if (null != val && val.equals(dif.getDifCost().toString())) {
            return false;
        }
        redisUtils.set(key, dif.getDifCost().toString(), 60 * 60 * 12);
        return true;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class Dif {

        private BigDecimal dspCost;

        private BigDecimal dspUnqCost;

        private BigDecimal adxCost;

        private BigDecimal difCost;

        private Double difCostRate;

        private BigDecimal difUnqCost;

        private Long dspView;

        private Long dspUnqView;

        private Long adxView;

        private Long difView;

        private Long difUnqView;

        private String adxName;
    }
}