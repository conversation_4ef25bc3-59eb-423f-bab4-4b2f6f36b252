package com.overseas.service.monitor.service.impl;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.report.rta.RtaReportListDTO;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.vo.market.plan.direct.RtaSelectGetVO;
import com.overseas.service.monitor.feign.FgMarketService;
import com.overseas.service.monitor.feign.FgReportService;
import com.overseas.service.monitor.service.PutMonitorNoticeService;
import com.overseas.service.monitor.service.PutRtaService;
import com.overseas.service.monitor.vo.monitor.task.PutMonitorNoticeConfigVO;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class PutRtaServiceImpl implements PutRtaService {

    private final FgReportService fgReportService;

    private final PutMonitorNoticeService putMonitorNoticeService;

    private final FgMarketService fgMarketService;

    @Override
    public void checkRtaData(Long curr2) {
        Date today = DateUtils.long2Date(curr2);
        Long curr1 = DateUtils.date2Long(DateUtils.afterHour(today, -1));
        Long last2 = curr2 - 60 * 60 * 24;
        Long last1 = DateUtils.date2Long(DateUtils.afterDay(DateUtils.afterHour(today, -1), -1));
        Map<Long, Double> result1 = fgReportService.getRtaData(curr1, curr2).getData()
                .stream().collect(Collectors.toMap(RtaReportListDTO::getRtaStrategyId, RtaReportListDTO::getPassRate));
        Map<Long, Double> result2 = fgReportService.getRtaData(last1, last2).getData()
                .stream().collect(Collectors.toMap(RtaReportListDTO::getRtaStrategyId, RtaReportListDTO::getPassRate));
        Set<Long> rtaIds = new HashSet<>(result2.keySet());
        rtaIds.addAll(result1.keySet());
        List<Dif> difList = new ArrayList<>();
        rtaIds.forEach(rtaId -> {
            BigDecimal t = BigDecimal.valueOf(result1.getOrDefault(rtaId, 0d));
            BigDecimal y = BigDecimal.valueOf(result2.getOrDefault(rtaId, 0d));
            if (!BigDecimal.ZERO.equals(y)) {
                BigDecimal rate = t.subtract(y).multiply(BigDecimal.valueOf(100d)).divide(y, 1, RoundingMode.HALF_UP);
                if (rate.abs().compareTo(BigDecimal.valueOf(50d)) > 0) {
                    difList.add(Dif.builder().rtaId(rtaId).y(y).t(t).dif(rate).build());
                }
            }
        });
        if (!difList.isEmpty()) {
            RtaSelectGetVO getVO = new RtaSelectGetVO();
            getVO.setIds(difList.stream().map(Dif::getRtaId).collect(Collectors.toList()));
            Map<Long, String> rtaMap = fgMarketService.selectRtaStrategy(getVO).getData().stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
            StringBuffer stringBuffer = new StringBuffer("【")
                    .append(DateUtils.format(today))
                    .append("】【RTA通过率偏差告警】\n");
            difList.forEach(u -> {
                stringBuffer.append("【").append(rtaMap.getOrDefault(u.getRtaId(), u.getRtaId().toString())).append("】：")
                        .append("当前小时通过率：").append(u.getT().toString()).append("%，")
                        .append("昨日同比通过率：").append(u.getY().toString()).append("%\n")
                        .append("同比偏差：").append(u.getDif().toString()).append("%")
                        .append("\n");
            });
            putMonitorNoticeService.sendNotice(PutMonitorNoticeConfigVO.builder()
                            .hookType(2)
                            .hookUrl("https://oapi.dingtalk.com/robot/send?access_token=e00e5dc519633f499fbbf76cf451587a065fd07f9f29f2c98512fe8669841f36")
                            .hookToken("SECb1e872c3e5f6738b3b80ac7be5058a5c19be5c73037dcead1b17624ee8e13b9c")
                            .build(),
                    stringBuffer.toString());
        }
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class Dif {

        private BigDecimal y;

        private BigDecimal t;

        private Long rtaId;

        private BigDecimal dif;

    }
}
