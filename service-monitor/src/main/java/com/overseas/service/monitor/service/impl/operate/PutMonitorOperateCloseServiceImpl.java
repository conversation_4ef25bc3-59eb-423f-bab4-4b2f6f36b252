package com.overseas.service.monitor.service.impl.operate;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.utils.FeignR;
import com.overseas.common.vo.market.creative.CreativeUnitAttributeOperateMonitorVO;
import com.overseas.common.vo.market.creative.CreativeUpdateAssetWaitCountVO;
import com.overseas.common.vo.market.plan.PlanByInfoGetVO;
import com.overseas.service.monitor.dto.monitor.put.PutMonitorDTO;
import com.overseas.service.monitor.entity.PutMonitorNotice;
import com.overseas.service.monitor.enums.MonitorIdentifyEnum;
import com.overseas.service.monitor.feign.FgMarketService;
import com.overseas.service.monitor.service.PutMonitorNoticeService;
import com.overseas.service.monitor.service.impl.AbstractPutMonitorOperateDealService;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class PutMonitorOperateCloseServiceImpl extends AbstractPutMonitorOperateDealService {

    private final PutMonitorNoticeService putMonitorNoticeService;

    private final FgMarketService fgMarketService;

    @Override
    public void dealAfter(PutMonitorDTO putMonitor, List<PutMonitorNotice> notices) {
        Close close = JSONObject.parseObject(putMonitor.getOperateVal(), Close.class);
        List<CloseVO> closeVOList = this.findDimensionMap(
                notices,
                putMonitor.getMonitorDimensionList(),
                close
        );
        if (closeVOList.isEmpty()) {
            log.info("此次后续操作无内容");
            return;
        }
        //执行自动上新流程
        if (1 == close.getIsAssetUpdate()) {
            closeVOList.stream().map(u -> u.getInfo().getPlanId())
                    .distinct().forEach(planId -> {
                        try {
                            fgMarketService.assetCrativeAddPlan(planId, putMonitor.getId());
                        } catch (Exception e) {
                            log.info("监控ID:{} 计划ID:{} 执行自动上新失败 {}", putMonitor.getId(), planId, e.getMessage(), e);
                        }
                    });
        }
        Integer surplusCount;
        if (null != close.getIsSurplusNotice() && 1 == close.getIsSurplusNotice()) {
            surplusCount = close.getSurplusCount();
        } else {
            surplusCount = -1;
        }
        Map<Long, Integer> planAssetUpdateMap = new HashMap<>();
        Map<Long, List<String>> noticePlanMap = new HashMap<>();
        //归档操作
        MonitorIdentifyEnum identifyEnum = ICommonEnum.get(close.getReplaceDimension(), MonitorIdentifyEnum.class);
        closeVOList.forEach(closeVO -> {
            switch (identifyEnum) {
                case CREATIVE_UNIT:
                    try {
                        CreativeUnitAttributeOperateMonitorVO attributeOperateVO = new CreativeUnitAttributeOperateMonitorVO();
                        attributeOperateVO.setId(closeVO.getInfo().getCreativeUnitId());
                        attributeOperateVO.setMasterId(closeVO.getMasterId());
                        attributeOperateVO.setOperateType("status");
                        attributeOperateVO.setValue("4");
                        attributeOperateVO.setSurplusCount(surplusCount);
                        log.info("监控通知ID:{}，执行创意归档操作，条件 ：{}", closeVO.getNoticeId(), JSONObject.toJSONString(attributeOperateVO));
                        FeignR<Integer> feignR = fgMarketService.changeCreativeStatusMonitor(attributeOperateVO);
                        switch (feignR.getCode()) {
                            case 500024:
                                noticePlanMap.computeIfAbsent(closeVO.getInfo().getPlanId(), k -> new ArrayList<>())
                                        .add(closeVO.getInfo().getCreativeUnitId().toString());
                                break;
                            case 0:
                                if (feignR.getData() == 1) {
                                    int count = planAssetUpdateMap.computeIfAbsent(closeVO.getInfo().getPlanId(), v -> 0);
                                    planAssetUpdateMap.put(closeVO.getInfo().getPlanId(), count + 1);
                                }
                            default:
                        }
                    } catch (Exception e) {
                        log.error("监控通知ID:{} 归档创意失败:{}", closeVO.getNoticeId(), e.getMessage(), e);

                    }
                    break;
                default:
            }
            log.info("监控通知ID:{} 执行成功结果成功", closeVO.getNoticeId());
        });
        //
        if (!planAssetUpdateMap.isEmpty()) {
            planAssetUpdateMap.forEach((planId, count) -> {
                try {
                    fgMarketService.updateAssetWaitCount(CreativeUpdateAssetWaitCountVO.builder().count(count).planId(planId).build());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
        }
        //发送告警信息
        if (!noticePlanMap.isEmpty()) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("【归档操作执行告警】\n").append("由于计划下投放中创意不足").append(surplusCount).append("个，请人工介入 \n");
            noticePlanMap.forEach((planId, creativeUnitIds) ->
                    stringBuilder.append("计划:").append(planId).append(" 下创意单元: ").append(String.join(",", creativeUnitIds)).append(" \n")
            );
            log.info(stringBuilder.toString());
            //取消告警
//            this.putMonitorNoticeService.sendNotice(putMonitor.getNoticeConfig(), stringBuilder.toString());
        }
        //再执行一次上新功能，补充归档的创意
        if (1 == close.getIsAssetUpdate()) {
            closeVOList.stream().map(u -> u.getInfo().getPlanId()).distinct()
                    .forEach(planId -> {
                        try {
                            fgMarketService.assetCrativeAddPlan(planId, putMonitor.getId());
                        } catch (Exception e) {
                            log.info("监控ID:{} 计划ID:{} 执行自动上新失败 {}", putMonitor.getId(), planId, e.getMessage(), e);
                        }
                    });
        }
    }

    /**
     * 根据信息获取数据
     *
     * @param notices 设置
     * @param dimList 维度
     * @param close   关闭设置
     * @return 返回数据
     */
    private List<CloseVO> findDimensionMap(List<PutMonitorNotice> notices, List<Integer> dimList, Close close) {
        List<CloseVO> result = new ArrayList<>();
        for (PutMonitorNotice notice : notices) {
            String dimInfoId = putMonitorNoticeService.buildKey(notice.getDimensionInfo(), notice.getDimensionId());
            PlanByInfoGetVO planByInfoGetVO = new PlanByInfoGetVO();
            CloseVO closeVO = new CloseVO();
            closeVO.setNoticeId(notice.getId());
            closeVO.setMasterId(notice.getMasterId());
            String[] splits = putMonitorNoticeService.splitKey(dimInfoId);
            int set = 0;
            for (int i = 0; i < dimList.size(); i++) {
                String val = splits[i];
                Integer dim = dimList.get(i);
                if (close.getReplaceDimension().equals(dim)) {
                    closeVO.setExclude(val);
                }
                MonitorIdentifyEnum identifyEnum = ICommonEnum.get(dimList.get(i), MonitorIdentifyEnum.class);
                switch (identifyEnum) {
                    case MASTER:
                        planByInfoGetVO.setMasterId(Integer.parseInt(val));
                        set++;
                        break;
                    case PLAN:
                        planByInfoGetVO.setPlanId(Long.parseLong(val));
                        set++;
                        break;
                    case CAMPAIGN:
                        planByInfoGetVO.setCampaignId(Long.parseLong(val));
                        set++;
                        break;
                    case ADX:
                        planByInfoGetVO.setAdxId(Integer.parseInt(val));
                        set++;
                        break;
                    case EP:
                        planByInfoGetVO.setEpIds(List.of(Integer.parseInt(val)));
                        set++;
                        break;
                    case SLOT:
                        planByInfoGetVO.setSlotType(Integer.parseInt(val));
                        set++;
                        break;
                    case CREATIVE_UNIT:
                        planByInfoGetVO.setCreativeUnitId(Long.parseLong(val));
                        set++;
                        break;
                    default:
                }
            }
            if (set != 0 && StringUtils.isNotBlank(closeVO.getExclude())) {
                closeVO.setInfo(planByInfoGetVO);
                result.add(closeVO);
            }
        }
        return result;
    }

    @Data
    public static class CloseVO {

        private PlanByInfoGetVO info;

        private String exclude;

        private Long noticeId;

        private Integer masterId;

    }

    @Data
    public static class Close {

        private List<Integer> searchDimension;

        private Integer replaceDimension;

        private Integer isAssetUpdate;

        private Integer isSurplusNotice;

        private Integer surplusCount;
    }

    @Data
    @Builder
    public static class CloseRest {

        private HashSet<Long> noticeIds;

        private HashSet<Long> planIds;
    }
}
