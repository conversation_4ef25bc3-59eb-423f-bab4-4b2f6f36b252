package com.overseas.service.monitor.controller;

import com.overseas.common.dto.R;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.vo.monitor.put.PutDataVO;
import com.overseas.service.monitor.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/monitor/puts")
@Slf4j
@RequiredArgsConstructor
public class MonitorController {

    private final PutMonitorService putMonitorService;

    private final PutNoticeDayService putNoticeDayService;

    private final PutAdxService putAdxService;

    private final PutRtaService putRtaService;

    private final PutMonitorOperateService putMonitorOperateService;

    @RequestMapping("/start/day")
    public R start(@RequestParam(name = "id", defaultValue = "0") Long id,
                   @RequestParam(name = "day", defaultValue = "") String day) {
        putMonitorService.putMonitor(id, DateUtils.string2Long(day));
        return R.ok();
    }

    @RequestMapping("/start")
    public R start(@RequestParam(name = "id", defaultValue = "0") Long id,
                   @RequestParam(name = "day", defaultValue = "") Long day) {
        putMonitorService.putMonitor(id, day);
        return R.ok();
    }

    @RequestMapping("/data")
    public R data(@RequestBody @Validated PutDataVO putDataVO) {
        putMonitorService.putData(putDataVO);
        return R.ok();
    }

    @RequestMapping("/data/{id}")
    public R getData(@PathVariable(name = "id") Long id, @RequestBody @Validated PutDataVO putDataVO) {
        putMonitorService.putData(id, putDataVO);
        return R.ok();
    }

    @RequestMapping("/get/data")
    public R getData(@RequestBody @Validated PutDataVO putDataVO) {
        return R.data(putMonitorService.getData(putDataVO));
    }

    @RequestMapping("/check")
    public R check(@RequestParam(name = "id", defaultValue = "0") Long id,
                   @RequestParam(name = "day", defaultValue = "") Long day) {
        putMonitorService.putCheck(id, day);
        return R.ok();
    }

    @RequestMapping("/check/day")
    public R check(@RequestParam(name = "id", defaultValue = "0") Long id,
                   @RequestParam(name = "day", defaultValue = "") String day) {
        putMonitorService.putCheck(id, DateUtils.string2Long(day));
        return R.ok();
    }

    @RequestMapping("/notice/day")
    public R noticeDay(@RequestParam(name = "id", defaultValue = "0") Long id,
                       @RequestParam(name = "day", defaultValue = "") Long day,
                       HttpServletRequest request) {
        String token = request.getHeader("Access-Token");
        putNoticeDayService.notice(id, token, DateUtils.long2Date(day));
        return R.ok();
    }

    @RequestMapping("/adx/notice")
    public R adxNotice(@RequestParam(name = "date", defaultValue = "") String date) {
        putAdxService.checkAdxData(date);
        return R.ok();
    }

    @RequestMapping("/rta/notice")
    public R rtaNotice(@RequestParam(name = "date", defaultValue = "0") Long date) {
        putRtaService.checkRtaData(date);
        return R.ok();
    }

    @RequestMapping("/reset/day")
    public R restDay(@RequestParam(name = "day", defaultValue = "") String day) {
        putMonitorOperateService.reset(DateUtils.string2Date(day));
        return R.ok();
    }

    @RequestMapping("/reset/day/time")
    public R restDayTime(@RequestParam(name = "day", defaultValue = "") Long day) {
        putMonitorOperateService.reset(DateUtils.long2Date(day));
        return R.ok();
    }

}
