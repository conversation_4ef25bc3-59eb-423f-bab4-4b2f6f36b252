package com.overseas.service.monitor.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.monitor.dto.monitor.put.PutMonitorDTO;
import com.overseas.service.monitor.dto.monitor.task.PutMonitorTaskListDTO;
import com.overseas.service.monitor.entity.PutMonitor;
import com.overseas.service.monitor.entity.PutMonitorData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 **/
public interface PutMonitorMapper extends BaseMapper<PutMonitor> {

    /**
     * 根据ID获取数据
     *
     * @param id ID
     * @return 返回数据
     */
    @Select("SELECT * from tm_put_monitor where  id = #{id}")
    PutMonitorDTO findById(Serializable id);

    /**
     * 获取数据
     *
     * @param wrapper 筛选条件
     * @return 返回数据
     */
    @Select("SELECT  * from tm_put_monitor ${ew.customSqlSegment}")
    List<PutMonitorData> selectDto(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 获取数据分页消息
     *
     * @param iPage   分页
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT * FROM tm_put_monitor ${ew.customSqlSegment} ")
    IPage<PutMonitorTaskListDTO> list(IPage<?> iPage, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 根据ID 项目账户
     *
     * @param moduleMaster 项目账户
     * @param id           id
     */
    @Update("UPDATE tm_put_monitor SET module_master = #{moduleMaster} WHERE id = #{id} ")
    void updateMasterById(@Param("moduleMaster") String moduleMaster, @Param("id") Long id);
}
