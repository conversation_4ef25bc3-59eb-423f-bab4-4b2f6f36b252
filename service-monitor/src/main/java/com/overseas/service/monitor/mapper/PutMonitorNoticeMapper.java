package com.overseas.service.monitor.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.monitor.dto.monitor.task.PutMonitorTaskLogCreativeListDTO;
import com.overseas.service.monitor.entity.PutMonitorNotice;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface PutMonitorNoticeMapper extends BaseMapper<PutMonitorNotice> {


    /**
     * 根据信息获取 最大max 数据
     *
     * @param wrapper 筛选条件
     * @return 返回睡觉
     */
    @Select("SELECT * from tm_put_monitor_notice where id in (" +
            "   SELECT max(id) from tm_put_monitor_notice ${ew.customSqlSegment}" +
            ")")
    List<PutMonitorNotice> findMaxInfo(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 获取需要恢复的数据
     *
     * @param wrapper 条件
     * @return 返回数据
     */

    @Select("SELECT tpmn.* from tm_put_monitor_notice tpmn " +
            "left join tm_put_monitor tpm ON tpmn.put_monitor_id = tpm.id " +
            "left join m_master mm ON  tpmn.master_id = mm.user_id " +
            "${ew.customSqlSegment} ")
    List<PutMonitorNotice> findNoticeByNeedReset(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    /**
     * 获取创意列表信息
     *
     * @param iPage   分页数据
     * @param wrapper 筛选条件
     * @return 返回数据
     */
    @Select("SELECT tpmn.id, tpmn.dimension_id as creative_unit_id, mcu.plan_id, mp.plan_name, tpmn.create_time as notice_time, " +
            "tpmn.notice_data, ma.id as asset_id ,ma.asset_type, ma.content, ma.is_upload, ma.cover_img_id " +
            "FROM tm_put_monitor_notice tpmn " +
            "LEFT JOIN m_creative_unit mcu ON tpmn.dimension_id = mcu.id " +
            "LEFT JOIN m_plan mp ON mcu.plan_id = mp.id  " +
            "LEFT JOIN m_material_asset mma ON mcu.material_id = mma.material_id AND mma.field_type = 1  " +
            "LEFT JOIN m_asset  ma On mma.asset_id = ma.id " +
            "${ew.customSqlSegment}")
    IPage<PutMonitorTaskLogCreativeListDTO> listLogCreative(IPage<?> iPage, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
