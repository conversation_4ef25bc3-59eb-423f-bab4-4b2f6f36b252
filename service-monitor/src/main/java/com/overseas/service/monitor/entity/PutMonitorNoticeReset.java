package com.overseas.service.monitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 **/
@TableName("tm_put_monitor_notice_reset")
@Data
public class PutMonitorNoticeReset {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long putNoticeId;

    private Integer setType;

    private Long setId;

    private String setVal;

    private Integer isReset;

    private String resetTime;

    private String createTime;
}
