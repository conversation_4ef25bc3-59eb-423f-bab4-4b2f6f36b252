package com.overseas.service.monitor.dto.monitor.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@ApiModel
public class PutMonitorTaskListDTO {

    @ApiModelProperty("监控ID")
    private Long id;

    @ApiModelProperty("监控名称")
    private String putMonitorName;

    @ApiModelProperty("监控类型")
    private Integer monitorType;

    @ApiModelProperty("监控类型名称")
    private String monitorTypeName;

    @ApiModelProperty("监控指定数据维度")
    private Integer dimension;

    @ApiModelProperty("监控维度")
    private List<Integer> monitorDimension;

    @ApiModelProperty(value = "监控维度", hidden = true)
    private String monitorDimensions;

    @ApiModelProperty("时间间隔")
    private Integer timeCycle;

    @ApiModelProperty("类型")
    private Integer timeCycleUnit;

    @JSONField(serialize = false, deserialize = false)
    private String conditions;

    @ApiModelProperty("规则字符")
    private List<String> conditionStr;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date noticeTime;

    @ApiModelProperty("备注信息")
    private String notice;

    @ApiModelProperty("是否top")
    private Integer isTop;

    @ApiModelProperty("top配置")
    private Object topConfig;

    @ApiModelProperty("投放状态")
    private Integer putStatus;

    @ApiModelProperty("投放状态名称")
    private String putStatusName;

}
