package com.overseas.service.monitor.vo.monitor.task;

import com.overseas.common.validation.annotation.MpIn;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PutMonitorNoticeConfigVO {

    @ApiModelProperty("通知类型:1:飞书；2:钉钉,3:系统消息,4:短信；5:i讯飞")
    @MpIn(values = {"1", "2", "3", "4", "5"}, message = "通知类型不合法")
    @NotNull(message = "通知类型不能为空")
    private Integer hookType;

    @ApiModelProperty("通知对象")
    @NotBlank(message = "通知对象不能为空")
    private String hookUrl;

    @ApiModelProperty("通知token")
//    @NotBlank(message = "通知密钥不能为空")
    private String hookToken;
}
