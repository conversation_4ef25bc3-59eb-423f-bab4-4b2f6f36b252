package com.overseas.service.monitor.common.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 **/
@Data
public class AeConfiguration {

    private boolean start;

    private Long agentId;

    private String identify;

    private String masterIds;

    private String token;

    private String url;

    private String fields;

    private String demoContent;
}
