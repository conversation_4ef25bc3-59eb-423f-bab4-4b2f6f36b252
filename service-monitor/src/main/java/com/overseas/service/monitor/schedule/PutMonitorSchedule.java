package com.overseas.service.monitor.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.utils.DateUtils;
import com.overseas.service.monitor.entity.PutMonitor;
import com.overseas.service.monitor.feign.FgMonitorService;
import com.overseas.service.monitor.mapper.PutMonitorMapper;
import com.overseas.service.monitor.service.PutMonitorOperateService;
import com.overseas.service.monitor.service.PutMonitorUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Slf4j
@Component
@Profile(value = {"test2", "online"})
@RefreshScope
public class PutMonitorSchedule {

    private final PutMonitorMapper putMonitorMapper;

    private final FgMonitorService fgMonitorService;

    private final ThreadPoolTaskExecutor putMonitorThreadPool;

    private final PutMonitorUserService putMonitorUserService;

    private final PutMonitorOperateService putMonitorOperateService;

    @Value("${adx.notice.is_check:false}")
    private Boolean isCheckAdxNotice;

    @Scheduled(cron = "0 */5 * * * ?")
    public void schedule() {
        log.info("监控执行开始");
        String token = putMonitorUserService.getUserToken();
        log.info("Access-Token :{}", token);
        Long day = DateUtils.string2Long(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN).substring(0, 17) + "00");
        putMonitorMapper.selectList(new LambdaQueryWrapper<PutMonitor>().eq(PutMonitor::getPutStatus, 2)
                .eq(PutMonitor::getIsDel, IsDelEnum.NORMAL.getId())
        ).forEach(putMonitor ->
                CompletableFuture.runAsync(() -> {
                    try {
                        log.info("监控执行开始：{}, 时间:{} ", putMonitor.getId(), day);
                        fgMonitorService.monitorStart(putMonitor.getId(), day, token);
                        log.info("监控执行结束：{} ", putMonitor.getId());
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }, putMonitorThreadPool)
        );
        log.info("监控执行结束");

    }

    /**
     * 恢复执行，每个小时3分钟执行
     */
    @Scheduled(cron = "0 3 */1 * * ?")
    public void scheduleReset() {
        Date today = new Date();
        log.info("监控恢复开始 :{}", DateUtils.format(today, DateUtils.DATE_TIME_PATTERN));
        fgMonitorService.resetDay(DateUtils.date2Long(today), putMonitorUserService.getUserToken());
        log.info("监控恢复结束 :{}", DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
    }

    /**
     * adx 校验，每天 13点40，16点40执行
     */
    @Scheduled(cron = "0 40 13,16 * * ?")
    public void scheduleAdx() {
        if (!isCheckAdxNotice) {
            return;
        }
        log.info("adx notice is start");
        String token = putMonitorUserService.getUserToken();
        fgMonitorService.adxNotice(DateUtils.format(DateUtils.afterDay(new Date(), -1)), token);
    }

    /**
     * adx 校验，每天 13点40，16点40执行
     */
    @Scheduled(cron = "0 10 */1 * * ?")
    public void scheduleRtaPassRate() {
        if (!isCheckAdxNotice) {
            return;
        }
        log.info("pass rate notice is start");
        String token = putMonitorUserService.getUserToken();
        fgMonitorService.rtaNotice(DateUtils.date2Long(new Date()), token);
    }


    //    @Scheduled(cron = "0 0 */1 * * ?")
//    public void scheduleMonitor() {
//        log.info("日报任务执行开始");
//        Long day = DateUtils.string2Long(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN).substring(0, 17) + "00");
//        String token = putMonitorUserService.getUserToken();
//        putNoticeDayMapper.selectList(new LambdaQueryWrapper<PutNoticeDay>()
//                .eq(PutNoticeDay::getPutStatus, 2)
//                .eq(PutNoticeDay::getIsDel, IsDelEnum.NORMAL.getId())
//        ).forEach(u -> {
//            CompletableFuture.runAsync(() -> {
//                try {
//                    fgMonitorService.noticeDay(u.getId(), day, token);
//                } catch (Exception e) {
//                    log.error(e.getMessage(), e);
//                }
//            }, putMonitorThreadPool);
//        });
//        log.info("日报任务执行结束");
//    }

}
