package com.overseas.service.monitor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitPutStatusDTO;
import com.overseas.common.dto.market.market.MarketDimensionInfoDTO;
import com.overseas.common.dto.market.master.MasterTimeZoneDTO;
import com.overseas.common.dto.monitor.put.*;
import com.overseas.common.dto.sys.customIndex.CustomIndexChildColumnDTO;
import com.overseas.common.entity.User;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.PlanSlotTypeEnum;
import com.overseas.common.enums.sys.msg.MsgIndustryEnum;
import com.overseas.common.enums.sys.msg.MsgTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.creative.CreativeUnitPutStatusVO;
import com.overseas.common.vo.market.market.MarketDimensionInfoVO;
import com.overseas.common.vo.market.market.MarketMasterGetVO;
import com.overseas.common.vo.market.master.MasterTimeZoneGetVO;
import com.overseas.common.vo.market.plan.direct.AdxSelectGetVO;
import com.overseas.common.vo.market.plan.direct.EpSelectGetVO;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.common.vo.sys.msg.SysMsgSendVO;
import com.overseas.service.monitor.dto.monitor.put.PutMonitorDTO;
import com.overseas.service.monitor.entity.PutMonitor;
import com.overseas.service.monitor.entity.PutMonitorNotice;
import com.overseas.service.monitor.enums.MonitorIdentifyEnum;
import com.overseas.service.monitor.enums.MonitorOperateTypeEnum;
import com.overseas.service.monitor.enums.MonitorUnitEnum;
import com.overseas.service.monitor.feign.FgMarketService;
import com.overseas.service.monitor.feign.FgSystemService;
import com.overseas.service.monitor.mapper.PutMonitorNoticeMapper;
import com.overseas.service.monitor.service.PutMonitorNoticeService;
import com.overseas.service.monitor.utils.ShaUtils;
import com.overseas.service.monitor.vo.monitor.task.PutMonitorNoticeConfigVO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Service
@RequiredArgsConstructor
@Slf4j
@RefreshScope
public class PutMonitorNoticeServiceImpl implements PutMonitorNoticeService {

    @Value("${center.budget.url}")
    private String budgetUrl;

    private final SheinConfiguration sheinConfiguration;

    private final PutMonitorNoticeMapper putMonitorNoticeMapper;

    private final FgMarketService fgMarketService;

    private final FgSystemService fgSystemService;

    private final SmsUtils smsUtils;

    @Override
    public List<PutMonitorNotice> notice(CheckDateDTO checkDateDTO, PutMonitorDTO putMonitor,
                                         Map<String, ErrConditionDTO> dimensionIdMap) {
        log.info("监控告警，监控ID:{}，错误内容:{}", putMonitor.getId(), JSONObject.toJSONString(dimensionIdMap));
        if (dimensionIdMap.isEmpty()) {
            return List.of();
        }
        //判定数据相同，恢复等逻辑，处理完毕
        if (null == putMonitor.getExcludeSameData() || 1 == putMonitor.getExcludeSameData()) {
            checkDataNeedNotice(checkDateDTO, putMonitor, dimensionIdMap);
        }
        //判定处理创意单元是否投放 -- 后续增加计划，活动
        checkCreativeIsPut(checkDateDTO, putMonitor, dimensionIdMap);
        if (dimensionIdMap.isEmpty()) {
            log.info("经过已告警排除，发现可告警信息为空");
            return List.of();
        }
        //预算控制调用
        List<String[]> keyList = dimensionIdMap.keySet().stream().map(u -> u.split(" -- ")).collect(Collectors.toList());
        String resp = "{}";
        MonitorIdentifyEnum checkMonitor = MonitorIdentifyEnum.needCheck(putMonitor.getMonitorDimensionList());
        try {
            if (null != checkMonitor) {
                int index = putMonitor.getMonitorDimensionList().indexOf(checkMonitor.getId());
                resp = HttpUtils.postObject(budgetUrl + "/querystatus",
                        keyList.stream().map(u -> String.format("%s_%s", checkMonitor.getVal(), u[index])).collect(Collectors.toList()),
                        null);
            }
        } catch (Exception e) {
            log.info(e.getMessage(), e);
        }
        Map<String, Bud> budMap = JSONObject.parseObject(resp, new TypeReference<>() {
        });
        List<String> del = new ArrayList<>();
        dimensionIdMap.forEach((dimensionId, msg) -> {
            Bud bud = null;
            if (null != checkMonitor) {
                bud = budMap.get(String.format("%s_%s", checkMonitor.getVal(), dimensionId));
            }
            if (null == bud) {
                bud = new Bud();
                bud.need_stop = false;
            }
            if (bud.isNeed_stop()) {
                del.add(dimensionId);
                log.info("判定{}数据异常，但是由于不在投放中，不进行告警：原因：{}", dimensionId, bud.getMsg());
            }
        });
        //删除不需要告警的
        for (String de : del) {
            dimensionIdMap.remove(de);
        }
        if (dimensionIdMap.isEmpty()) {
            log.info("经过投放排除，发现可告警信息名称为空");
            return List.of();
        }
        return dimensionIdMap.values().stream().map(msg -> {
                    PutMonitorNotice putMonitorNotice = new PutMonitorNotice();
                    putMonitorNotice.setPutMonitorId(putMonitor.getId());
                    putMonitorNotice.setMasterId(msg.getMasterId());
                    putMonitorNotice.setDimensionId(msg.getDimensionId());
                    putMonitorNotice.setDimensionName(msg.getDimensionName());
                    putMonitorNotice.setDimensionInfo(msg.getDimensionInfo());
                    putMonitorNotice.setNoticeData(JSONObject.toJSONString(msg.getConditions()));
                    putMonitorNotice.setNoticeTime(checkDateDTO.getTimestamp());
                    putMonitorNoticeMapper.insert(putMonitorNotice);
                    msg.setNoticeId(putMonitorNotice.getId());
                    return putMonitorNotice;
                }
        ).collect(Collectors.toList());
    }

    @Override
    public void restore(CheckDateDTO checkDateDTO, PutMonitorDTO putMonitor, Map<String, ErrConditionDTO> dimensionIdMap) {
        log.info("恢复告警，监控ID:{}，恢复ID:{}", putMonitor.getId(), JSONObject.toJSONString(dimensionIdMap.keySet()));
        if (dimensionIdMap.isEmpty()) {
            return;
        }
        List<String> noticeIds = putMonitorNoticeMapper.selectList(new LambdaQueryWrapper<PutMonitorNotice>()
                .eq(PutMonitorNotice::getPutMonitorId, putMonitor.getId())
                .eq(PutMonitorNotice::getNoticeTime, noticeTime(checkDateDTO.getEnd1()))
                .in(PutMonitorNotice::getDimensionId, dimensionIdMap.values().stream().map(ErrConditionDTO::getDimensionId).collect(Collectors.toList()))
                .eq(PutMonitorNotice::getIsRestore, 0)
        ).stream().map(PutMonitorNotice::getDimensionId).collect(Collectors.toList());
        noticeBuild("【监控恢复告警】\n", checkDateDTO, putMonitor, dimensionIdMap);
        PutMonitorNotice update = new PutMonitorNotice();
        update.setIsRestore(1);
        putMonitorNoticeMapper.update(update, new LambdaQueryWrapper<PutMonitorNotice>()
                .eq(PutMonitorNotice::getPutMonitorId, putMonitor.getId())
                .eq(PutMonitorNotice::getNoticeTime, noticeTime(checkDateDTO.getEnd1()))
                .in(PutMonitorNotice::getDimensionId, noticeIds)
                .eq(PutMonitorNotice::getIsRestore, 0));

    }

    @Override
    public void sendNotice(String config, String content) {
        JSONObject.parseArray(config, PutMonitorNoticeConfigVO.class).forEach(configVO -> this.sendNotice(configVO, content));
    }

    @Override
    public void sendNotice(PutMonitorNoticeConfigVO configVO, String content) {
        try {
            this.sendContent(content, configVO);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void sendNotice(String content) {
//        smsUtils.sendMsg("海外监控告警", content, List.of("15556985735", "18756052799"));
    }

    @Override
    public void sendMarkDown(PutMonitorNoticeConfigVO configVO, String title, String text) {
        try {
            switch (configVO.getHookType()) {
                case 1:
                    sendMsg(new HashMap<>(8) {{
                        put("msg_type", "text");
                        put("content", Map.of("text", text));
                    }}, configVO);
                    break;
                case 2:
                    sendMsg(new HashMap<>(8) {{
                        put("msgtype", "markdown");
                        put("markdown", Map.of("title", title, "text", text));
                    }}, configVO);
                    break;
                case 3:
                    sendMsg(new HashMap<>() {{
                        put("content", text);
                        put("title", title);
                    }}, configVO);
                    break;
                default:
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void sendNotice(PutNoticeDayServiceImpl.SendNoticeConfig sendNoticeConfig,
                           List<JSONObject> data, List<JSONObject> yesterday,
                           PutMonitorDTO putMonitor) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        Map<String, JSONObject> yesterdayData = null == yesterday ? new HashMap<>(8) :
                yesterday.stream()
                        .collect(Collectors.toMap(u -> buildKey(u.getString("dimensionInfo"), u.getString("dimensionId")),
                                Function.identity()));
        StringBuilder stringBuilder = new StringBuilder(sendNoticeConfig.getContent());
        //含有字段
        List<String> fields = JSONObject.parseArray(putMonitor.getNotice(), String.class);
        //获取监测信息名称
        Map<Integer, Map<String, MarketDimensionInfoDTO>> nameMap = findMapName(putMonitor.getMonitorDimensionList(),
                data.stream().filter(u -> !"-1".equals(u.getString("dimensionId")))
                        .map(u -> buildKey(u.getString("dimensionInfo"), u.getString("dimensionId")).split(" -- "))
                        .collect(Collectors.toList())
        );
        //获取identify enums 数据
        List<JSONObject> tableData = new ArrayList<>();
        List<MonitorIdentifyEnum> identifyEnums = putMonitor.getMonitorDimensionList().stream()
                .map(u -> ICommonEnum.get(u, MonitorIdentifyEnum.class)).collect(Collectors.toList());
        List<SelectDTO2> tableHeader = identifyEnums.stream().map(u -> new SelectDTO2(u.getId(), u.getName())).collect(Collectors.toList());
        //获取字段名称
        Map<String, String> filedNameMap = fgSystemService.getCustomIndexReportDownload(CustomIndexGetVO.builder()
                        .module(putMonitor.getModule())
                        .identify(putMonitor.getModuleIdentify())
                        .build()
                ).getData()
                .stream().flatMap(u -> u.getColumns().stream())
                .collect(Collectors.toMap(CustomIndexChildColumnDTO::getKey, CustomIndexChildColumnDTO::getTitle));
        AtomicInteger setHeader = new AtomicInteger(2);
        data.forEach(todayDatum -> {
            String key = buildKey(todayDatum.getString("dimensionInfo"), todayDatum.getString("dimensionId"));
            StringBuilder oneStringBuilder = new StringBuilder();
            JSONObject tableOne;
            //设置维度名称
            if ("-1".equals(key)) {
                oneStringBuilder.append("【总计】\n");
                tableOne = new JSONObject();
            } else {
                oneStringBuilder.append(buildName(nameMap, key, identifyEnums)).append("\n");
                tableOne = buildNameData(nameMap, key, identifyEnums);
            }
            //设置单条数据
            JSONObject yesterdayDatum = yesterdayData.get(key);
            int idx = 0;
            for (int i = 0; i < fields.size(); i++) {
                String field = fields.get(i);
                idx++;
                String fieldName = filedNameMap.get(field);
                switch (putMonitor.getCompareCycle()) {
                    //上一个周期
                    case 1:
                        oneStringBuilder.append(
                                buildFieldVal(todayDatum, yesterdayDatum,
                                        field, fieldName,
                                        "本期", "上期",
                                        tableOne, setHeader.get() == 2 ? tableHeader : null
                                ));
                        break;
                    //昨日
                    case 2:
                        oneStringBuilder.append(
                                buildFieldVal(todayDatum, yesterdayDatum,
                                        field, fieldName,
                                        String.format("%s日", DateUtils.format(sendNoticeConfig.getTodayDate(), "dd")),
                                        String.format("%s日", DateUtils.format(sendNoticeConfig.getYesterdayDate(), "dd")),
                                        tableOne, setHeader.get() == 2 ? tableHeader : null
                                ));
                        break;
                    //无比较
                    case 3:
                        oneStringBuilder.append(fieldName).append("：").append(todayDatum.getString(field));
                        if (setHeader.get() == 2) {
                            tableHeader.add(new SelectDTO2(String.format("now_%s", field), fieldName));
                        }
                        tableOne.put(String.format("now_%s", field), todayDatum.getString(field));
                    default:
                }
                boolean endWithN = oneStringBuilder.toString().endsWith("\n");
                if (idx % 2 == 0 && !endWithN) {
                    oneStringBuilder.append("\n");
                } else if (!endWithN) {
                    oneStringBuilder.append("，");
                }
            }
            stringBuilder.append(oneStringBuilder);
            if (!oneStringBuilder.toString().endsWith("\n")) {
                stringBuilder.append("\n");
            }
            if (!"-1".equals(key)) {
                setHeader.set(3);
            }
            tableData.add(tableOne);
        });
        for (PutMonitorNoticeConfigVO config : sendNoticeConfig.getConfigVO()) {
            try {
                if (config.getHookType().equals(3)) {
                    sendSysMsg("【数据通知】", new JSONObject() {{
                        put("content", String.format("%s [[table]]", sendNoticeConfig.getContent().replaceAll("\n", "</br>")));
                        put("tables", List.of(new JSONObject() {{
                            put("header", tableHeader);
                            put("data", tableData);
                        }}));
                    }}, MsgIndustryEnum.MONITOR_DATA.getId(), findUserByMonitor(putMonitor));
                } else {
                    this.sendContent(stringBuilder.toString(), config);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 封装发送notice
     *
     * @param content    头
     * @param putMonitor 监控
     * @param conditions 判定条件
     */
    @Override
    public void noticeBuild(String content, CheckDateDTO checkDateDTO, PutMonitorDTO putMonitor,
                            Map<String, ErrConditionDTO> conditions) {
        if (conditions.isEmpty()) {
            return;
        }
        List<String[]> keyList = conditions.keySet().stream().map(u -> u.split(" -- ")).collect(Collectors.toList());
        Map<Integer, Map<String, MarketDimensionInfoDTO>> dimensionNameMap = findMapName(putMonitor.getMonitorDimensionList(), keyList);
        if (dimensionNameMap.isEmpty()) {
            log.info("经过名称排除，发现可告警信息名称为空");
        }
        StringBuilder header = new StringBuilder()
                .append("监控名称：").append(putMonitor.getPutMonitorName()).append("\n")
                .append("告警原因：").append(getNoticeReason(putMonitor)).append("\n")
                .append("判定东八区时间：");
        if (checkDateDTO.getStart1().equals(-101L)) {
            header.append("累计至 ")
                    .append(DateUtils.format(DateUtils.long2Date(checkDateDTO.getEnd1()), "yyyy-MM-dd HH:mm"))
                    .append("\n");
        } else {
            header.append(ObjectUtils.isNullOrZero(checkDateDTO.getStart1()) ?
                            DateUtils.format(DateUtils.long2Date(checkDateDTO.getEnd1()), "yyyy-MM-dd 00:00") :
                            DateUtils.format(DateUtils.long2Date(checkDateDTO.getStart1()), "yyyy-MM-dd HH:mm")
                    ).append(" ~ ")
                    .append(DateUtils.format(DateUtils.long2Date(checkDateDTO.getEnd1()), "yyyy-MM-dd HH:mm"))
                    .append("\n");
        }
        if (StringUtils.isNotBlank(putMonitor.getNotice())) {
            header.append("触发描述：").append(putMonitor.getNotice()).append("\n");
        }
        StringBuilder stringBuilder = new StringBuilder();
        List<JSONObject> tableData = new ArrayList<>();
        List<MonitorIdentifyEnum> identifyEnums = putMonitor.getMonitorDimensionList()
                .stream().map(u -> ICommonEnum.get(u, MonitorIdentifyEnum.class))
                .collect(Collectors.toList());
        List<SelectDTO2> tableHeader = identifyEnums.stream().map(u -> new SelectDTO2(u.getId(), u.getName())).collect(Collectors.toList());
        AtomicBoolean setHeader = new AtomicBoolean(true);
        conditions.forEach((dimKey, errConditionDTO) -> {
            //设置维度key name；
            errConditionDTO.setDimensionName(dimensionNameMap.get(dimensionNameMap.size() - 1).get(errConditionDTO.getDimensionId()).getName());
            //补充文本数据
            stringBuilder.append(buildName(dimensionNameMap, dimKey, identifyEnums)).append("\n");
            //表格数据
            JSONObject tableOne = buildNameData(dimensionNameMap, dimKey, identifyEnums);
            if (MonitorIdentifyEnum.MASTER.getId().equals(putMonitor.getMonitorDim())) {
                String[] dims = dimKey.split(" -- ");
                MarketDimensionInfoDTO master = dimensionNameMap.get(dimensionNameMap.size() - 1).get(dims[dims.length - 1]);
                stringBuilder.append("所属时区：").append(TimeZoneEnum.getNameById(master.getTimeZone()))
                        .append("  ");
                String timeRange = String.format("%s ~ %s",
                        ObjectUtils.isNotNullOrZero(checkDateDTO.getStart1()) ?
                                DateUtils.format(TimeZoneEnum.getTimeZoneDate(
                                        DateUtils.long2Date(checkDateDTO.getStart1()), master.getTimeZone(), 0
                                ), "yyyy-MM-dd HH:mm") :
                                DateUtils.format(TimeZoneEnum.getTimeZoneDate(
                                        DateUtils.long2Date(checkDateDTO.getEnd1()), master.getTimeZone(), 0
                                ), "yyyy-MM-dd 00:00"),
                        DateUtils.format(TimeZoneEnum.getTimeZoneDate(
                                DateUtils.long2Date(checkDateDTO.getEnd1()), master.getTimeZone(), 0
                        ), "yyyy-MM-dd HH:mm"));
                stringBuilder.append(" 时间段: ").append(timeRange).append("\n");
                tableOne.put("timezone", TimeZoneEnum.getNameById(master.getTimeZone()));
                tableOne.put("timeRange", timeRange);
                if (setHeader.get()) {
                    tableHeader.add(new SelectDTO2("timezone", "时区"));
                    tableHeader.add(new SelectDTO2("timeRange", "时间段"));
                }
            }
            for (ConditionRDTO cond : errConditionDTO.getConditions()) {
                //降低，提高
                if (null != cond.getDiffVal()) {
                    stringBuilder.append(cond.getTargetName())
                            .append("：当前：").append(cond.getLastDiff())
                            .append("，上期：").append(cond.getLast2Diff())
                            .append("，");
                    tableOne.put(String.format("now_%s", cond.getTarget()), cond.getLastDiff());
                    tableOne.put(String.format("last_%s", cond.getTarget()), cond.getLast2Diff());
                    if (setHeader.get()) {
                        tableHeader.add(new SelectDTO2(String.format("now_%s", cond.getTarget()), String.format("当前%s", cond.getTargetName())));
                        tableHeader.add(new SelectDTO2(String.format("last_%s", cond.getTarget()), String.format("上期%s", cond.getTargetName())));
                    }
                    if (MonitorUnitEnum.VALUE.getId().equals(cond.getUnit())) {
                        stringBuilder.append("偏差值：").append(cond.getDiff());
                        tableOne.put(String.format("diff_%s", cond.getTarget()), cond.getDiff());
                        if (setHeader.get()) {
                            tableHeader.add(new SelectDTO2(String.format("diff_%s", cond.getTarget()), String.format("%s偏差值", cond.getTargetName())));
                        }
                    } else {
                        tableOne.put(String.format("diff_%s", cond.getTarget()), cond.getDiffVal());
                        tableOne.put(String.format("diff_rate_%s", cond.getTarget()), cond.getDiff() + "%");
                        stringBuilder.append("偏差值：").append(cond.getDiffVal()).append("，偏差比例：").append(cond.getDiff()).append("%");
                        if (setHeader.get()) {
                            tableHeader.add(new SelectDTO2(String.format("diff_%s", cond.getTarget()), String.format("%s偏差值", cond.getTargetName())));
                            tableHeader.add(new SelectDTO2(String.format("diff_rate_%s", cond.getTarget()), String.format("%s偏差比例", cond.getTargetName())));
                        }
                    }
                } else {
                    //偏差数据
                    stringBuilder.append(cond.getTargetName())
                            .append("：当前：").append(cond.getLastDiff());
                    tableOne.put(String.format("now_%s", cond.getTarget()), cond.getLastDiff());
                    if (setHeader.get()) {
                        tableHeader.add(new SelectDTO2(String.format("now_%s", cond.getTarget()), String.format("当前%s", cond.getTargetName())));
                    }
                }
                //增加日预算
                if (null != cond.getDailyBudget()) {
                    stringBuilder.append("\n日预算：").append(cond.getDailyBudget().stripTrailingZeros().toPlainString());
                    tableOne.put("dailyBudget", cond.getDailyBudget().stripTrailingZeros().toPlainString());
                    if (setHeader.get()) {
                        tableHeader.add(new SelectDTO2("dailyBudget", "日预算"));
                    }
                }
                stringBuilder.append("\n");
            }
            //增加后续行为内容
            if (StringUtils.isNotBlank(errConditionDTO.getOperateVal())) {
                stringBuilder.append("后续操作:").append(errConditionDTO.getOperateVal()).append("\n");
                tableOne.put("operateVal", errConditionDTO.getOperateVal());
                if (setHeader.get()) {
                    tableHeader.add(new SelectDTO2("operateVal", "后续操作"));
                }
            }
            tableData.add(tableOne);
            setHeader.set(false);
        });
        List<PutMonitorNoticeConfigVO> noticeConfig = JSONObject.parseArray(putMonitor.getNoticeConfig(), PutMonitorNoticeConfigVO.class);
        noticeConfig.forEach(config -> {
            try {
                if (config.getHookType().equals(3)) {
                    Map<String, Integer> difMap = tableHeader.stream().collect(Collectors.toMap(u -> u.getKey().toString(), v -> 1, (o1, o2) -> o1));
                    List<SelectDTO2> lastTableHeader = tableHeader.stream().filter(u -> {
                        if (difMap.containsKey(u.getKey().toString())) {
                            difMap.remove(u.getKey().toString());
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                    sendSysMsg("【监控告警】", new JSONObject() {{
                        put("content", String.format("%s [[table]]", header.toString().replaceAll("\n", "</br>")));
                        put("tables", List.of(new JSONObject() {{
                            put("header", lastTableHeader);
                            put("data", tableData);
                        }}));
                    }}, MsgIndustryEnum.MONITOR_PUT.getId(), findUserByMonitor(putMonitor));
                } else {
                    sendContent(content + header + stringBuilder, config);
                }
            } catch (Exception e) {
                log.info("发送告警失败", e);
            }
        });
    }

    /**
     * 获取循环数据的名称
     *
     * @param monitorDimensions 维度数组
     * @param dimensionIds      维度key
     * @return 返回名称
     */
    private Map<Integer, Map<String, MarketDimensionInfoDTO>> findMapName(List<Integer> monitorDimensions,
                                                                          List<String[]> dimensionIds) {
        Map<Integer, List<String>> maps = new HashMap<>(8);
        for (String[] arr : dimensionIds) {
            for (int i = 0; i < arr.length; i++) {
                maps.computeIfAbsent(i, k -> new ArrayList<>()).add(arr[i]);
            }
        }
        Map<Integer, Map<String, MarketDimensionInfoDTO>> result = new HashMap<>();
        maps.forEach((k, v) -> result.put(k, findDimensionName(monitorDimensions.get(k), v)));
        return result;
    }

    /**
     * 获取维度名称
     *
     * @param monitorDimension 监控维度
     * @param dimensionIds     维度ID
     * @return 返回数据
     */
    private Map<String, MarketDimensionInfoDTO> findDimensionName(Integer monitorDimension, List<String> dimensionIds) {
        if (dimensionIds.isEmpty()) {
            return Map.of();
        }
        Map<String, MarketDimensionInfoDTO> dimensionInfoMap = new HashMap<>();
        MonitorIdentifyEnum identifyEnum = ICommonEnum.get(monitorDimension, MonitorIdentifyEnum.class);
        switch (identifyEnum) {
            case PACKAGE:
            case CREATIVE_UNIT:
                for (String dimensionId : dimensionIds) {
                    dimensionInfoMap.put(dimensionId, MarketDimensionInfoDTO.builder().name(dimensionId).build());
                }
                break;
            case COUNTRY:
                dimensionInfoMap = fgSystemService.areaCountrySelect(AreaCountrySelectVO.builder()
                        .countryIds(dimensionIds.stream().map(Long::parseLong).collect(Collectors.toList())).build()
                ).getData().stream().collect(Collectors.toMap(SelectDTO3::getKey, v -> MarketDimensionInfoDTO.builder().name(v.getTitle()).build()));
                break;
            case EP:
                dimensionInfoMap = fgMarketService.epSelect(EpSelectGetVO.builder()
                        .epIds(dimensionIds.stream().map(Long::parseLong).collect(Collectors.toList())).build()
                ).getData().stream().collect(Collectors.toMap(k -> k.getId().toString(), v -> MarketDimensionInfoDTO.builder().name(v.getName()).build()));
                //增加其他
                break;
            case ADX:
                dimensionInfoMap = fgMarketService.adxSelect(AdxSelectGetVO.builder()
                        .adxIds(dimensionIds.stream().map(Long::parseLong).collect(Collectors.toList())).build()
                ).getData().stream().collect(Collectors.toMap(k -> k.getId().toString(), v -> MarketDimensionInfoDTO.builder().name(v.getName()).build()));
                break;
            case SLOT:
                dimensionInfoMap = Arrays.stream(PlanSlotTypeEnum.values())
                        .collect(Collectors.toMap(u -> u.getId().toString(), v -> MarketDimensionInfoDTO.builder().name(v.getName()).build()));
                break;
            default:
                dimensionInfoMap = fgMarketService.marketDimensionInfo(
                                MarketDimensionInfoVO.builder()
                                        .ids(dimensionIds.stream().map(Long::parseLong).collect(Collectors.toList()))
                                        .dimension(monitorDimension).build())
                        .getData().stream().collect(Collectors.toMap(u -> u.getId().toString(), Function.identity()));
        }
        //增加其他
        if (dimensionIds.contains("0")) {
            dimensionInfoMap.put("0", MarketDimensionInfoDTO.builder().name("其他").build());
        }
        return dimensionInfoMap;
    }

    /**
     * 发送内容
     *
     * @param content      内容
     * @param noticeConfig 配置
     *                     hookType: 1: 飞书，2:叮叮，3:系统消息；4:短信；5:i讯飞（飞书）
     */
    private void sendContent(String content, PutMonitorNoticeConfigVO noticeConfig) {
        try {
            switch (noticeConfig.getHookType()) {
                case 1:
                case 5:
                    sendMsg(new HashMap<>(8) {{
                        put("msg_type", "text");
                        put("content", Map.of("text", content));
                    }}, noticeConfig);
                    break;
                case 2:
                    sendMsg(new HashMap<>(8) {{
                        put("msgtype", "text");
                        put("text", Map.of("content", content));
                    }}, noticeConfig);
                    break;
                case 4:
                    sendMsg(new HashMap<>() {{
                        put("content", content);
                    }}, noticeConfig);
                    break;
                default:
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 发送通知
     *
     * @param content      通知内容
     * @param noticeConfig 通知配置
     * @throws Exception 异常
     */
    private void sendMsg(Map<String, Object> content, PutMonitorNoticeConfigVO noticeConfig) throws Exception {
        switch (noticeConfig.getHookType()) {
            //飞书
            case 1:
                //i讯飞飞书
            case 5:
                Long timestamp1 = System.currentTimeMillis() / 1000;
                String sign1 = ShaUtils.sha256_fs(timestamp1 + "\n" + noticeConfig.getHookToken());
                content.putAll(new HashMap<>() {{
                    put("timestamp", timestamp1);
                    put("sign", sign1);
                }});
                String resp1 = HttpUtils.post(noticeConfig.getHookUrl(),
                        content,
                        new HashMap<>() {{
                            put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
                        }});
                JSONObject respObj1 = JSONObject.parseObject(resp1);
                if (0 != respObj1.getInteger("code")) {
                    throw new CustomException(respObj1.getString("msg"));
                }
                break;
            //钉钉
//            case 2:
//                Long timestamp2 = System.currentTimeMillis();
//                String sign2 = ShaUtils.sha256_dd(timestamp2 + "\n" + noticeConfig.getHookToken(), noticeConfig.getHookToken());
//                String resp2 = HttpUtils.post(String.format("%s&timestamp=%s&sign=%s", noticeConfig.getHookUrl(), timestamp2, sign2),
//                        content,
//                        new HashMap<>() {{
//                            put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
//                        }});
//                JSONObject respObj2 = JSONObject.parseObject(resp2);
//                if (0 != respObj2.getInteger("errcode")) {
//                    throw new CustomException(respObj2.getString("errmsg"));
//                }
//                break;
            case 4:
                smsUtils.sendMsg(
                        content.getOrDefault("title", "监控告警").toString(),
                        content.getOrDefault("content", "").toString(),
                        Arrays.stream(noticeConfig.getHookUrl().split(",")).collect(Collectors.toList())
                );
                break;
            default:
        }
        log.info("群组发送信息成功，地址：{} \n 内容: {}\n", noticeConfig.getHookUrl(), content);
    }


    /**
     * 发送系统消息
     *
     * @param title   标题
     * @param content 内容
     * @param userIds 用户
     */
    private void sendSysMsg(String title, JSONObject content, Integer industry, List<Integer> userIds) {
        SysMsgSendVO sendVO = SysMsgSendVO.builder()
                .msgType(MsgTypeEnum.MONITOR.getId())
                .msgIndustry(industry)
                .msgTitle(title)
                .msgContent(content)
                .userIds(userIds)
                .build();
        log.info("系统消息通知内容：{}", JSONObject.toJSONString(sendVO));
        fgSystemService.sendMsg(sendVO);
    }


    @Data
    public static class Bud {

        private boolean need_stop;

        private String level;

        private Integer code;

        private String msg;
    }


    /**
     * 时间
     *
     * @param day 日期
     * @return 返回数据
     */
    private Long noticeTime(Long day) {
        return DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(day)));
    }

    /**
     * 获取告警原因
     *
     * @param putMonitor 监控
     * @return 返回数据
     */
    private String getNoticeReason(PutMonitor putMonitor) {
        return JSONObject.parseArray(putMonitor.getConditions(), ConditionDTO.class)
                .stream().map(condition ->
                        String.format("%s%s%s%s", condition.getTargetName(), condition.getJudge(),
                                condition.getValue(), MonitorUnitEnum.getNameById2(condition.getUnit()))
                ).collect(Collectors.joining(","));
    }

    /**
     * 生成 name 名称
     *
     * @param dimensionNameMap 名称map
     * @param dimKey           key
     * @param identifyEnums    维度数组
     * @return 返回数据
     */
    private String buildName(Map<Integer, Map<String, MarketDimensionInfoDTO>> dimensionNameMap, String dimKey,
                             List<MonitorIdentifyEnum> identifyEnums) {
        StringBuilder stringBuilder = new StringBuilder();
        String[] dims = dimKey.split(" -- ");
        for (int i = 0; i < identifyEnums.size(); i++) {
            if ("-1".equals(dims[i])) {
                continue;
            }
            stringBuilder.append(identifyEnums.get(i).getName()).append("：【").append(dimensionNameMap.get(i).get(dims[i]).getName()).append("】 ");
        }
        return stringBuilder.toString();
    }


    /**
     * 设置名称数据
     *
     * @param dimensionNameMap 名称
     * @param dimKey           key
     * @param identifyEnums    枚举
     * @return 返回 对象
     */
    private JSONObject buildNameData(Map<Integer, Map<String, MarketDimensionInfoDTO>> dimensionNameMap,
                                     String dimKey, List<MonitorIdentifyEnum> identifyEnums) {
        JSONObject result = new JSONObject();
        String[] dims = dimKey.split(" -- ");
        for (int i = 0; i < identifyEnums.size(); i++) {
            if ("-1".equals(dims[i])) {
                result.put(String.valueOf(identifyEnums.get(i).getId()), ConstantUtils.PLACEHOLDER);
                continue;
            }
            result.put(String.valueOf(identifyEnums.get(i).getId()), dimensionNameMap.get(i).get(dims[i]).getName());
        }
        return result;
    }

    /**
     * 组合 key
     *
     * @param keyInfo info
     * @param key     id
     * @return 返回数据
     */
    @Override
    public String buildKey(String keyInfo, String key) {
        if (StringUtils.isNotBlank(keyInfo)) {
            return String.format("%s -- %s", keyInfo, key);
        }
        return key;
    }

    @Override
    public String[] splitKey(String str) {
        return str.split(" -- ");
    }


    /**
     * 设置 val
     *
     * @param todayDatum     今日数据
     * @param yesterdayDatum 昨日数据
     * @param field          字段
     * @param fieldName      字段名称
     * @param todayName      今日数据
     * @param yesterdayName  昨日名称
     * @return 返回val
     */
    private String buildFieldVal(JSONObject todayDatum, JSONObject yesterdayDatum,
                                 String field, String fieldName,
                                 String todayName, String yesterdayName,
                                 JSONObject tableOne, List<SelectDTO2> header) {
        StringBuilder oneStringBuilder = new StringBuilder();
        BigDecimal today = todayDatum.getBigDecimal(field);
        BigDecimal yest = null == yesterdayDatum ? null : yesterdayDatum.getBigDecimal(field);
        String suffix = "本期".equals(todayName) ? "环比" : "同比";
        if (null != header) {
            header.add(new SelectDTO2(String.format("now_%s", field), String.format("%s%s", todayName, fieldName)));
            header.add(new SelectDTO2(String.format("yest_%s", field), String.format("%s%s", yesterdayName, fieldName)));
            header.add(new SelectDTO2(String.format("rate_%s", field), String.format("%s%s", suffix, fieldName)));
        }
        if (null != today && null != yest && yest.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal rate = today.subtract(yest).multiply(new BigDecimal("100")).divide(yest, 3, RoundingMode.HALF_UP);
            oneStringBuilder.append(todayName)
                    .append(fieldName).append("：").append(today).append("，")
                    .append(yesterdayName)
                    .append(fieldName).append("：").append(yest).append("，")
                    .append(suffix)
                    .append(rate.compareTo(BigDecimal.ZERO) > 0 ? "提升" + rate.toPlainString() : "降低" + rate.negate())
                    .append("%")
                    .append("\n");
            tableOne.put(String.format("rate_%s", field), rate.toPlainString());
        } else {
            oneStringBuilder.append(todayName)
                    .append(fieldName).append("：").append(today).append("，")
                    .append(yesterdayName)
                    .append(fieldName).append("：").append(null == yest ? "0" : yest).append(",")
                    .append(suffix).append("--")
                    .append("\n");
            tableOne.put(String.format("rate_%s", field), "--");
        }
        tableOne.put(String.format("now_%s", field), today);
        tableOne.put(String.format("yest_%s", field), null == yest ? "0" : yest);
        return oneStringBuilder.toString();
    }

    /**
     * 获取发送账户信息
     *
     * @param putMonitor 返回
     * @return 返回数据
     */
    private List<Integer> findUserByMonitor(PutMonitorDTO putMonitor) {
        if (StringUtils.isBlank(putMonitor.getModuleMaster())) {
            return List.of();
        }
        List<Long> masterIds = JSONObject.parseArray(putMonitor.getModuleMaster(), Long.class);
        List<User> users = fgSystemService.getManagersByMaster(MarketMasterGetVO.builder().masterIds(masterIds).build())
                .getData();
        users = users.stream().filter(u -> !sheinConfiguration.isRole(u.getRoleId())).collect(Collectors.toList());
        if (users.isEmpty()) {
            return List.of();
        }
        return users.stream().map(User::getId).distinct().collect(Collectors.toList());
    }


    /**
     * 检查数据是否需要 告警（相同日数据，是否告警恢复等）
     *
     * @param checkDateDTO   判定时间
     * @param putMonitor     监控数据
     * @param dimensionIdMap 指标数据
     */
    private void checkDataNeedNotice(CheckDateDTO checkDateDTO, PutMonitorDTO putMonitor,
                                     Map<String, ErrConditionDTO> dimensionIdMap) {
        //获取已经记录日志的数据（近两日）
        Map<String, PutMonitorNotice> hasNotice = putMonitorNoticeMapper.findMaxInfo(
                new LambdaQueryWrapper<PutMonitorNotice>()
                        .between(PutMonitorNotice::getNoticeTime,
                                DateUtils.date2Long(DateUtils.format(DateUtils.long2Date(checkDateDTO.getEnd1()), -2)),
                                checkDateDTO.getEnd1()
                        ).eq(PutMonitorNotice::getPutMonitorId, putMonitor.getId())
                        .in(PutMonitorNotice::getDimensionId, dimensionIdMap.values().stream().map(ErrConditionDTO::getDimensionId).collect(Collectors.toList()))
                        .groupBy(PutMonitorNotice::getDimensionInfo)
                        .groupBy(PutMonitorNotice::getDimensionId)
        ).stream().collect(Collectors.toMap(u -> this.buildKey(u.getDimensionInfo(), u.getDimensionId()), Function.identity()));
        //获取账户时区
        MasterTimeZoneGetVO masterTimeZoneGetVO = new MasterTimeZoneGetVO();
        masterTimeZoneGetVO.setMasterIds(dimensionIdMap.values().stream().map(ErrConditionDTO::getMasterId).collect(Collectors.toList()));
        masterTimeZoneGetVO.setTimeZone(100);
        List<MasterTimeZoneDTO> timeZones = fgMarketService.getTimezoneInfo(masterTimeZoneGetVO).getData();
        if (timeZones.isEmpty()) {
            log.error("监测ID:{}, 无法获取时区", putMonitor.getId());
            dimensionIdMap = new HashMap<>();
            return;
        }
        //  获取账户时区的今日时间
        Date date = DateUtils.long2Date(checkDateDTO.getTimestamp());
        Map<Long, MasterTodayTimeDTO> todayMap = timeZones.stream().map(timezone -> {
            Date local = TimeZoneEnum.getTimeZoneDate(date, timezone.getTimeZone(), 0);
            Date localZero = DateUtils.setHour(local, 0);
            //如果是0点，往前提取一天数据
            if (localZero.equals(local)) {
                localZero = DateUtils.afterDay(localZero, -1);
            }
            return MasterTodayTimeDTO.builder()
                    .start(DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(localZero, timezone.getTimeZone(), 0)))
                    .end(DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(localZero, timezone.getTimeZone(), 24)))
                    .masterId(timezone.getMasterId())
                    .build();
        }).collect(Collectors.toMap(MasterTodayTimeDTO::getMasterId, Function.identity()));

        List<String> delIds = new ArrayList<>();
        //进行今日数据无变化判定
        for (String key : dimensionIdMap.keySet()) {
            ErrConditionDTO err = dimensionIdMap.get(key);
            PutMonitorNotice putMonitorNotice = hasNotice.get(key);
            if (null == putMonitorNotice) {
                continue;
            }
            MasterTodayTimeDTO today = todayMap.get(err.getMasterId().longValue());
            if (putMonitorNotice.getNoticeTime() > today.getStart() && putMonitorNotice.getNoticeTime() < today.getEnd()) {
                //如果今日已告警，且告警恢复开启，则直接删除
                if (1 == putMonitor.getIsRestore()) {
                    if (putMonitorNotice.getIsRestore().equals(0)) {
                        dimensionIdMap.remove(key);
                        continue;
                    }
                }
                //判定数据是否与上次告警数据相同，如果相同，则不需要告警
                Map<String, String> objCondV = JSONObject.parseArray(putMonitorNotice.getNoticeData(), ConditionRDTO.class)
                        .stream().collect(Collectors.toMap(ConditionRDTO::getTarget, v ->
                                        v.getDiff().toPlainString()
                                , (o, n) -> n));
                AtomicBoolean analysisFlag = new AtomicBoolean(false);
                AtomicBoolean sameFlag = new AtomicBoolean(true);
                err.getConditions().forEach(cond -> {
                    if (!objCondV.containsKey(cond.getTarget())) {
                        return;
                    }
                    if (putMonitor.getIsOperate().equals(1) && putMonitor.getOperateType().equals(MonitorOperateTypeEnum.ANALYSIS.getId())) {
                        analysisFlag.setRelease(true);
                        return;
                    }
                    if (!cond.getDiff().toPlainString().equals(objCondV.get(cond.getTarget()))) {
                        sameFlag.set(false);
                    }
                });
                if (analysisFlag.get()) {
                    log.info("由于判定今日数据已经告警，所以删除此数据告警");
                    delIds.add(key);
                    continue;
                }
                if (sameFlag.get()) {
                    log.info("由于判定今日数据无变化，所以删除此数据告警");
                    log.info("昨日数据 {}, 今日数据{}", putMonitorNotice.getNoticeData(), JSONObject.toJSONString(err.getConditions()));
                    delIds.add(key);
                }
            }
        }
        if (!delIds.isEmpty()) {
            delIds.forEach(dimensionIdMap::remove);
        }
    }


    /**
     * 判定广告单元是否投放，不在投放状态中，则删除
     *
     * @param checkDateDTO   时间
     * @param putMonitor     监控
     * @param dimensionIdMap 数据 map
     */
    private void checkCreativeIsPut(CheckDateDTO checkDateDTO, PutMonitorDTO putMonitor,
                                    Map<String, ErrConditionDTO> dimensionIdMap) {
        //判定是否是创意ID
        if (!MonitorIdentifyEnum.CREATIVE_UNIT.getId().equals(putMonitor.getMonitorDim())) {
            return;
        }
        Map<Long, List<String>> creativeUnitMap = dimensionIdMap.keySet().stream().collect(Collectors.groupingBy(u -> {
            try {
                String[] arr = this.splitKey(u);
                return Long.parseLong(arr[arr.length - 1]);
            } catch (Exception e) {
                return 0L;
            }
        }));
        if (creativeUnitMap.isEmpty()) {
            return;
        }
        CreativeUnitPutStatusVO unitPutStatusVO = new CreativeUnitPutStatusVO();
        unitPutStatusVO.setCreativeUnitIds(new ArrayList<>(creativeUnitMap.keySet()));
        Map<Long, Integer> unitStatusMaps = fgMarketService.creativeUnitPutStatus(unitPutStatusVO).getData().stream()
                .collect(Collectors.toMap(CreativeUnitPutStatusDTO::getCreativeUnitId, CreativeUnitPutStatusDTO::getPutStatus));
        log.info("unit status map {}", JSONObject.toJSONString(unitStatusMaps));
        creativeUnitMap.forEach((unitId, idMap) -> {
            Integer putStatus = unitStatusMaps.getOrDefault(unitId, 0);
            if (0 == putStatus) {
                log.info("创意单元:{} 发现不在投放状态，已排除", unitId);
                idMap.forEach(dimensionIdMap::remove);
            }
        });
    }

}
