package com.overseas.service.monitor.service.impl;

import com.overseas.common.dto.sys.UserLoginDTO;
import com.overseas.common.utils.redis.RedisUtils;
import com.overseas.common.vo.sys.UserLoginVO;
import com.overseas.service.monitor.feign.FgSystemService;
import com.overseas.service.monitor.common.config.SecurityConfig;
import com.overseas.service.monitor.service.PutMonitorUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class PutMonitorUserServiceImpl implements PutMonitorUserService {

    private final FgSystemService fgSystemService;

    private final SecurityConfig securityConfig;

    private final RedisUtils redisUtils;

    /**
     * 获取用户数据
     *
     * @return 返回数据
     */
    @Override
    public String getUserToken() {
        String key = "monitor_user_token";
        String token = redisUtils.get(key);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        UserLoginVO userLoginVO = new UserLoginVO();
        userLoginVO.setUserName(securityConfig.getUserName());
        userLoginVO.setPassword(securityConfig.getPassword());
        UserLoginDTO userLoginDTO = fgSystemService.authLogin(userLoginVO).getData();
        //存储半个小时
        redisUtils.set(key, userLoginDTO.getAuthToken().toString(), 1800);
        return userLoginDTO.getAuthToken().toString();
    }
}
