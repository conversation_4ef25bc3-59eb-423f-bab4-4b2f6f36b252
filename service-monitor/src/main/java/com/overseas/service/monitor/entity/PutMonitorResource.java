package com.overseas.service.monitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.entity.Base;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 **/
@TableName("tm_put_monitor_resource")
@Getter
@Setter
@ToString
public class PutMonitorResource extends Base {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long putMonitorId;

    private Long masterId;

    private Long dimensionId;

    @TableLogic(value = "0", delval = "1")
    private Integer isDel;

}
