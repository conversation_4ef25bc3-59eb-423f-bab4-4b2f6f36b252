package com.overseas.service.monitor.dto.monitor.task;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@ApiModel
public class PutMonitorTaskLogCreativeListDTO {

    @ApiModelProperty("记录ID")
    private Long id;

    @ApiModelProperty("监测ID")
    private Long putMonitorId;

    @ApiModelProperty("告警时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date noticeTime;

    @JSONField(serialize = false, deserialize = false)
    private String noticeData;

    @ApiModelProperty("告警当时指标")
    private List<String> noticeContent;

    @ApiModelProperty("计划ID")
    private String planId;

    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty("素材类型")
    private Integer assetType;

    @ApiModelProperty("素材类型名称")
    private String assetTypeName;

    @ApiModelProperty("素材ID")
    private Long assetId;

    @ApiModelProperty("素材http地址")
    private String httpUrl;

    @ApiModelProperty("素材内容")
    private String content;

    @ApiModelProperty("是否已上传")
    private Integer isUpload;

    @ApiModelProperty("素材封面ID")
    private Long coverImgId;

    @ApiModelProperty("素材封面地址")
    private String coverImgUrl;

    @ApiModelProperty("广告单元ID")
    private Long creativeUnitId;

}
