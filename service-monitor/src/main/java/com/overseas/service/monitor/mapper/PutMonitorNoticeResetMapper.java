package com.overseas.service.monitor.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.monitor.dto.monitor.task.PutMonitorTaskResetLogListDTO;
import com.overseas.service.monitor.entity.PutMonitorNoticeReset;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface PutMonitorNoticeResetMapper extends BaseMapper<PutMonitorNoticeReset> {

    /**
     * 插入列表
     *
     * @param resetList 返回数据
     */
    @Insert("<script> " +
            "INSERT INTO `tm_put_monitor_notice_reset` " +
            " ( `put_notice_id`, `set_type`, `set_id`)" +
            " VALUES  " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            " (#{item.putNoticeId}, #{item.setType}, #{item.setId}) " +
            "</foreach> " +
            "</script>")
    void insertList(@Param("list") List<PutMonitorNoticeReset> resetList);

    /**
     * @param iPage   分页数据
     * @param wrapper 筛选条件
     * @return 返回数据
     */
    @Select("SELECT tpmnr.*, mp.plan_name, mp.campaign_id, mc.campaign_name," +
            "mc.master_id, uu.company_name as master_name," +
            "tpmn.notice_data AS `condition`, tpm.operate_type  " +
            "FROM tm_put_monitor_notice_reset tpmnr " +
            "LEFT JOIN tm_put_monitor_notice tpmn ON tpmnr.put_notice_id = tpmn.id " +
            "LEFT JOIN tm_put_monitor tpm ON tpmn.put_monitor_id = tpm.id " +
            "LEFT JOIN m_plan mp ON tpmnr.set_id = mp.id " +
            "LEFT JOIN m_campaign mc ON mp.campaign_id = mc.id " +
            "LEFT JOIN u_user uu ON mp.master_id = uu.id " +
            "${ew.customSqlSegment} ")
    IPage<PutMonitorTaskResetLogListDTO> listPage(IPage<?> iPage, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 获取监控数据
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT tpmnr.* from tm_put_monitor_notice_reset tpmnr " +
            "left join tm_put_monitor_notice tpmn ON tpmnr.put_notice_id = tpmn.id " +
            "${ew.customSqlSegment}")
    List<PutMonitorNoticeReset> selectWithNotice(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

}
