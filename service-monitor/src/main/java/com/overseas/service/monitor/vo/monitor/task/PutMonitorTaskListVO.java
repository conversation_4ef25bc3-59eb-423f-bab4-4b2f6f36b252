package com.overseas.service.monitor.vo.monitor.task;

import com.overseas.common.validation.annotation.MpEnum;
import com.overseas.common.vo.ListVO;
import com.overseas.service.monitor.enums.MonitorPutStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
public class PutMonitorTaskListVO extends ListVO {

    @ApiModelProperty("投放状态")
    @MpEnum(clazz = MonitorPutStatusEnum.class, message = "投放状态不合法")
    private Integer putStatus;

    @ApiModelProperty("账户ID")
    private Integer masterId;

    @ApiModelProperty("监控类型")
    private Integer monitorType;

}
