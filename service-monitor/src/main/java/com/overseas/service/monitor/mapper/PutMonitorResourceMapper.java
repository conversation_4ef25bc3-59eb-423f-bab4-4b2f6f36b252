package com.overseas.service.monitor.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.monitor.entity.PutMonitorResource;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface PutMonitorResourceMapper extends BaseMapper<PutMonitorResource> {

    /***
     * 获取任务下啦
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT tpm.id, tpm.put_monitor_name AS `name` " +
            "FROM tm_put_monitor_resource tpmr " +
            "LEFT JOIN tm_put_monitor tpm ON tpmr.put_monitor_id = tpm.id " +
            "${ew.customSqlSegment} ")
    List<SelectDTO> selectTask(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    /**
     * 筛选计划
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT mp.id , mp.plan_name as `name` " +
            "from tm_put_monitor_resource tpmr " +
            "left join  m_plan mp ON tpmr.dimension_id = mp.id " +
            "${ew.customSqlSegment}")
    List<SelectDTO> selectPlan(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


}

