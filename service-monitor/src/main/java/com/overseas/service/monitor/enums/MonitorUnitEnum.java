package com.overseas.service.monitor.enums;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum MonitorUnitEnum implements ICommonEnum {

    //
    VALUE(1, ""),
    RATE(2, "%");

    private final Integer id;

    private final String name;


    private final static Map<Integer, String> NAME_MAP = new HashMap<>();

    static {
        for (MonitorUnitEnum monitorUnitEnum : values()) {
            NAME_MAP.put(monitorUnitEnum.getId(), monitorUnitEnum.getName());
        }
    }

    public static String getNameById2(Integer id) {
        return NAME_MAP.getOrDefault(id, "");
    }
}
