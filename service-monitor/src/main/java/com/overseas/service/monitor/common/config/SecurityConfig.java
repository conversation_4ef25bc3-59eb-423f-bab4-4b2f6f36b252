package com.overseas.service.monitor.common.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 **/
@Component
@Slf4j
@Data
@RefreshScope
public class SecurityConfig {

    @Value("${security.overseas.userName}")
    private String userName;

    @Value("${security.overseas.password}")
    private String password;
}
