package com.overseas.service.monitor.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.overseas.common.entity.Base;
import com.overseas.service.monitor.enums.MonitorTimeCycleUnitEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 **/
@TableName("tm_put_monitor")
@Getter
@Setter
@ToString
public class PutMonitor extends Base {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String moduleIdentify;

    private String moduleMaster;

    private String putMonitorName;

    private Integer monitorType;

    private Integer dimension;

    private String monitorDimensions;

    @TableField(exist = false)
    private Integer timeCycleMinute;

    private Integer timeCycle;

    private Integer timeCycleUnit;

    private Integer dataCycleType;

    private Integer dataCycle;

    private Integer dataCycleUnit;

    private Integer postbackCycle;

    private Integer postbackCycleUnit;

    private Integer compareCycle;

    private String conditions;

    private String specialConditions;

    private String notice;

    private String noticeConfig;

    private Integer isRestore;

    private String chargeTime;

    private Integer isTop;

    private String topConfig;

    /**
     * 汇总
     */
    private Integer isTotal;

    /**
     * 排除相同数据 ；1:开始；0 ：不开启
     */
    private Integer excludeSameData;

    private Integer isOperate;

    private Integer operateType;

    private String operateVal;

    private Integer isOperateReset;

    private String resetVal;

    @TableLogic(value = "0", delval = "1")
    private Integer isDel;

    private Integer putStatus;

    public Integer getTimeCycleMinute() {
        if (MonitorTimeCycleUnitEnum.HOUR.getId().equals(this.getTimeCycleUnit())) {
            return timeCycle * 60;
        }
        return timeCycle;
    }

}
