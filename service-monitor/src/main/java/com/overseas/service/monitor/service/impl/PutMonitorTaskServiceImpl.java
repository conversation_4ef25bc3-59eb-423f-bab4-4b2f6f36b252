package com.overseas.service.monitor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.market.market.MarketDimensionInfoDTO;
import com.overseas.common.dto.monitor.put.ConditionDTO;
import com.overseas.common.dto.monitor.put.ConditionRDTO;
import com.overseas.common.dto.monitor.put.NoticeConfigDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.IsDelEnum;
import com.overseas.common.enums.market.asset.AssetTypeEnum;
import com.overseas.common.enums.monitor.put.MonitorDimensionEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.asset.AssetAttributeGetVO;
import com.overseas.common.vo.market.market.MarketDimensionInfoVO;
import com.overseas.service.monitor.dto.monitor.asset.AssetGetDTO;
import com.overseas.service.monitor.dto.monitor.task.*;
import com.overseas.service.monitor.entity.PutMonitor;
import com.overseas.service.monitor.entity.PutMonitorNotice;
import com.overseas.service.monitor.entity.PutMonitorNoticeConfig;
import com.overseas.service.monitor.entity.PutMonitorResource;
import com.overseas.service.monitor.enums.*;
import com.overseas.service.monitor.feign.FgMarketService;
import com.overseas.service.monitor.mapper.*;
import com.overseas.service.monitor.service.PutMonitorNoticeService;
import com.overseas.service.monitor.service.PutMonitorOperateService;
import com.overseas.service.monitor.service.PutMonitorTaskService;
import com.overseas.service.monitor.vo.monitor.task.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class PutMonitorTaskServiceImpl implements PutMonitorTaskService {

    private final FgMarketService fgMarketService;

    private final PutMonitorNoticeService putMonitorNoticeService;

    private final PutMonitorMapper putMonitorMapper;

    private final PutMonitorResourceMapper putMonitorResourceMapper;

    private final PutMonitorNoticeMapper putMonitorNoticeMapper;

    private final PutMonitorNoticeConfigMapper putMonitorNoticeConfigMapper;

    private final PutMonitorNoticeResetMapper putMonitorNoticeResetMapper;

    private final PutMonitorOperateService putMonitorOperateService;

    @Override
    public List<SelectDTO> creativeTaskSelect(PutMonitorTaskCreativeSelectVO selectVO) {
        return putMonitorResourceMapper.selectTask(new QueryWrapper<>()
                .eq("tpmr.master_id ", selectVO.getMasterId())
                .eq("tpmr.dimension_id", 0)
                .and(q -> q.and(q1 -> q1.eq("tpm.put_status", MonitorPutStatusEnum.ENABLE.getId())
                                .eq("tpmr.is_del", IsDelEnum.NORMAL.getId())
                                .eq("tpm.is_del", IsDelEnum.NORMAL.getId())
                        ).or(CollectionUtils.isNotEmpty(selectVO.getAppointIds()), q2 -> q2.in("tpm.id", selectVO.getAppointIds()))
                )
        );
    }

    @Override
    public PageUtils<?> list(PutMonitorTaskListVO listVO, List<Integer> permissionMasterIds, Integer loginUserId) {
        PageUtils<?> emptyPage = new PageUtils<>(listVO.getPage(), listVO.getPageNum());
        if (permissionMasterIds.isEmpty()) {
            return emptyPage;
        }
        if (ObjectUtils.isNotNullOrZero(listVO.getMasterId())) {
            if (permissionMasterIds.contains(listVO.getMasterId())) {
                permissionMasterIds = List.of(listVO.getMasterId());
            } else {
                return emptyPage;
            }
        }
        List<Long> includeIds = putMonitorResourceMapper.selectList(new LambdaQueryWrapper<PutMonitorResource>()
                .in(PutMonitorResource::getMasterId, permissionMasterIds)
                .eq(PutMonitorResource::getIsDel, IsDelEnum.NORMAL.getId())
        ).stream().map(PutMonitorResource::getPutMonitorId).distinct().collect(Collectors.toList());
        if (includeIds.isEmpty()) {
            return emptyPage;
        }
        IPage<PutMonitorTaskListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = putMonitorMapper.list(iPage, new LambdaQueryWrapper<PutMonitor>()
                .and(q -> q.in(PutMonitor::getId, includeIds))
                .eq(PutMonitor::getIsDel, IsDelEnum.NORMAL.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMonitorType()), PutMonitor::getMonitorType, listVO.getMonitorType())
                .ne(ObjectUtils.isNullOrZero(listVO.getMonitorType()), PutMonitor::getMonitorType, MonitorTypeEnum.CREATIVE_MONITOR.getId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPutStatus()), PutMonitor::getPutStatus, listVO.getPutStatus())
                .like(StringUtils.isNotBlank(listVO.getSearch()), PutMonitor::getPutMonitorName, listVO.getSearch())
                .orderByDesc(PutMonitor::getId)
        );
        if (iPage.getRecords().isEmpty()) {
            return new PageUtils<>(iPage);
        }
        Map<Long, Date> noticeMap = putMonitorNoticeMapper.selectList(new QueryWrapper<PutMonitorNotice>()
                .select("put_monitor_id , max(create_time) as create_time")
                .lambda()
                .in(PutMonitorNotice::getPutMonitorId, iPage.getRecords().stream()
                        .map(PutMonitorTaskListDTO::getId).collect(Collectors.toList()))
                .groupBy(PutMonitorNotice::getPutMonitorId)
        ).stream().collect(Collectors.toMap(PutMonitorNotice::getPutMonitorId, PutMonitorNotice::getCreateTime));

        iPage.getRecords().forEach(u -> {
            u.setConditionStr(JSONObject.parseArray(u.getConditions(), ConditionDTO.class).stream()
                    .map(cond ->
                            String.format("%s%s%s%s", cond.getTargetName(), cond.getJudge(), cond.getValue(),
                                    MonitorUnitEnum.getNameById2(cond.getUnit()))
                    ).collect(Collectors.toList())
            );
            u.setNoticeTime(noticeMap.getOrDefault(u.getId(), null));
            u.setMonitorDimension(JSONObject.parseArray(u.getMonitorDimensions(), Integer.class));
            u.setMonitorTypeName(ICommonEnum.getNameById(u.getMonitorType(), MonitorTypeEnum.class));
        });
        return new PageUtils<>(iPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(PutMonitorTaskSaveVO saveVO, List<Integer> permissionMasterIds, Integer loginUserId) {
        PutMonitor putMonitor = setPutMonitorInfo(saveVO, null, permissionMasterIds, loginUserId);
        putMonitor.setCreateUid(loginUserId);
        putMonitorMapper.insert(putMonitor);
        insertResource(putMonitor, saveVO.getDimensionIds(), loginUserId);
        if (saveVO.getMonitorType().equals(MonitorTypeEnum.CREATIVE_MONITOR.getId())) {
            saveVO.getMasterIds().forEach(masterId -> {
                PutMonitorResource putMonitorResource = new PutMonitorResource();
                putMonitorResource.setMasterId(masterId);
                putMonitorResource.setDimensionId(0L);
                putMonitorResource.setPutMonitorId(putMonitor.getId());
                putMonitorResource.setCreateUid(loginUserId);
                putMonitorResourceMapper.insert(putMonitorResource);
            });
        }
    }

    @Override
    public PutMonitorTaskGetDTO get(PutMonitorTaskGetVO getVO, Integer loginUserId) {
        PutMonitor putMonitor = findOneById(getVO.getId(), null, 0);
        PutMonitorTaskGetDTO getDTO = new PutMonitorTaskGetDTO();
        BeanUtils.copyProperties(putMonitor, getDTO);
        getDTO.setMasterIds(JSONObject.parseArray(putMonitor.getModuleMaster(), Long.class));
        //获取指定维度
        getDTO.setDimensionIds(putMonitorResourceMapper.selectList(new LambdaQueryWrapper<PutMonitorResource>()
                .eq(PutMonitorResource::getPutMonitorId, putMonitor.getId())
                .ne(PutMonitorResource::getDimensionId, 0)
                .eq(PutMonitorResource::getIsDel, IsDelEnum.NORMAL.getId())
        ).stream().map(PutMonitorResource::getDimensionId).collect(Collectors.toList()));
        getDTO.setMonitorDimension(JSONObject.parseArray(putMonitor.getMonitorDimensions(), Integer.class));
        getDTO.setConditions(JSONObject.parseArray(putMonitor.getConditions(), ConditionDTO.class));
        getDTO.setSpecialConditions(JSONObject.parseArray(putMonitor.getSpecialConditions(), ConditionDTO.class));
        getDTO.setNoticeConfig(JSONObject.parseArray(putMonitor.getNoticeConfig(), NoticeConfigDTO.class));
        getDTO.setChargeTime(JSONObject.parseArray(putMonitor.getChargeTime(), PutMonitorChargeTimeVO.class));
        switch (putMonitor.getMonitorType()) {
            case 1:
                break;
            case 2:
                getDTO.setNoticeIndicators(JSONObject.parseArray(putMonitor.getNotice(), String.class));
                getDTO.setTopConfig(JSONObject.parseObject(putMonitor.getTopConfig()));
            default:
        }
        return getDTO;
    }

    @Override
    public void update(PutMonitorTaskUpdateVO updateVO, List<Integer> permissionMasterIds, Integer loginUserId) {
        PutMonitor putMonitor = setPutMonitorInfo(updateVO, updateVO.getId(), permissionMasterIds, loginUserId);
        putMonitor.setUpdateUid(loginUserId);
        putMonitorMapper.updateById(putMonitor);
        //删除资源
        putMonitorResourceMapper.delete(new LambdaQueryWrapper<PutMonitorResource>()
                .eq(PutMonitorResource::getPutMonitorId, putMonitor.getId())
                .ne(PutMonitorResource::getDimensionId, 0)
                .eq(PutMonitorResource::getIsDel, IsDelEnum.NORMAL.getId())
        );
        insertResource(putMonitor, updateVO.getDimensionIds(), loginUserId);
    }

    @Override
    public void putStatus(PutMonitorTaskPutStatusVO putStatusVO, List<Integer> permissionMasterIds, Integer loginUserId) {
        PutMonitor putMonitor = findOneById(putStatusVO.getId(), permissionMasterIds, loginUserId);
        PutMonitor update = new PutMonitor();
        update.setPutStatus(putStatusVO.getPutStatus());
        update.setUpdateUid(loginUserId);
        putMonitorMapper.update(update, new LambdaQueryWrapper<PutMonitor>().eq(PutMonitor::getId, putMonitor.getId()));
    }

    @Override
    public void testNotice(PutMonitorNoticeConfigVO configVO) {
        putMonitorNoticeService.sendNotice(configVO, "测试通知");
    }

    @Override
    public PageUtils<?> logList(PutMonitorTaskLogListVO listVO, Integer loginUserId) {
        IPage<PutMonitorNotice> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = putMonitorNoticeMapper.selectPage(iPage, new LambdaQueryWrapper<PutMonitorNotice>()
                .eq(PutMonitorNotice::getPutMonitorId, listVO.getPutMonitorId())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q -> q.like(PutMonitorNotice::getDimensionId, listVO.getSearch()))
                .orderByDesc(PutMonitorNotice::getId)
                .between(StringUtils.isNotBlank(listVO.getStartDate()) && StringUtils.isNotBlank(listVO.getEndDate()),
                        PutMonitorNotice::getNoticeTime, DateUtils.string2Long(listVO.getStartDate(), "00:00:00"),
                        DateUtils.string2Long(listVO.getEndDate(), "23:59:59"))
        );
        if (iPage.getRecords().isEmpty()) {
            return new PageUtils<>(iPage);
        }
        List<PutMonitorTaskLogListDTO> list = new ArrayList<>();
        iPage.getRecords().forEach(u -> {
            PutMonitorTaskLogListDTO put = new PutMonitorTaskLogListDTO();
            put.setId(u.getId());
            put.setPutMonitorId(u.getPutMonitorId());
            put.setDimensionId(u.getDimensionId());
            put.setDimensionName(u.getDimensionName());
            put.setIsRestore(u.getIsRestore());
            put.setNoticeTime(u.getCreateTime());
            if (MonitorIsResetEnum.RESET.getId().equals(u.getIsRestore())) {
                put.setRestoreTime(u.getUpdateTime());
            } else {
                put.setRestoreTime(null);
            }
            put.setNoticeContent(this.transformConditionR(JSONObject.parseArray(u.getNoticeData(), ConditionRDTO.class)));
            list.add(put);
        });
        return new PageUtils<>(list, iPage.getTotal());
    }

    @Override
    public PageUtils<?> logListCreative(PutMonitorTaskLogCreativeListVO listVO, Integer loginUserId) {
        IPage<PutMonitorTaskLogCreativeListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = this.putMonitorNoticeMapper.listLogCreative(iPage, new QueryWrapper<>()
                .eq("tpmn.put_monitor_id", listVO.getPutMonitorId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanId()), "mcu.plan_id", listVO.getPlanId())
                .between(StringUtils.isNotBlank(listVO.getStartDate()) && StringUtils.isNotBlank(listVO.getEndDate()),
                        "tpmn.create_time", listVO.getStartDate() + " 00:00:00", listVO.getEndDate() + " 23:59:59")
                .eq(StringUtils.isNotBlank(listVO.getSearch()), "tpmn.dimension_id", listVO.getSearch())
        );
        iPage.getRecords().forEach(log -> {
            AssetAttributeGetVO getVO = new AssetAttributeGetVO();
            getVO.setId(log.getAssetId());
            AssetGetDTO asset = fgMarketService.assetGet(getVO).getData();
            log.setHttpUrl(asset.getHttpUrl());
            log.setCoverImgUrl(asset.getCoverImgUrl());
            log.setAssetTypeName(ICommonEnum.getNameById(log.getAssetType(), AssetTypeEnum.class));
            log.setNoticeContent(this.transformConditionR(JSONObject.parseArray(log.getNoticeData(), ConditionRDTO.class)));
        });
        return new PageUtils<>(iPage);
    }

    @Override
    public List<SelectDTO> select(PutMonitorTaskGetVO getVO) {
        PutMonitor putMonitor = findOneById(getVO.getId(), null, 0);
        MonitorDimensionEnum dimensionEnum = ICommonEnum.get(putMonitor.getDimension(), MonitorDimensionEnum.class);
        switch (dimensionEnum) {
            case PLAN:
                return this.putMonitorResourceMapper.selectPlan(new QueryWrapper<>()
                        .eq("tpmr.put_monitor_id", getVO.getId())
                        .ne("tpmr.dimension_id", 0)
                        .eq("tpmr.is_del", IsDelEnum.NORMAL.getId())
                        .eq("mp.is_del", IsDelEnum.NORMAL.getId())
                );
            case MASTER:
            case CAMPAIGN:
            default:
                return List.of();
        }
    }

    @Override
    public PageUtils<?> resetLogList(PutMonitorTaskResetLogListVO listVO, Integer loginUserId) {
        IPage<PutMonitorTaskResetLogListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        iPage = putMonitorNoticeResetMapper.listPage(iPage, new QueryWrapper<>()
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPutMonitorId()), "tpmn.put_monitor_id", listVO.getPutMonitorId())
                .and(StringUtils.isNotBlank(listVO.getSearch()), q ->
                        q.like("tpmnr.put_notice_id", listVO.getSearch()).or()
                                .like("tpmnr.set_id", listVO.getSearch())
                )
                .eq(ObjectUtils.isNotAll(listVO.getIsReset()), "tpmnr.is_reset", listVO.getIsReset())
                .between(StringUtils.isNotBlank(listVO.getStartDate()) && StringUtils.isNotBlank(listVO.getEndDate()),
                        "tpmnr.create_time", listVO.getStartDate() + " 00:00:00", listVO.getEndDate() + " 23:59:59")
                .orderByDesc("tpmnr.id")
        );
        iPage.getRecords().forEach(u -> {
            u.setIsResetName(ICommonEnum.getNameById(u.getIsReset(), MonitorIsResetEnum.class));
            //触发内容
            u.setConditionContent(this.transformConditionR(JSONObject.parseArray(u.getCondition(), ConditionRDTO.class)));
            //后续行为内容
            if (StringUtils.isNotBlank(u.getSetVal())) {
                u.setSetValContent(this.putMonitorOperateService.findServiceById(u.getOperateType()).transformSetVal(u.getSetVal()));
            } else {
                u.setSetValContent(ConstantUtils.PLACEHOLDER);
            }
        });
        return new PageUtils<>(iPage);
    }

    @Override
    public List<SelectDTO> noticeConfigSelect(Integer loginUserId) {
        return putMonitorNoticeConfigMapper.selectList(new LambdaQueryWrapper<PutMonitorNoticeConfig>()
                        .eq(PutMonitorNoticeConfig::getIsDel, IsDelEnum.NORMAL.getId()))
                .stream().map(u -> new SelectDTO(u.getId(), u.getConfigName())).collect(Collectors.toList());
    }

    /**
     * 根据id 查询数据
     *
     * @param id                  id
     * @param permissionMasterIds 可操作账户ID
     * @param loginUserId         登陆用户
     * @return 返回数据
     */
    private PutMonitor findOneById(Long id, List<Integer> permissionMasterIds, Integer loginUserId) {
        PutMonitor putMonitor = putMonitorMapper.selectById(id);
        if (null == putMonitor) {
            throw new CustomException("监控不存在");
        }

        if (ObjectUtils.isNotNullOrZero(loginUserId)) {
            //全部账户，非本人无法操作
            if (MonitorIdentifyEnum.ALL.getId().equals(putMonitor.getDimension())) {
                if (!loginUserId.equals(putMonitor.getCreateUid())) {
                    throw new CustomException("监控无权限操作");
                } else {
                    return putMonitor;
                }
            }
            //如果含有全部可操作账户，则可操作
            List<Integer> masterIds = JSONObject.parseArray(putMonitor.getModuleMaster(), Integer.class);
            if (!new HashSet<>(permissionMasterIds).containsAll(masterIds)) {
                throw new CustomException("监控无权限操作");
            }
        }
        return putMonitor;
    }

    /**
     * 保存资源信息
     *
     * @param putMonitor   监控信息
     * @param dimensionIds 维度ID
     * @param loginUserId  登陆用户
     */
    private void insertResource(PutMonitor putMonitor, List<Long> dimensionIds, Integer loginUserId) {
        if (dimensionIds.isEmpty()) {
            return;
        }
        Map<Long, Long> infoMap = fgMarketService.marketDimensionInfo(MarketDimensionInfoVO.builder()
                        .dimension(putMonitor.getDimension()).ids(dimensionIds).build())
                .getData().stream().collect(Collectors.toMap(MarketDimensionInfoDTO::getId,
                        MarketDimensionInfoDTO::getMasterId, (o, n) -> n));
        //保存资源
        dimensionIds.forEach(id -> {
            PutMonitorResource putMonitorResource = new PutMonitorResource();
            putMonitorResource.setMasterId(infoMap.getOrDefault(id, 0L));
            putMonitorResource.setPutMonitorId(putMonitor.getId());
            putMonitorResource.setDimensionId(id);
            putMonitorResource.setCreateUid(loginUserId);
            putMonitorResourceMapper.insert(putMonitorResource);
        });
    }

    /**
     * 设置通知配置
     *
     * @param saveVO              保存数据
     * @param id                  id
     * @param permissionMasterIds 权限投放账户
     * @param loginUserId         用户
     * @return 返回数据
     */
    private PutMonitor setPutMonitorInfo(PutMonitorTaskSaveVO saveVO, Long id,
                                         List<Integer> permissionMasterIds, Integer loginUserId) {
        // 判断监测名称是否重复
        this.checkMonitorName(saveVO.getPutMonitorName(), id);
        PutMonitor putMonitor;
        if (null == id) {
            putMonitor = new PutMonitor();
        } else {
            putMonitor = findOneById(id, permissionMasterIds, loginUserId);
        }
        BeanUtils.copyProperties(saveVO, putMonitor);
        putMonitor.setMonitorDimensions(JSONObject.toJSONString(saveVO.getMonitorDimension()));
        putMonitor.setConditions(JSONObject.toJSONString(saveVO.getConditions()));
        putMonitor.setSpecialConditions(JSONObject.toJSONString(saveVO.getSpecialConditions()));
        putMonitor.setChargeTime(JSONObject.toJSONString(saveVO.getChargeTime()));
        putMonitor.setModuleMaster(JSONObject.toJSONString(saveVO.getMasterIds()));
        switch (putMonitor.getMonitorType()) {
            case 1:
                break;
            case 2:
                putMonitor.setNotice(JSONObject.toJSONString(saveVO.getNoticeIndicators()));
                putMonitor.setTopConfig(JSONObject.toJSONString(saveVO.getTopConfig()));
            default:
        }
        saveVO.getNoticeConfig().forEach(noticeConfigDTO -> {
            if (ObjectUtils.isNotNullOrZero(noticeConfigDTO.getId())) {
                if (StringUtils.isBlank(noticeConfigDTO.getHookUrl())) {
                    PutMonitorNoticeConfig config = putMonitorNoticeConfigMapper.selectById(noticeConfigDTO.getId());
                    if (null == config) {
                        throw new CustomException("通知钉钉信息获取失败");
                    }
                    NoticeConfigDTO noticeConfig = JSONObject.parseObject(config.getConfigVal(), NoticeConfigDTO.class);
                    noticeConfigDTO.setHookToken(noticeConfig.getHookToken());
                    noticeConfigDTO.setHookUrl(noticeConfig.getHookUrl());
                    noticeConfigDTO.setHookType(noticeConfig.getHookType());
                }
            }
        });
        putMonitor.setNoticeConfig(JSONObject.toJSONString(saveVO.getNoticeConfig()));
        if (ObjectUtils.isNullOrZero(putMonitor.getDataCycleType()) ||
                List.of(MonitorDataCycleTypeEnum.LEI_JI.getId(), MonitorDataCycleTypeEnum.ONE_DAY.getId()).contains(putMonitor.getDataCycleType())) {
            putMonitor.setExcludeSameData(1);
        } else {
            putMonitor.setExcludeSameData(0);
        }
        return putMonitor;
    }


    /**
     * 检查用户名称重复
     *
     * @param monitorName 监测名称
     * @param id          已有监测ID
     */
    private void checkMonitorName(String monitorName, Long id) {
        long count = this.putMonitorMapper.selectCount(new LambdaQueryWrapper<PutMonitor>()
                .eq(PutMonitor::getPutMonitorName, monitorName)
                .ne(ObjectUtils.isNotNullOrZero(id), PutMonitor::getId, id)
                .eq(PutMonitor::getIsDel, IsDelEnum.NORMAL.getId())
        );
        if (count > 0) {
            throw new CustomException("监测名称重复，请修改后再试");
        }

    }

    /**
     * 转换字符串
     *
     * @param conditions 错误内容
     * @return 返回数据
     */
    private List<String> transformConditionR(List<ConditionRDTO> conditions) {
        return conditions.stream().map(r ->
                String.format("【%s】%s %s%s,当前: %s", r.getTargetName(), r.getJudge(), r.getValue(),
                        MonitorUnitEnum.getNameById2(r.getUnit()), r.getDiff())
        ).collect(Collectors.toList());
    }

}
