package com.overseas.service.monitor.enums;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum MonitorIdentifyEnum implements ICommonEnum {

    //
    ALL(1, "全部账户", "master"),
    MASTER(2, "投放账户", "master"),
    CAMPAIGN(3, "活动", "camp"),
    PLAN(4, "计划", "plan"),
    PACKAGE(5, "流量包", "package"),
    COUNTRY(6, "国家", "country"),
    EP(7, "EP", "ep"),
    ADX(8, "ADX", "adx"),
    SLOT(12, "广告形式", "slot"),
    CREATIVE_UNIT(13, "创意单元", "creative");

    private final Integer id;

    private final String name;

    private final String val;


    public static MonitorIdentifyEnum needCheck(List<Integer> ids) {
        for (MonitorIdentifyEnum monitorIdentifyEnum : List.of(PLAN, CAMPAIGN)) {
            if (ids.contains(monitorIdentifyEnum.getId())) {
                return monitorIdentifyEnum;
            }
        }
        return null;
    }
}
