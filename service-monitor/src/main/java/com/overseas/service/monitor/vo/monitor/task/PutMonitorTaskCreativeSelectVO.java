package com.overseas.service.monitor.vo.monitor.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel
public class PutMonitorTaskCreativeSelectVO {

    @ApiModelProperty("账户ID")
    @NotNull(message = "监测账户不能为空")
    private Integer masterId;

    @ApiModelProperty("指定监控ID")
    private List<Long> appointIds;

}
