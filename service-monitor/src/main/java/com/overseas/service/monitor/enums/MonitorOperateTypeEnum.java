package com.overseas.service.monitor.enums;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum MonitorOperateTypeEnum implements ICommonEnum {
    //
    EXPEL(1, "定向排除", "putMonitorOperateExpelServiceImpl"),
    PAUSE(2, "创意暂停", "putMonitorOperatePauseServiceImpl"),
    CLOSE(3, "创意归档", "putMonitorOperateCloseServiceImpl"),
    ADJUST(4, "预算调整", "putMonitorOperateAdjustServiceImpl"),
    ANALYSIS(5, "数据分析", "putMonitorOperateAnalysisServiceImpl"),
    BUDGET_ANALYSIS(6, "预算达标", "putMonitorOperateBudgetThresholdServiceImpl");

    private final Integer id;

    private final String name;

    private final String clazz;
}
