package com.overseas.service.monitor.dto.monitor.put;

import com.alibaba.fastjson.JSONObject;
import com.overseas.service.monitor.entity.PutMonitor;
import com.overseas.service.monitor.service.impl.PutMonitorServiceImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class PutMonitorDTO extends PutMonitor {

    private List<Integer> monitorDimensionList;

    private List<Integer> dimInfos;

    private Integer monitorDim;

    private final String module = PutMonitorServiceImpl.CUSTOM_MODULE;

    @Override
    public void setMonitorDimensions(String monitorDimensions) {
        super.setMonitorDimensions(monitorDimensions);
        this.monitorDimensionList = JSONObject.parseArray(monitorDimensions, Integer.class);
        if (this.monitorDimensionList.size() > 1) {
            dimInfos = this.monitorDimensionList.subList(0, this.monitorDimensionList.size() - 1);
        } else {
            dimInfos = new ArrayList<>();
        }
        monitorDim = this.monitorDimensionList.get(this.monitorDimensionList.size() - 1);
    }
}
