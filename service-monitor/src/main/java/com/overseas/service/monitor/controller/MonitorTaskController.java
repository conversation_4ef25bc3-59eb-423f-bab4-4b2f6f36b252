package com.overseas.service.monitor.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.vo.monitor.put.NoticeDevelopVO;
import com.overseas.service.monitor.dto.monitor.task.PutMonitorTaskLogListDTO;
import com.overseas.service.monitor.dto.monitor.task.PutMonitorTaskResetLogListDTO;
import com.overseas.service.monitor.enums.MonitorIdentifyEnum;
import com.overseas.service.monitor.service.PutMonitorNoticeService;
import com.overseas.service.monitor.vo.monitor.task.*;
import com.overseas.service.monitor.service.PutMonitorTaskService;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/monitor/tasks")
@Slf4j
@RequiredArgsConstructor
public class MonitorTaskController extends AbstractController {

    private final PutMonitorTaskService putMonitorTaskService;

    private final PutMonitorNoticeService putMonitorNoticeService;

    @ApiOperation(value = "监控下拉")
    @PostMapping("/creative/task/select")
    public R select(@RequestBody @Validated PutMonitorTaskCreativeSelectVO creativeSelectVO) {
        return R.data(putMonitorTaskService.creativeTaskSelect(creativeSelectVO));
    }

    @ApiOperation(value = "指定维度下拉数据")
    @PostMapping("/dimension/select")
    public R dimensionSelect() {
        return R.data(ICommonEnum.list(MonitorIdentifyEnum.class));
    }

    @ApiOperation(value = "监控配置")
    @PostMapping("/notice/config/select")
    public R noticeConfigSelect() {
        return R.data(putMonitorTaskService.noticeConfigSelect(this.getUserId()));
    }

    @ApiOperation(value = "投放监控列表")
    @PostMapping("/list")
    public R list(@RequestBody @Validated PutMonitorTaskListVO listVO) {
        return R.page(putMonitorTaskService.list(listVO, this.listMasterId(), this.getUserId()));
    }

    @ApiOperation(value = "投放监控保存")
    @PostMapping("/save")
    public R save(@RequestBody @Validated PutMonitorTaskSaveVO saveVO) {
        putMonitorTaskService.save(saveVO, this.listMasterId(), this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "投放监控查询")
    @PostMapping("/get")
    public R putStatus(@RequestBody @Validated PutMonitorTaskGetVO getVO) {
        return R.data(putMonitorTaskService.get(getVO, this.getUserId()));
    }

    @ApiOperation(value = "投放监控更新")
    @PostMapping("/update")
    public R update(@RequestBody @Validated PutMonitorTaskUpdateVO updateVO) {
        putMonitorTaskService.update(updateVO, this.listMasterId(), this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "投放监控开关")
    @PostMapping("/put/status")
    public R putStatus(@RequestBody @Validated PutMonitorTaskPutStatusVO putStatusVO) {
        putMonitorTaskService.putStatus(putStatusVO, this.listMasterId(), this.getUserId());
        return R.ok();
    }

    @ApiOperation(value = "投放监控测试发送信息")
    @PostMapping("/test/notice")
    public R testNotice(@RequestBody @Validated PutMonitorNoticeConfigVO configVO) {
        putMonitorTaskService.testNotice(configVO);
        return R.ok();
    }

    @ApiOperation(value = "投放监控日志列表", response = PutMonitorTaskLogListDTO.class)
    @PostMapping("/log/list")
    public R logList(@RequestBody @Validated PutMonitorTaskLogListVO listVO) {
        return R.page(putMonitorTaskService.logList(listVO, this.getUserId()));
    }

    @ApiOperation(value = "投放监控日志列表(创意)", response = PutMonitorTaskLogListDTO.class)
    @PostMapping("/log/list/creative")
    public R logListCreative(@RequestBody @Validated PutMonitorTaskLogCreativeListVO listVO) {
        return R.page(putMonitorTaskService.logListCreative(listVO, this.getUserId()));
    }

    @ApiOperation(value = "投放监控资源", response = SelectDTO.class)
    @PostMapping("/resource/select")
    public R creativePlanSelect(@RequestBody @Validated PutMonitorTaskGetVO getVO) {
        return R.data(this.putMonitorTaskService.select(getVO));
    }

    @ApiOperation(value = "投放监控日志后续操作记录列表", response = PutMonitorTaskResetLogListDTO.class)
    @PostMapping("/notice/reset/log/list")
    public R resetLogList(@RequestBody @Validated PutMonitorTaskResetLogListVO listVO) {
        return R.page(putMonitorTaskService.resetLogList(listVO, this.getUserId()));
    }

    @ApiModelProperty("业务通知告警")
    @PostMapping("/notice/develop")
    public R noticeDevelop(@RequestBody @Validated NoticeDevelopVO developVO) {
        putMonitorNoticeService.sendNotice(developVO.getMessage());
        return R.ok();
    }

}
