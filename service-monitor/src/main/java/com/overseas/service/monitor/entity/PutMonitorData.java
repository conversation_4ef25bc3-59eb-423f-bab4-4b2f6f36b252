package com.overseas.service.monitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.overseas.common.dto.report.DimensionTotalDTO;
import lombok.*;

/**
 * <AUTHOR>
 **/
@TableName("tm_put_monitor_data")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PutMonitorData extends DimensionTotalDTO {

    @TableId(type = IdType.AUTO)
    private Long id;
    private Long putMonitorId;
    private String dimension;
    private String dimensionInfo;
    private String dimensionId;
    private Integer dataTime;
}
