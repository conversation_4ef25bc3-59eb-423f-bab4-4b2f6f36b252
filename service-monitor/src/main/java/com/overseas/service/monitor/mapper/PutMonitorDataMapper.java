package com.overseas.service.monitor.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.monitor.entity.PutMonitorData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface PutMonitorDataMapper extends BaseMapper<PutMonitorData> {

    /**
     * 数据入库
     *
     * @param putMonitorId 监测ID
     * @param dimension    监测维度
     * @param dataTime     监测时间
     * @param list         list 数据
     */
    @Insert("<script>" +
            "INSERT INTO `tm_put_monitor_data` " +
            " ( `put_monitor_id`, `dimension`, `dimension_info`, `dimension_id`, `data_time`, `idx_bid`, `idx_inner_bid`," +
            " `idx_win`, `idx_view`, `idx_user_view`, `idx_click`, `idx_user_click`, `idx_media_cost`, `idx_platform_cost`," +
            " `idx_report_cost`,`idx_consumer_cost`, `idx_reach`, `idx_action`, `idx_num_action`, `idx_action1`, `idx_action2`," +
            " `idx_action3`, `idx_action4`, `idx_action5`, `idx_action6`, `idx_action7`, `idx_action8`, `idx_action9`, " +
            "`idx_action10`, `idx_action11`, `idx_action12`, `idx_action13`, `idx_action14`, `idx_action15`, `idx_action16`, " +
            "`idx_action17`, `idx_action18`, `idx_action19`, `idx_action20`, `idx_action21`, `idx_action22`, `idx_action23`," +
            "`idx_action24`, `idx_action25`, `idx_action26`, `idx_action27`, `idx_action28`, `idx_action29`, `idx_action30`, " +
            "`idx_action31`, `idx_action32`, `idx_action33`, `idx_action34`, `idx_action35`, " +
            "`idx_action36`, `idx_action37`, `idx_action38`, `idx_action39`, `idx_action40`, " +
            "`idx_settlement`, `idx_click_uniq_session`)" +
            " VALUES  " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            " (#{item.putMonitorId}, #{dimension}, #{item.dimensionInfo}, #{item.dimensionId}, #{dataTime}, #{item.bid}, #{item.innerBid}," +
            " #{item.win}, #{item.view}, #{item.userView}, #{item.click}, #{item.userClick}, #{item.mediaCost}, #{item.platformCost}," +
            " #{item.reportCost}, #{item.consumerCost}, #{item.reach}, #{item.action}, #{item.numAction}, #{item.action1}, #{item.action2}," +
            " #{item.action3}, #{item.action4}, #{item.action5}, #{item.action6}, #{item.action7}, #{item.action8}, #{item.action9}," +
            " #{item.action10}, #{item.action11}, #{item.action12}, #{item.action13}, #{item.action14}, #{item.action15}, #{item.action16}," +
            " #{item.action17}, #{item.action18}, #{item.action19}, #{item.action20}, #{item.action21}, #{item.action22}, #{item.action23}," +
            " #{item.action24}, #{item.action25}, #{item.action26}, #{item.action27}, #{item.action28}, #{item.action29}, #{item.action30}," +
            " #{item.action31}, #{item.action32}, #{item.action33}, #{item.action34}, #{item.action35}," +
            " #{item.action36}, #{item.action37}, #{item.action38}, #{item.action39}, #{item.action40}," +
            " #{item.settlement}, #{item.clickUniqSession}) " +
            "</foreach>" +
            "ON DUPLICATE KEY UPDATE " +
            " idx_bid = idx_bid + VALUES(idx_bid), " +
            " idx_inner_bid = idx_inner_bid + VALUES(idx_inner_bid), " +
            " idx_win = idx_win + VALUES(idx_win), " +
            " idx_view = idx_view + VALUES(idx_view), " +
            " idx_user_view = idx_user_view + VALUES(idx_user_view), " +
            " idx_click = idx_click + VALUES(idx_click), " +
            " idx_user_click = idx_user_click + VALUES(idx_user_click), " +
            " idx_media_cost = idx_media_cost + VALUES(idx_media_cost), " +
            " idx_platform_cost = idx_platform_cost + VALUES(idx_platform_cost), " +
            " idx_report_cost = idx_report_cost + VALUES(idx_report_cost), " +
            " idx_consumer_cost = idx_consumer_cost + VALUES(idx_consumer_cost), " +
            " idx_reach = idx_reach + VALUES(idx_reach), " +
            " idx_action = idx_action + VALUES(idx_action), " +
            " idx_num_action = idx_num_action + VALUES(idx_num_action), " +
            " idx_action1 = idx_action1 + VALUES(idx_action1), " +
            " idx_action2 = idx_action2 + VALUES(idx_action2), " +
            " idx_action3 = idx_action3 + VALUES(idx_action3), " +
            " idx_action4 = idx_action4 + VALUES(idx_action4), " +
            " idx_action5 = idx_action5 + VALUES(idx_action5), " +
            " idx_action6 = idx_action6 + VALUES(idx_action6), " +
            " idx_action7 = idx_action7 + VALUES(idx_action7), " +
            " idx_action8 = idx_action8 + VALUES(idx_action8), " +
            " idx_action9 = idx_action9 + VALUES(idx_action9), " +
            " idx_action10 = idx_action10 + VALUES(idx_action10), " +
            " idx_action11 = idx_action11 + VALUES(idx_action11), " +
            " idx_action12 = idx_action12 + VALUES(idx_action12), " +
            " idx_action13 = idx_action13 + VALUES(idx_action13), " +
            " idx_action14 = idx_action14 + VALUES(idx_action14), " +
            " idx_action15 = idx_action15 + VALUES(idx_action15), " +
            " idx_action16 = idx_action16 + VALUES(idx_action16), " +
            " idx_action17 = idx_action17 + VALUES(idx_action17), " +
            " idx_action18 = idx_action18 + VALUES(idx_action18), " +
            " idx_action19 = idx_action19 + VALUES(idx_action19), " +
            " idx_action20 = idx_action20 + VALUES(idx_action20), " +
            " idx_action21 = idx_action21 + VALUES(idx_action21), " +
            " idx_action22 = idx_action22 + VALUES(idx_action22), " +
            " idx_action23 = idx_action23 + VALUES(idx_action23), " +
            " idx_action24 = idx_action24 + VALUES(idx_action24), " +
            " idx_action25 = idx_action25 + VALUES(idx_action25), " +
            " idx_action26 = idx_action26 + VALUES(idx_action26), " +
            " idx_action27 = idx_action27 + VALUES(idx_action27), " +
            " idx_action28 = idx_action28 + VALUES(idx_action28), " +
            " idx_action29 = idx_action29 + VALUES(idx_action29), " +
            " idx_action30 = idx_action30 + VALUES(idx_action30), " +
            " idx_action31 = idx_action31 + VALUES(idx_action31), " +
            " idx_action32 = idx_action32 + VALUES(idx_action32), " +
            " idx_action33 = idx_action33 + VALUES(idx_action33), " +
            " idx_action34 = idx_action34 + VALUES(idx_action34), " +
            " idx_action35 = idx_action35 + VALUES(idx_action35), " +
            " idx_action36 = idx_action36 + VALUES(idx_action36), " +
            " idx_action37 = idx_action37 + VALUES(idx_action37), " +
            " idx_action38 = idx_action38 + VALUES(idx_action38), " +
            " idx_action39 = idx_action39 + VALUES(idx_action39), " +
            " idx_action40 = idx_action40 + VALUES(idx_action40), " +
            " idx_settlement = idx_settlement + VALUES(idx_settlement)," +
            " idx_click_uniq_session = idx_click_uniq_session + VALUES(idx_click_uniq_session) " +
            "</script>")
    void batchInsert(@Param("putMonitorId") Long putMonitorId, @Param("dimension") String dimension,
                     @Param("dataTime") Long dataTime, @Param("list") List<PutMonitorData> list);


    /**
     * 获取数据
     *
     * @param wrapper 筛选条件
     * @return 返回数据
     */
    @Select(" SELECT ${ew.sqlSelect} FROM  tm_put_monitor_data ${ew.customSqlSegment}")
    List<PutMonitorData> selectData(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 获取数据
     *
     * @param wrapper 筛选条件
     * @return 返回数据
     */
    @Select(" SELECT  * from ( SELECT ${ew.sqlSelect} FROM  tm_put_monitor_data ${ew.customSqlSegment} ) t ${ew2.customSqlSegment} ")
    List<PutMonitorData> selectDataByWhere(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper, @Param("ew2") Wrapper<?> wrapper2);


    /**
     * 获取数据差集
     *
     * @param wrapper  筛选条件
     * @param diffSql2 筛选条件
     * @return 返回数据
     */
    @Select(" SELECT ${ew.sqlSelect} FROM (${diffSql2}) d ${ew.customSqlSegment}")
    List<PutMonitorData> selectDiff(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper, @Param("diffSql2") String diffSql2);

    /**
     * 获取数据差集
     *
     * @param wrapper    筛选条件
     * @param diffSql1   1条sql
     * @param diffSql2   2条sql
     * @param diffSelect 条件结果
     * @return 返回数据
     */
    @Select(" SELECT ${ew.sqlSelect} FROM  " +
            " (" +
            "SELECT * FROM " +
            " (SELECT ${diffSelect} from (${diffSql1}) cur1 LEFT JOIN (${diffSql2}) cur2" +
            "   ON cur1.dimension_info = cur2.dimension_info AND cur1.dimension_id = cur2.dimension_id   ) d1" +
            "  UNION ALL" +
            " SELECT ${diffSelect} from (${diffSql1}) cur1 RIGHT JOIN (${diffSql2}) cur2" +
            "     ON cur1.dimension_info = cur2.dimension_info AND cur1.dimension_id = cur2.dimension_id ) d " +
            " ${ew.customSqlSegment}")
    List<PutMonitorData> selectDiffWithZero(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper, @Param("diffSql1") String diffSql1,
                                            @Param("diffSql2") String diffSql2, @Param("diffSelect") String diffSelect);

    /**
     * 查询需要注意的维度
     *
     * @param wrapper 查询数据
     * @return 返回数据
     */
    @Select("SELECT DISTINCT dimension_info  FROM tm_put_monitor_data  ${ew.customSqlSegment} ")
    List<String> selectDistinctDimensionInfo(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

}
