package com.overseas.service.monitor.enums;

import com.overseas.common.enums.ICommonEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum MonitorTypeEnum implements ICommonEnum {

    //
    MONITOR(1, "监控告警"),
    DATA_NOTICE(2, "数据通知"),
    CREATIVE_MONITOR(3, "创意监控");


    private final Integer id;

    private final String name;
}
