
package com.overseas.service.monitor.feign;

import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.sys.UserLoginDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexFieldDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexParentColumnDTO;
import com.overseas.common.dto.sys.project.ProjectByMasterDTO;
import com.overseas.common.dto.sys.project.ProjectResourceDTO;
import com.overseas.common.dto.sys.user.UserSelectByIdDTO;
import com.overseas.common.entity.User;
import com.overseas.common.utils.FeignR;
import com.overseas.common.vo.market.market.MarketMasterGetVO;
import com.overseas.common.vo.sys.UserLoginVO;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.common.vo.sys.msg.SysMsgSendVO;
import com.overseas.common.vo.sys.project.ProjectByMasterVO;
import com.overseas.common.vo.sys.project.ProjectResourceVO;
import com.overseas.common.vo.sys.user.UserSelectByIdVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 **/
@FeignClient("ai-overseas-service-system")
public interface FgSystemService {

    @PostMapping("/sys/auth/getUserId")
    FeignR<Integer> getUserId();

    @PostMapping("/sys/auth/getUser")
    FeignR<User> getUser();

    @PostMapping("/sys/auth/listMasterId")
    FeignR<List<Integer>> listMasterId();

    @PostMapping("/sys/projects/resource")
    FeignR<ProjectResourceDTO> getProjectResource(ProjectResourceVO projectResourceVO);

    @PostMapping("/sys/projects/by/master")
    FeignR<ProjectByMasterDTO> getProjectByMaster(ProjectByMasterVO projectByMasterVO);

    @PostMapping("/sys/auth/login")
    FeignR<UserLoginDTO> authLogin(UserLoginVO userLoginVO);

    @PostMapping("/sys/customIndex/get/report/select")
    FeignR<String> listCustomIndexReportSelect(CustomIndexGetVO getVO);

    @PostMapping("/sys/customIndex/fieldMap/get")
    FeignR<List<CustomIndexFieldDTO>> getCustomIndexFieldMap(CustomIndexGetVO getVO);

    @PostMapping("/sys/customIndex/get/report/download")
    FeignR<List<CustomIndexParentColumnDTO>> getCustomIndexReportDownload(CustomIndexGetVO getVO);

    @PostMapping("/sys/resource/area/countries/select")
    FeignR<List<SelectDTO3>> areaCountrySelect(AreaCountrySelectVO selectVO);

    @PostMapping("/sys/users/select/by/id")
    FeignR<List<UserSelectByIdDTO>> selectUserById(UserSelectByIdVO selectByIdVO);

    @PostMapping("/sys/msg/send")
    FeignR<?> sendMsg(SysMsgSendVO sendVO);

    @PostMapping("/sys/users/managers/get")
    FeignR<List<User>> getManagersByMaster(MarketMasterGetVO getVO);

}
