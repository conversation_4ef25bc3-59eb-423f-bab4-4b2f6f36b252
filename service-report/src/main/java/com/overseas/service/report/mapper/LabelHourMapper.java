package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.AssetLabelListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.LabelHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface LabelHourMapper extends BaseMapper<LabelHour> {
    /**
     * 数据列表
     *
     * @param wrapper 筛选条件
     * @return 返回数据
     */
    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_label_hour ${ew.customSqlSegment} ")
    List<AssetLabelListDTO> list(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 数据列表
     *
     * @param iPage   分页
     * @param wrapper 筛选条件
     * @return 返回数据
     */
    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_label_hour ${ew.customSqlSegment} ")
    IPage<AssetLabelListDTO> listAssetLabel(IPage<?> iPage, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 数据 total
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_label_hour ${ew.customSqlSegment} ")
    AssetLabelListDTO totalList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

}
