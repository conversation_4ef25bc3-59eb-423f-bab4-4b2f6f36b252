package com.overseas.service.report.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.market.master.MasterTimeZoneDTO;
import com.overseas.common.dto.market.reportField.ReportFieldDTO;
import com.overseas.common.dto.market.reportNote.ReportNoteDTO;
import com.overseas.common.dto.market.rtaStrategy.RtaStrategyListDTO;
import com.overseas.common.dto.report.revenue.RevenueDspResultDTO;
import com.overseas.common.dto.report.revenue.RevenueForUpdateDTO;
import com.overseas.common.dto.report.revenue.RevenueReportDTO;
import com.overseas.common.dto.report.revenue.RevenueReportListDTO;
import com.overseas.common.dto.report.revenue.ae.AeRevenueAllReportExcelDTO;
import com.overseas.common.dto.report.revenue.ae.AeRevenueReportExcelDTO;
import com.overseas.common.dto.report.revenue.lazada.LazadaRevenueReportExcelDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexContentDTO;
import com.overseas.common.dto.sys.project.ProjectGetDTO;
import com.overseas.common.enums.*;
import com.overseas.common.enums.market.rta.RtaStrategyStatusEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.master.MasterTimeZoneGetVO;
import com.overseas.common.vo.market.plan.direct.RtaSelectGetVO;
import com.overseas.common.vo.market.reportField.ReportFieldGetVO;
import com.overseas.common.vo.market.reportNote.ReportNoteListVO;
import com.overseas.common.vo.report.revenue.*;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.common.vo.sys.project.ProjectForMasterVO;
import com.overseas.common.vo.sys.project.ProjectGetVO;
import com.overseas.service.report.entity.PlanRtaHour;
import com.overseas.service.report.entity.RevenueDay;
import com.overseas.service.report.enums.RevenueDimensionFieldEnum;
import com.overseas.service.report.feign.FgMarketService;
import com.overseas.service.report.feign.FgSystemService;
import com.overseas.service.report.mapper.PlanRtaHourMapper;
import com.overseas.service.report.mapper.RevenueDayMapper;
import com.overseas.service.report.service.RevenueDayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class RevenueDayServiceImpl extends ServiceImpl<RevenueDayMapper, RevenueDay> implements RevenueDayService {

    private final FgMarketService fgMarketService;

    private final FgSystemService fgSystemService;

    private final PlanRtaHourMapper planRtaHourMapper;

    @Override
    public PageUtils<RevenueReportListDTO> listRevenueReport(RevenueReportListVO listVO) {

        if (ObjectUtils.isNullOrZero(listVO.getMasterId())) {
            listVO.setMasterIds(this.fgSystemService.getMasterIds().getData());
            if (listVO.getMasterIds().isEmpty()) {
                return new PageUtils<>(List.of(), 0L);
            }
            listVO.getMasterIds().add(0);
        }

        // 获取需要查询的自定义指标
        String sqlSelect = this.getSqlSelect(this.listRtaCustomIndex(listVO.getProjectId()).stream()
                        .map(contentDTO -> contentDTO.getRule().replaceAll("`idx_", "dsp.`idx_"))
                        .collect(Collectors.joining(",")),
                this.listReportField(listVO.getProjectId(), listVO.getCustoms()).stream()
                        .map(ReportFieldDTO::getRule).collect(Collectors.joining(",")));

        return this.listRevenueReports(listVO, sqlSelect);
    }

    private String getSqlSelect(String dspSelect, String customFields) {

        return dspSelect + (StringUtils.isNotBlank(customFields) ? ("," + customFields) : "");
    }

    private PageUtils<RevenueReportListDTO> listRevenueReports(RevenueReportListVO listVO, String sqlSelect) {
        // 1.获取查询Wrapper
        QueryWrapper<RevenueDay> queryWrapper = this.getWrapper(listVO, sqlSelect);
        //获取dsp数据指标筛选
        QueryWrapper<RevenueDay> dspWrapper = this.getSummaryWrapper(listVO, revenueDspSelect())
                .groupBy("dim_day")
                .groupBy("dim_master_id")
                .groupBy("dim_rta_id")
                .groupBy("dim_country_id");

        // 2.查询分页数据
        IPage<RevenueReportListDTO> pageData = this.listRevenueReport(listVO, queryWrapper, dspWrapper);

        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }
        // 3.填充数据
        this.fillDataList(pageData, listVO);

        // 4.获取、填充汇总数据
        return new PageUtils<>(pageData,
                this.getRevenueReportTotal(this.getSummaryWrapper(listVO, sqlSelect), dspWrapper));
    }

    private List<CustomIndexContentDTO> listRtaCustomIndex(Long projectId) {
        CustomIndexGetVO getVO = new CustomIndexGetVO();
        getVO.setModule("report_revenue_list");
        getVO.setProjectId(projectId);
        return this.fgSystemService.getCustomIndexByModule(getVO).getData();
    }

    private List<ReportFieldDTO> listReportField(Long projectId, List<String> fields) {

        ReportFieldGetVO getVO = new ReportFieldGetVO();
        getVO.setProjectId(projectId);
        getVO.setFields(fields);
        return this.fgMarketService.listReportField(getVO).getData();
    }

    /**
     * 获取项目分页数据
     *
     * @param listVO       传入参数
     * @param queryWrapper 返回数据
     * @return 返回数据
     */
    private IPage<RevenueReportListDTO> listRevenueReport(RevenueReportListVO listVO,
                                                          QueryWrapper<RevenueDay> queryWrapper,
                                                          QueryWrapper<?> dspWrapper) {
        return this.baseMapper.listRevenueReport(new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper,
                dspWrapper);
    }

    /**
     * 获取项目汇总数据
     *
     * @param queryWrapper 返回数据
     * @param dspWrapper   筛选请求
     * @return 返回数据
     */
    private RevenueReportListDTO getRevenueReportTotal(QueryWrapper<RevenueDay> queryWrapper,
                                                       QueryWrapper<?> dspWrapper) {
        RevenueReportListDTO revenueReportListDTO = this.baseMapper.getRevenueReportTotal(queryWrapper, dspWrapper);
        revenueReportListDTO.setDay(0L);
        revenueReportListDTO.setMasterId(0L);
        revenueReportListDTO.setRtaId(0L);
        return revenueReportListDTO;
    }

    /**
     * 获取查询Wrapper
     *
     * @param listVO    传入参数
     * @param sqlSelect 需要查询的自定义参数
     * @return 返回数据
     */
    private QueryWrapper<RevenueDay> getWrapper(RevenueReportListVO listVO, String sqlSelect) {

        QueryWrapper<RevenueDay> queryWrapper = this.getSummaryWrapper(listVO, sqlSelect)
                .groupBy(RevenueDimensionFieldEnum.getFieldsByKeys(listVO.getDimensions(), "dsp."));

        // 设置排序
        if (List.of("", "normal").contains(listVO.getSortField())) {
            // 默认排序
            listVO.getDimensions().forEach(dimension ->
                    queryWrapper.orderByDesc("dsp." +
                            Objects.requireNonNull(RevenueDimensionFieldEnum.get(dimension)).getField()));
        } else {
            listVO.setSortField(List.of("reportDate", "masterName", "countryName", "rtaName")
                    .contains(listVO.getSortField())
                    ? RevenueDimensionFieldEnum.get(listVO.getSortField()).getOrderSql()
                    : HumpLineUtils.humpToLine2(listVO.getSortField()));
            queryWrapper.orderBy(true,
                    SortTypeEnum.ASC.getSortType().equals(listVO.getSortType()), listVO.getSortField());
        }
        return queryWrapper;
    }

    /**
     * 获取查询的总Wrapper
     *
     * @param listVO    传入参数
     * @param sqlSelect 需要查询的自定义参数
     * @return 返回数据
     */
    private QueryWrapper<RevenueDay> getSummaryWrapper(RevenueReportListVO listVO, String sqlSelect) {

        return new QueryWrapper<RevenueDay>()
                .select(RevenueDimensionFieldEnum.getSelectByKeys(listVO.getDimensions()) + "," + sqlSelect)
                .between("dsp.dim_day",
                        DateUtils.string2Long(listVO.getStartDate()), DateUtils.string2Long(listVO.getEndDate()))
                .eq(ObjectUtils.isNotNullOrZero(listVO.getProjectId()),
                        "dsp.dim_project_id", listVO.getProjectId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()),
                        "dsp.dim_master_id", listVO.getMasterId())
                .in(ObjectUtils.isNullOrZero(listVO.getMasterId()),
                        "dsp.dim_master_id", listVO.getMasterIds())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getRtaId()),
                        "dsp.dim_rta_id", listVO.getRtaId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCountryId()),
                        "dsp.dim_country_id", listVO.getCountryId())
                .gt(!ProjectEnum.AE.getId().equals(listVO.getProjectId().intValue()), "dsp.idx_report_cost", 0);
    }

    /**
     * 填充数据
     *
     * @param pageData 当页数据
     * @param listVO   传入参数
     */
    private void fillDataList(IPage<RevenueReportListDTO> pageData, RevenueReportListVO listVO) {

        // 获取账号映射Map
        Map<Long, String> masterMap = listVO.getDimensions().contains("masterName")
                ? this.getMasterMap() : new HashMap<>();
        // 获取国家映射Map
        Map<Long, String> countryMap = listVO.getDimensions().contains("countryName")
                ? this.getCountryMap(pageData.getRecords().stream().map(RevenueReportListDTO::getCountryId)
                .filter(ObjectUtils::isNotNullOrZero).distinct().collect(Collectors.toList())) : new HashMap<>();
        // 获取RTA映射Map
//        Map<Long, String> rtaMap = listVO.getDimensions().contains("rtaName")
//                ? this.getRtaMap(pageData.getRecords().stream().map(RevenueReportListDTO::getRtaId).distinct().collect(Collectors.toList())) : new HashMap<>();
        // 获取备注映射Map
        ReportNoteListVO noteListVO = new ReportNoteListVO();
        noteListVO.setProjectId(listVO.getProjectId());
        noteListVO.setReportDates(pageData.getRecords().stream().map(entity -> DateUtils.long2String(entity.getDay()))
                .collect(Collectors.toList()));
        Map<String, String> notesMap = listVO.getDimensions().contains("reportDate")
                && listVO.getDimensions().size() == 1
                ? this.fgMarketService.listReportNote(noteListVO).getData()
                .stream().collect(Collectors.toMap(ReportNoteDTO::getReportDate, ReportNoteDTO::getNote)) : new HashMap<>();
        Map<Long, RtaStrategyListDTO> rtaNameMap = new HashMap<>();
        Map<String, RtaStrategyListDTO> rtaNameMap2 = new HashMap<>();
        if (listVO.getDimensions().contains("rtaName")) {
            List<RtaStrategyListDTO> rtaList = this.getRtaStrategiesAll(ProjectEnum.AE.getId().intValue() == listVO.getProjectId() ? ProjectEnum.AE.getRtaGroupId() : null,
                    null, pageData.getRecords().stream().map(RevenueReportListDTO::getRtaId).distinct().collect(Collectors.toList()));
            rtaNameMap = rtaList.stream().collect(Collectors.toMap(RtaStrategyListDTO::getId, Function.identity(), (o, n) -> n));
            rtaNameMap2 = rtaList.stream().collect(Collectors.toMap(k -> String.format("%s-%s", k.getId(), k.getRtaCountryId().equals(20L) ? 826000000000L : k.getRtaCountryId()), Function.identity(), (o, n) -> n));
        }
        for (RevenueReportListDTO entity : pageData.getRecords()) {
            entity.setReportDate(ObjectUtils.isNotNullOrZero(entity.getDay())
                    ? DateUtils.long2String(entity.getDay()) : ConstantUtils.PLACEHOLDER_2);
            entity.setNote(notesMap.getOrDefault(entity.getReportDate(), ""));
            entity.setMasterName(masterMap.getOrDefault(entity.getMasterId(), ConstantUtils.UNKNOWN));
            entity.setCountryName(countryMap.getOrDefault(entity.getCountryId(), ConstantUtils.UNKNOWN));
            RtaStrategyListDTO rta;
            if (null == entity.getCountryId()) {
                rta = rtaNameMap.get(entity.getRtaId());
            } else {
                rta = rtaNameMap2.get(String.format("%s-%s", entity.getRtaId(), entity.getCountryId()));
            }
            if (null != rta) {
                entity.setRtaName(rta.getRtaStrategyName());
                entity.setCampaignId(rta.getCampaignId());
            } else {
                entity.setRtaName(ConstantUtils.UNKNOWN);
                entity.setCampaignId(ConstantUtils.PLACEHOLDER);
            }
        }
    }

    /**
     * 获取当前登录账号下所有投放账号映射Map
     *
     * @return 返回数据
     */
    private Map<Long, String> getMasterMap() {
        return this.fgMarketService.selectAllMaster().getData().stream()
                .collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
    }

    /**
     * 获取国家映射Map
     *
     * @param countryIds 国家ID
     * @return 返回数据
     */
    private Map<Long, String> getCountryMap(List<Long> countryIds) {
        if (countryIds.isEmpty()) {
            return new HashMap<>();
        }
        AreaCountrySelectVO selectVO = new AreaCountrySelectVO();
        selectVO.setCountryIds(countryIds);
        return this.fgSystemService.selectCountry(selectVO).getData()
                .stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
    }

    /**
     * 获取指定RTA映射Map
     *
     * @param ids RTA集合
     * @return 返回数据
     */
    private Map<Long, String> getRtaMap(List<Long> ids) {
        RtaSelectGetVO getVO = new RtaSelectGetVO();
        getVO.setIds(ids);
        return this.fgMarketService.selectRtaStrategy(getVO).getData().stream()
                .collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
    }

    /**
     * 导出营收报表
     *
     * @param listVO   传入参数
     * @param response response
     * @throws IOException 异常
     */
    @Override
    public void exportRevenueReport(RevenueReportListVO listVO, HttpServletResponse response) throws IOException {

        if (ObjectUtils.isNullOrZero(listVO.getMasterId())) {
            listVO.setMasterIds(this.fgSystemService.getMasterIds().getData());
            if (listVO.getMasterIds().isEmpty()) {
                throw new CustomException("暂无可下载数据");
            }
        }
        // 获取需要查询的自定义指标
        // 获取DSP指标
        List<CustomIndexContentDTO> customIndexContentDTOS = this.listRtaCustomIndex(listVO.getProjectId());
        // 获取客户指标
        List<ReportFieldDTO> reportFieldDTOS = this.listReportField(listVO.getProjectId(), listVO.getCustoms());

        String sqlSelect = this.getSqlSelect(customIndexContentDTOS.stream()
                        .map(contentDTO -> contentDTO.getRule().replaceAll("`idx_", "dsp.`idx_"))
                        .collect(Collectors.joining(",")),
                reportFieldDTOS.stream().map(ReportFieldDTO::getRule).collect(Collectors.joining(",")));
        List<RevenueReportListDTO> reportList = this.listRevenueReports(listVO, sqlSelect).getData();
        if (reportList.isEmpty()) {
            throw new CustomException("暂无可下载数据");
        }

        Map<String, String> fieldMap = new HashMap<>() {{
            putAll(customIndexContentDTOS.stream().collect(
                    Collectors.toMap(CustomIndexContentDTO::getKey, CustomIndexContentDTO::getTitle)));
            putAll(reportFieldDTOS.stream().collect(
                    Collectors.toMap(ReportFieldDTO::getField, ReportFieldDTO::getFieldName)));
        }};
        List<SelectDTO2> headers = new ArrayList<>() {{
            listVO.getDimensions().forEach(dimension -> add(
                    new SelectDTO2(dimension, RevenueDimensionFieldEnum.get(dimension).getTitle())));
            listVO.getCustoms().forEach(field -> add(new SelectDTO2(field, fieldMap.getOrDefault(field, field))));
        }};

        // 获取项目名称
        ProjectGetVO projectGetVO = new ProjectGetVO();
        projectGetVO.setId(listVO.getProjectId());
        ProjectGetDTO projectGetDTO = this.fgSystemService.getProject(projectGetVO).getData();
        String fileName = (projectGetDTO == null ? "" : projectGetDTO.getProjectName()) + "营收报表_" +
                (listVO.getStartDate().equals(listVO.getEndDate())
                        ? listVO.getStartDate()
                        : listVO.getStartDate() + "_" + listVO.getEndDate());
        ExcelUtils.download(response, fileName, "报表数据", reportList, headers);
    }

    /**
     * 保存上传的客户数据
     *
     * @param saveVO 传入参数
     */
    @Override
    public void saveRevenueData(RevenueReportSaveVO saveVO) {
        // 调整后只需要上传Lazada_rta数据
        ProjectEnum projectEnum = ICommonEnum.get(saveVO.getProjectId().intValue(), ProjectEnum.class);
        if (null == projectEnum) {
            return;
        }
        switch (projectEnum) {
            case LAZADA_RTA:
                this.saveLazadaRevenueData(saveVO);
                return;
            case AE:
                if (null == saveVO.getFileType()) {
                    throw new CustomException("请选择文件类型");
                }
                switch (saveVO.getFileType()) {
                    //媒体数据
                    case 2:
                        this.saveAeRevenueMediaData(saveVO);
                        break;
                    //全量数据
                    case 1:
                    default:
                        this.saveAeRevenueDetailData(saveVO);
                }
                return;
            default:
        }

    }

    /**
     * 手动更新营收报表客户侧数据
     *
     * @param updateVO 传入参数
     */
    @Override
    public void updateRevenueData(RevenueUpdateVO updateVO) {
        RevenueReportDTO revenueReportDTO = new RevenueReportDTO();
        revenueReportDTO.setProjectId(updateVO.getProjectId());
        revenueReportDTO.setDay(updateVO.getDay());
        revenueReportDTO.setMasterId(updateVO.getMasterId());
        revenueReportDTO.setCountryId(updateVO.getCountryId());
        revenueReportDTO.setRtaId(updateVO.getRtaId());
        String field = updateVO.getField().replace("customer", "").toLowerCase();
        ObjectUtils.setObjectValue(revenueReportDTO, field, Double.parseDouble(updateVO.getValue()));
        this.baseMapper.batchUpdateRevenueDayData(List.of(revenueReportDTO),
                ",idx_" + field, ",#{item." + field + "}",
                "idx_" + field + " = VALUES(idx_" + field + ")");
    }

    /**
     * 保存Lazada数据
     *
     * @param saveVO 传入参数
     */
    private void saveLazadaRevenueData(RevenueReportSaveVO saveVO) {

        // 1.获取Excel中的数据
        List<LazadaRevenueReportExcelDTO> lazadaReportList = ExcelUtils.read(saveVO.getFilePath(),
                LazadaRevenueReportExcelDTO.class);
        if (lazadaReportList.isEmpty()) {
            return;
        }
        // 2.根据RTA的CampaignId，获取相应的RTA
        Map<String, List<RtaStrategyListDTO>> rtaStrategiesMap = this.getRtaStrategiesMap(lazadaReportList.stream()
                .map(LazadaRevenueReportExcelDTO::getCampaignId).distinct().collect(Collectors.toList()));
        if (rtaStrategiesMap.isEmpty()) {
            throw new CustomException("没有匹配的RTA，请确认后再试");
        }

        // 3.获取Lazada的报表映射字段
        Map<String, ReportFieldDTO> reportFieldMap = this.getReportFieldMap(saveVO.getProjectId());

        // 4.获取需要Insert的数据
        List<RevenueReportDTO> revenueDays = new ArrayList<>() {{
            lazadaReportList.forEach(lazadaReport -> {
                if (rtaStrategiesMap.get(lazadaReport.getCampaignId()) != null) {
                    rtaStrategiesMap.get(lazadaReport.getCampaignId()).forEach(rtaStrategy -> {
                        RevenueReportDTO revenueDay = getRevenueDay(saveVO.getProjectId(), lazadaReport,
                                lazadaReport.getCampaignId(), rtaStrategy, reportFieldMap);
                        revenueDay.setDay(DateUtils.date2Long(DateUtils.string2Date(lazadaReport.getReportDate(),
                                DateUtils.DATE_PATTERN_2)));
                        // 临时屏蔽lazada传音帐号录入rta数据，防止记录重复
                        if (!revenueDay.getMasterId().equals(20024L)) {
                            add(revenueDay);
                        }
                    });
                }
            });
        }};

        // 5.执行Insert
        this.executeSaveRevenueData(revenueDays, reportFieldMap);
    }

    /**
     * 通过RTA活动ID获取RTA映射Map
     *
     * @param campaignIds 活动ID
     * @return 返回数据
     */
    private Map<String, List<RtaStrategyListDTO>> getRtaStrategiesMap(List<String> campaignIds) {

        RtaSelectGetVO rtaSelectGetVO = new RtaSelectGetVO();
        rtaSelectGetVO.setCampaignIds(campaignIds);
        return this.fgMarketService.listRtaStrategyById(rtaSelectGetVO).getData()
                .stream().filter(e -> e.getRtaStatus().equals(RtaStrategyStatusEnum.OPEN.getId())).collect(Collectors.groupingBy(RtaStrategyListDTO::getCampaignId));
    }


    /**
     * 通过RTA活动ID获取RTA映射Map
     *
     * @param rtaGroupId rtaGroupId
     * @return 返回数据
     */
    private List<RtaStrategyListDTO> getRtaStrategiesAll(Long rtaGroupId, List<String> campaignIds, List<Long> rtaIds) {
        RtaSelectGetVO rtaSelectGetVO = new RtaSelectGetVO();
        rtaSelectGetVO.setRtaGroupId(rtaGroupId);
        rtaSelectGetVO.setCampaignIds(campaignIds);
        rtaSelectGetVO.setIds(rtaIds);
        return this.fgMarketService.listRtaStrategyAllById(rtaSelectGetVO).getData();
    }

    /**
     * 通过项目获取对应自定义指标映射Map
     *
     * @param projectId 项目
     * @return 返回数据
     */
    private Map<String, ReportFieldDTO> getReportFieldMap(Long projectId) {
        ReportFieldGetVO reportFieldGetVO = new ReportFieldGetVO();
        reportFieldGetVO.setProjectId(projectId);
        reportFieldGetVO.setActionType(1);
        return this.fgMarketService.listReportField(reportFieldGetVO).getData()
                .stream().collect(Collectors.toMap(ReportFieldDTO::getField, Function.identity()));
    }


    /**
     * 通过项目获取对应自定义指标映射Map
     *
     * @param projectId 项目
     * @return 返回数据
     */
    private List<ReportFieldDTO> getReportField(Long projectId) {
        ReportFieldGetVO reportFieldGetVO = new ReportFieldGetVO();
        reportFieldGetVO.setProjectId(projectId);
        reportFieldGetVO.setActionType(1);
        return this.fgMarketService.listReportField(reportFieldGetVO).getData();
    }


    /**
     * 获取需要Insert的数据的实例
     *
     * @param object         数据源对象
     * @param campaignId     RTA活动ID
     * @param rtaStrategy    RTA
     * @param reportFieldMap 自定义报表Map
     * @return 返回实例
     */
    private RevenueReportDTO getRevenueDay(Long projectId, Object object, String campaignId,
                                           RtaStrategyListDTO rtaStrategy,
                                           Map<String, ReportFieldDTO> reportFieldMap) {
        RevenueReportDTO revenueDay = new RevenueReportDTO();
        if (rtaStrategy == null) {
            throw new CustomException("活动（" + campaignId + "）没有对应RTA配置，请确认后再试");
        }
        // 配置公共字段
        revenueDay.setProjectId(projectId);
        revenueDay.setMasterId(rtaStrategy.getMasterId());
        revenueDay.setRtaId(rtaStrategy.getId());
        revenueDay.setCountryId(Optional.ofNullable(rtaStrategy.getRtaCountryId()).orElse(0L));
        if (revenueDay.getCountryId().equals(20L)) {
            revenueDay.setCountryId(826000000000L);
        }
        // 配置自定义指标字段
        this.fillRevenueCustomField(revenueDay, reportFieldMap, object);
        return revenueDay;
    }

    /**
     * 填充自定义列指标值
     *
     * @param revenueDay     列表数据
     * @param reportFieldMap 自定义列映射Map
     * @param object         数据源对象
     */
    private void fillRevenueCustomField(RevenueReportDTO revenueDay, Map<String, ReportFieldDTO> reportFieldMap,
                                        Object object) {
        reportFieldMap.forEach((key, value) -> {
            Double valueOf = Double.valueOf(Objects.requireNonNull(ObjectUtils.getObjectValue(object, key))
                    .toString().replace("%", ""));
            ObjectUtils.setObjectValue(revenueDay, value.getAction(), valueOf);
        });
    }

    /**
     * 执行insert操作
     *
     * @param revenueDays    列表数据
     * @param reportFieldMap 自定义列映射Map
     */
    private void executeSaveRevenueData(List<RevenueReportDTO> revenueDays,
                                        Map<String, ReportFieldDTO> reportFieldMap) {
        if (revenueDays.isEmpty()) {
            log.info("revenue data info：暂无RTA数据");
            return;
        }
        StringBuilder insertFields = new StringBuilder(), insertParams = new StringBuilder();
        reportFieldMap.forEach((key, value) -> {
            insertFields.append(",idx_").append(value.getAction());
            insertParams.append(",#{item.").append(value.getAction()).append("}").append(value.getInsertRule());
        });
        // 5.执行Insert
        this.baseMapper.batchSaveRevenueDayData(revenueDays, insertFields.toString(), insertParams.toString());
    }

    /**
     * 执行insert操作
     *
     * @param revenueDays    列表数据
     * @param reportFieldMap 自定义列映射Map
     */
    private void executeSaveRevenueData2(List<RevenueReportDTO> revenueDays,
                                         Map<String, ReportFieldDTO> reportFieldMap) {
        if (revenueDays.isEmpty()) {
            log.info("revenue data info：暂无RTA数据");
            return;
        }
        StringBuilder insertFields = new StringBuilder(), insertParams = new StringBuilder(), updateParams = new StringBuilder();
        reportFieldMap.forEach((key, value) -> {
            insertFields.append(",idx_").append(value.getAction());
            insertParams.append(",#{item.").append(value.getAction()).append("}").append(value.getInsertRule());
            updateParams.append("idx_").append(value.getAction()).append("= VALUES( idx_").append(value.getAction()).append("),");
        });
        // 5.执行Insert
        this.baseMapper.batchUpdateRevenueDayData(revenueDays, insertFields.toString(), insertParams.toString(), updateParams.substring(0, updateParams.toString().length() - 1));
    }

    @Override
    public void revenueForUpdate(String day) {
        List<RevenueForUpdateDTO> revenue = getOtherData("/report/revenue/dsp/info/for/update",
                new JSONObject() {{
                    put("day", day);
                }}, new TypeReference<>() {
                });
        Map<String, List<Long>> result = classifyMasterDay(revenue);
        if (result.isEmpty()) {
            log.info("无数据需要更新");
            return;
        }
        result.forEach((key, val) -> {
            String[] str = key.split("__");
            if (str.length != 2) {
                return;
            }
            Integer timezone = Integer.parseInt(str[0]);
            String thisDay = str[1];
            List<RevenueDay> revenueDays = getOtherData("/report/revenue/dsp/get/data/master/day",
                    JSONObject.parseObject(JSONObject.toJSONString(
                            RevenueSaveDataMasterDayVO.builder()
                                    .timezone(timezone)
                                    .masterIds(val)
                                    .day(thisDay).build()
                    )), new TypeReference<>() {
                    });
            saveRevenueDay2Db(revenueDays);
        });
    }

    @Override
    public List<RevenueForUpdateDTO> revenueInfoForUpdate(String day) {
        Date date = DateUtils.string2Date(day);
        return planRtaHourMapper.revenueDayForUpdate(
                DateUtils.format(DateUtils.afterDay(date, -1), DateUtils.DATE_PATTERN + " 00:00:00"),
                DateUtils.format(DateUtils.afterDay(date, -2), DateUtils.DATE_PATTERN + " 00:00:00")
        );
    }

    @Override
    public void saveDspDataByMaster(List<Long> masterIds, Integer timezone, String day) {
        List<RevenueDay> revenueDays = getOtherData("/report/revenue/dsp/get/data/master/day",
                JSONObject.parseObject(JSONObject.toJSONString(
                        RevenueSaveDataMasterDayVO.builder()
                                .timezone(timezone)
                                .masterIds(masterIds).day(day)
                                .build()
                )), new TypeReference<>() {
                });
        saveRevenueDay2Db(revenueDays);
    }

    @Override
    public void saveDspDateIn(String start, String end) {
        List<Integer> masterIds = fgMarketService.selectAllMaster().getData()
                .stream().map(u -> u.getId().intValue()).collect(Collectors.toList());
        if (masterIds.isEmpty()) {
            log.error("账号数据为空");
            throw new CustomException("账户无数据");
        }
        log.info("总回溯账户获取 :{}", JSONObject.toJSONString(masterIds));
        Map<Integer, List<Long>> masterMaps = new HashMap<>();
        List<MasterTimeZoneDTO> result = fgMarketService.getMasterTimeZone(
                MasterTimeZoneGetVO.builder().masterIds(masterIds).timeZone(101).build()
        ).getData();
        result.forEach(u ->
                masterMaps.computeIfAbsent(u.getTimeZone(), v -> new ArrayList<>()).add(u.getMasterId())
        );
        log.info("总回溯账号时区:{}", JSONObject.toJSON(masterMaps));
        List<String> days = DateUtils.getBetweenDate(start, end, DateUtils.DATE_PATTERN);
        log.info("总回溯日期:{}", JSONObject.toJSON(days));
        if (days.isEmpty()) {
            log.error("开始时间{}～结束时间:{}，中间没数据", start, end);
            throw new CustomException("日期无数据");
        }
        days.forEach(day -> {
            log.info("总回溯日期开始:{}", day);
            masterMaps.forEach((timezone, mIds)
                    -> this.saveDspDataByMaster(mIds, timezone, day)
            );
        });
    }

    @Override
    public void saveDspData(String day) {
        List<RevenueDay> revenueDays = getOtherData("/report/revenue/dsp/get/data/day",
                JSONObject.parseObject(JSONObject.toJSONString(
                        RevenueSaveDataDayVO.builder().day(day).build()
                )), new TypeReference<>() {
                });
        saveRevenueDay2Db(revenueDays);
    }

    @Override
    public List<RevenueDay> listDspData(String day) {
        // 1.获取当前小时处于凌晨的时区
        Date reportHour = DateUtils.formatHour(DateUtils.string2Date(day), -1);
        String reportHourStr = DateUtils.format(reportHour, "yyyy-MM-dd HH:00:00");
        Date hourDate = DateUtils.string2Date(reportHourStr);
        String hour = DateUtils.format(hourDate, "HH");
        if (null == hour) {
            log.error("report data :{}", reportHourStr);
            throw new CustomException("时间的小时获取失败");
        }
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneByCurrentHour(
                Integer.parseInt(hour)
        );
        if (timeZoneEnum == null) {
            return List.of();
        }
        // 2.先获取当前账号下所有投放账号时区
        MasterTimeZoneGetVO getVO = new MasterTimeZoneGetVO();
        getVO.setMasterIds(this.fgSystemService.getMasterIds().getData());
        getVO.setTimeZone(timeZoneEnum.getId());
        List<MasterTimeZoneDTO> masters = this.fgMarketService.getMasterTimeZone(getVO).getData();
        // 如果该时区下没有账号，则直接返回
        if (masters.isEmpty()) {
            return List.of();
        }
        return this.listDspData(
                masters.stream().map(MasterTimeZoneDTO::getMasterId).collect(Collectors.toList()),
                timeZoneEnum,
                DateUtils.format(DateUtils.afterDay(
                        TimeZoneEnum.getTimeZoneDate(reportHour, timeZoneEnum.getId(), 0), -1))
        );
    }

    @Override
    public List<RevenueDay> listDspData(List<Long> masterIds, TimeZoneEnum timeZoneEnum, String day) {
        Date today = DateUtils.string2Date(day);
        Date startUtc8Date = TimeZoneEnum.getUTC_8Date(today, timeZoneEnum.getId(), 0);
        Date endUtc8Data = TimeZoneEnum.getUTC_8Date(today, timeZoneEnum.getId(), 23);
        //获取数据
        List<RevenueDay> list = planRtaHourMapper.revenueDayByWrapper(new QueryWrapper<PlanRtaHour>()
                .in("report.dim_master_id", masterIds)
                .in("report.dim_day", List.of(
                        DateUtils.string2Long(DateUtils.format(startUtc8Date)),
                        DateUtils.string2Long(DateUtils.format(endUtc8Data))))
                .between("report.dim_report_hour",
                        DateUtils.date2Long(startUtc8Date), DateUtils.date2Long(endUtc8Data))
                .groupBy("`day`,`dim_master_id`,`dim_rta_id`, d_ep.ep_group")
                .isNotNull("d_ep.ep_group")
                .select(   // 计算当前时间
                        "UNIX_TIMESTAMP(FROM_UNIXTIME(dim_report_hour + "
                                + 3600 * timeZoneEnum.getFormatHour() + ",'%Y-%m-%d')) AS `day`,"
                                + " IFNULL(country.country_id, 0) AS `country_id`, "
                                + " d_ep.ep_group as `adx_id`, "
                                + dspSelect()
                )
        );
        Map<Long, Long> projectMap = fgSystemService.getMasterProject(
                ProjectForMasterVO.builder().masterIds(masterIds).build()).getData();
        //设置项目ID
        return list.stream()
                .peek(u -> {
                    u.setProjectId(projectMap.getOrDefault(u.getMasterId(), 0L));
                    if (u.getCountryId().equals(20L)) {
                        u.setCountryId(826000000000L);
                    }
                }).filter(u -> ObjectUtils.isNotNullOrZero(u.getProjectId())).collect(Collectors.toList());
    }

    /**
     * dsp 指标字段
     *
     * @return 返回数据
     */
    private String dspSelect() {
        return ObjectUtils.getFields(RevenueDay.class)
                .stream()
                .map(objField -> {
                    if (null == objField.getAnnotation(TableField.class)) {
                        return null;
                    }
                    String fieldName = HumpLineUtils.humpToLine2(objField.getName());
                    //日期也不需要返回
                    if (List.of("day", "project_id", "country_id", "node_id", "adx_id").contains(fieldName)) {
                        return null;
                    }
                    String tableField = objField.getAnnotation(TableField.class).value();
                    if (tableField.indexOf("dim_") == 0) {
                        return String.format("`%s` AS `%s`", tableField, fieldName);
                    }
                    if (tableField.indexOf("idx_") == 0) {
                        return String.format("SUM(`%s`) AS `%s`", tableField, fieldName);
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.joining(","));
    }

    /**
     * 解决双机房出现的数据聚合问题
     *
     * @return 返回数据
     */
    private String revenueDspSelect() {
        return ObjectUtils.getFields(RevenueDay.class)
                .stream()
                .map(objField -> {
                    if (null == objField.getAnnotation(TableField.class)) {
                        return null;
                    }
                    String tableField = objField.getAnnotation(TableField.class).value();
                    if (tableField.indexOf("dim_") == 0) {
                        return String.format("`%s` AS `%s`", tableField, tableField);
                    }
                    if (tableField.indexOf("idx_") == 0) {
                        return String.format("SUM(`%s`) AS `%s`", tableField, tableField);
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.joining(","));
    }

    /**
     * 根据获取更新时间进行排序
     *
     * @param revenue 数据
     * @return 排序结果
     */
    private Map<String, List<Long>> classifyMasterDay(List<RevenueForUpdateDTO> revenue) {
        if (CollectionUtils.isEmpty(revenue)) {
            return new HashMap<>();
        }
        Map<String, List<Long>> result = new HashMap<>();
        revenue.forEach(u ->
                result.computeIfAbsent(String.format("%s__%s", u.getTimezone(), u.getDay()), k -> new ArrayList<>())
                        .add(u.getMasterId())
        );
        return result;
    }

    /**
     * 保存数据
     *
     * @param revenueDays 数据
     */
    private void saveRevenueDay2Db(List<RevenueDay> revenueDays) {
        log.debug("data:{}", JSONObject.toJSONString(revenueDays));
        if (revenueDays.isEmpty()) {
            return;
        }
        // 批量插入数据
        this.baseMapper.batchInsertReplaceData(revenueDays);
    }

    /**
     * 获取数据
     *
     * @param method        请求
     * @param params        参数
     * @param typeReference 参数
     * @param <T>           返回数据类型
     * @return 返回数据
     */
    private <T> List<T> getOtherData(String method, JSONObject params,
                                     TypeReference<RevenueDspResultDTO<List<T>>> typeReference) {
        List<T> result = new ArrayList<>();
        for (MachineRoomEnum roomEnum : MachineRoomEnum.values()) {
            try {
                String resp = HttpUtils.post(roomEnum.getApiUrl() + method, params);
                RevenueDspResultDTO<List<T>> resultDTO = JSONObject.parseObject(resp, typeReference);
                resultDTO.isRight();
                if (!resultDTO.getData().isEmpty() && resultDTO.getData().get(0) instanceof RevenueDay) {
                    result.addAll(resultDTO.getData().stream().peek(
                                    u -> ((RevenueDay) u).setNodeId(roomEnum.getNodeId()))
                            .collect(Collectors.toList()));
                } else {
                    result.addAll(resultDTO.getData());
                }
            } catch (Exception e) {
                log.error("请求失败，请求地址：{} 请求参数 {}", roomEnum.getApiUrl() + method, params.toJSONString());
            }
        }
        return result;
    }


    /**
     * 保存AE数据
     *
     * @param saveVO 传入参数
     */
    private void saveAeRevenueMediaData(RevenueReportSaveVO saveVO) {
        // 1.获取Excel中的数据
        List<AeRevenueAllReportExcelDTO> aeReportList = ExcelUtils.read(saveVO.getFilePath(), AeRevenueAllReportExcelDTO.class);
        aeReportList = aeReportList.stream().filter(u -> u.getAction103() > 0).collect(Collectors.toList());
        if (aeReportList.isEmpty()) {
            return;
        }
        // 2.根据RTA的CampaignId，获取相应的RTA
        Map<String, List<RtaStrategyListDTO>> rtaStrategiesMap = this.getRtaStrategiesAll(ProjectEnum.AE.getRtaGroupId(), null, null)
                .stream().collect(Collectors.groupingBy(RtaStrategyListDTO::getRtaCountry));
        if (rtaStrategiesMap.isEmpty()) {
            throw new CustomException("没有匹配的RTA，请确认后再试");
        }
        // 3.获取Lazada的报表映射字段
        Map<String, ReportFieldDTO> reportFieldMap = this.getReportFieldMap(saveVO.getProjectId());
        reportFieldMap.keySet().removeIf(key -> !"action103".equals(key));
        Map<Long, Long> nonJvMap = new HashMap<>();
        // 4.获取需要Insert的数据
        List<RevenueReportDTO> revenueDays = aeReportList.stream().map(aeReport -> {
            long day;
            try {
                day = DateUtils.date2Long(DateUtils.string2Date(aeReport.getReportDate(), "yyyy/MM/dd"));
            } catch (Exception exception) {
                day = DateUtils.date2Long(DateUtils.string2Date(aeReport.getReportDate()));
            }
            if (day == 0) {
                return null;
            }
            if ("nonjv".equalsIgnoreCase(aeReport.getCountry())) {
                nonJvMap.put(day, aeReport.getAction103());
            }
            if (rtaStrategiesMap.containsKey(aeReport.getCountry())) {
                RtaStrategyListDTO rtaStrategy = rtaStrategiesMap.get(aeReport.getCountry()).stream().min(Comparator.comparing(RtaStrategyListDTO::getId)).orElse(null);
                RevenueReportDTO revenueDay = this.getRevenueDay(saveVO.getProjectId(), aeReport,
                        aeReport.getCountry(), rtaStrategy, reportFieldMap);
                revenueDay.setDay(day);
                return revenueDay;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        //补全DAU差值
        List<RevenueDay> dspOthers = new ArrayList<>();
        revenueDays.forEach(u -> {
            long count = this.baseMapper.getRevenueDayByWraper(new QueryWrapper<>()
                    .eq("dim_day", u.getDay())
                    .eq("dim_project_id", u.getProjectId())
                    .eq("dim_master_id", u.getMasterId())
                    .eq("dim_country_id", u.getCountryId())
                    .eq("dim_rta_id", u.getRtaId())
            );
            if (count == 0) {
                RevenueDay revenueDay = new RevenueDay();
                revenueDay.setDay(u.getDay());
                revenueDay.setProjectId(u.getProjectId());
                revenueDay.setMasterId(u.getMasterId());
                revenueDay.setCountryId(u.getCountryId());
                revenueDay.setRtaId(u.getRtaId());
                revenueDay.setAdxId(0L);
                revenueDay.setNodeId(MachineRoomEnum.SG.getNodeId());
                dspOthers.add(revenueDay);
            }
        });
        Map<Long, List<RevenueReportDTO>> allMap = revenueDays.stream().collect(Collectors.groupingBy(RevenueReportDTO::getDay));
        nonJvMap.forEach((day, nonJv) -> {
            long masterId = 0L;
            try {
                masterId = allMap.get(day).get(0).getMasterId();
            } catch (Exception e) {
                log.info("AE项目天:{}无SESSION DAU数据", day);
            }
            if (ObjectUtils.isNullOrZero(masterId)) {
                return;
            }
            Long all = allMap.getOrDefault(day, new ArrayList<>()).stream().mapToLong(u -> u.getAction5().longValue()).sum();
            if (!all.equals(nonJv)) {
                RevenueDay revenueDay = new RevenueDay();
                revenueDay.setDay(day);
                revenueDay.setProjectId(saveVO.getProjectId());
                revenueDay.setMasterId(masterId);
                revenueDay.setCountryId(0L);
                revenueDay.setRtaId(0L);
                revenueDay.setAdxId(0L);
                revenueDay.setNodeId(MachineRoomEnum.SG.getNodeId());
                dspOthers.add(revenueDay);
                RevenueReportDTO revenueReportDTO = new RevenueReportDTO();
                revenueReportDTO.setDay(day);
                revenueReportDTO.setProjectId(saveVO.getProjectId());
                revenueReportDTO.setMasterId(masterId);
                revenueReportDTO.setCountryId(0L);
                revenueReportDTO.setRtaId(0L);
                revenueReportDTO.setAction5((double) (nonJv - all));
                revenueDays.add(revenueReportDTO);
            }
        });
        // 7.执行Insert
        if (!revenueDays.isEmpty()) {
            this.executeSaveRevenueData2(revenueDays, reportFieldMap);
        }
        if (!dspOthers.isEmpty()) {
            this.baseMapper.batchInsertReplaceData(dspOthers);
        }


    }

    /**
     * 保存AE数据
     *
     * @param saveVO 传入参数
     */
    private void saveAeRevenueDetailData(RevenueReportSaveVO saveVO) {
        // 1.获取Excel中的数据
        List<AeRevenueReportExcelDTO> aeReportList = ExcelUtils.read(saveVO.getFilePath(), AeRevenueReportExcelDTO.class);
        // 2.根据RTA的CampaignId，获取相应的RTA
        Map<String, List<RtaStrategyListDTO>> rtaStrategiesMap = this.getRtaStrategiesAll(null, aeReportList.stream()
                        .map(AeRevenueReportExcelDTO::getCampaignId).distinct().collect(Collectors.toList()), null)
                .stream().collect(Collectors.groupingBy(RtaStrategyListDTO::getCampaignId));
        if (rtaStrategiesMap.isEmpty()) {
            throw new CustomException("没有匹配的RTA，请确认后再试");
        }
        // 3.获取Lazada的报表映射字段
        Map<String, ReportFieldDTO> reportFieldMap = this.getReportFieldMap(saveVO.getProjectId());
        reportFieldMap.remove("action103");
        // 4.获取需要Insert的数据
        Map<Long, Double> rtaPriceMap = new HashMap<>();
        List<RevenueReportDTO> revenueDays = new ArrayList<>() {{
            aeReportList.forEach(aeReport -> {
                if (rtaStrategiesMap.containsKey(aeReport.getCampaignId())) {
                    RtaStrategyListDTO rtaStrategy = rtaStrategiesMap.get(aeReport.getCampaignId()).stream().min(Comparator.comparing(RtaStrategyListDTO::getId)).orElse(null);
                    RevenueReportDTO revenueDay = getRevenueDay(saveVO.getProjectId(), aeReport, aeReport.getCampaignId(), rtaStrategy, reportFieldMap);
                    try {
                        revenueDay.setDay(DateUtils.date2Long(DateUtils.string2Date(aeReport.getReportDate(), "yyyy/MM/dd")));
                    } catch (Exception exception) {
                        revenueDay.setDay(DateUtils.date2Long(DateUtils.string2Date(aeReport.getReportDate())));
                    }
                    // 记录RTA-单价临时关系
                    rtaPriceMap.put(revenueDay.getRtaId(), rtaStrategy.getQualityEventPrice());
                    revenueDay.setIncome(revenueDay.getAction11());
                    add(revenueDay);
                }
            });
        }};
        // 7.执行Insert
        if (!revenueDays.isEmpty()) {
            this.executeSaveRevenueData2(revenueDays, reportFieldMap);
        }
        List<RevenueDay> others = new ArrayList<>();
        revenueDays.forEach(u -> {
            long count = this.baseMapper.getRevenueDayByWraper(new QueryWrapper<>()
                    .eq("dim_day", u.getDay())
                    .eq("dim_project_id", u.getProjectId())
                    .eq("dim_master_id", u.getMasterId())
                    .eq("dim_country_id", u.getCountryId())
                    .eq("dim_rta_id", u.getRtaId())
            );
            if (count == 0) {
                RevenueDay revenueDay = new RevenueDay();
                revenueDay.setDay(u.getDay());
                revenueDay.setProjectId(u.getProjectId());
                revenueDay.setMasterId(u.getMasterId());
                revenueDay.setCountryId(u.getCountryId());
                revenueDay.setRtaId(u.getRtaId());
                revenueDay.setAdxId(0L);
                revenueDay.setNodeId(MachineRoomEnum.SG.getNodeId());
                others.add(revenueDay);
            }
        });
        if (!others.isEmpty()) {
            this.baseMapper.batchInsertReplaceData(others);
        }
    }

}