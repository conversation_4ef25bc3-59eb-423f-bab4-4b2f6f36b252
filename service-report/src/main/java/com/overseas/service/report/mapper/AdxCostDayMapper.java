package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.adx.AdxDTO;
import com.overseas.common.dto.report.adx.AdxDailyCostListDTO;
import com.overseas.common.dto.report.openApi.xiaoMi.XiaoMiDailyCostListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.AdxCostDay;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AdxCostDayMapper extends BaseMapper<AdxCostDay> {

    @Insert("<script> " +
            "INSERT INTO m_dsp_flow_adx_cost_day " +
            "(`dim_day`,`dim_adx_id`,`dsp_view`,`dsp_unique_view`,`dsp_cost`,`customer_cost`,`dsp_unique_cost`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.day},#{item.adxId},#{item.dspView},#{item.dspUniqueView}," +
            "#{item.dspCost},#{item.customerCost},#{item.dspUniqueCost}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "dsp_view = VALUES(dsp_view), " +
            "dsp_unique_view = VALUES(dsp_unique_view), " +
            "dsp_cost = VALUES(dsp_cost), " +
            "customer_cost = VALUES(customer_cost), " +
            "dsp_unique_cost = VALUES(dsp_unique_cost) " +
            "</script>")
    void saveDspDailyCostData(@Param("list") List<AdxDailyCostListDTO> list);

    @Insert("<script> " +
            "INSERT INTO m_dsp_flow_adx_cost_day " +
            "(`dim_day`,`dim_adx_id`,`adx_view`,`adx_cost`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.day},#{item.adxId},#{item.adxView},#{item.adxCost}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "adx_view = VALUES(adx_view), " +
            "adx_cost = VALUES(adx_cost) " +
            "</script>")
    void saveAdxDailyCostData(@Param("list") List<AdxDailyCostListDTO> list);

    @Select("SELECT dim_day AS `day`,dim_adx_id AS `adx_id`,`adx_view`,`adx_cost`," +
            "`dsp_view`,`dsp_unique_view`,`dsp_cost`,`dsp_unique_cost`," +
            "`dsp_view` - `adx_view` AS `view_diff`," +
            "`dsp_unique_view` - `adx_view` AS `unique_view_diff`," +
            "ROUND(`dsp_cost` - `adx_cost`, 3) AS `cost_diff`," +
            "ROUND(`dsp_unique_cost` - `adx_cost`, 3) AS `unique_cost_diff`," +
            "ROUND((`dsp_view` - `adx_view`) / `adx_view` * 100, 3) AS `view_diff_rate`," +
            "ROUND((`dsp_unique_view` - `adx_view`) / `adx_view` * 100, 3) AS `unique_view_diff_rate`," +
            "ROUND((`dsp_cost` - `adx_cost`) / `adx_cost` * 100, 3) AS `cost_diff_rate`," +
            "ROUND((`dsp_unique_cost` - `adx_cost`) / `adx_cost` * 100, 3) AS `unique_cost_diff_rate` " +
            "FROM m_dsp_flow_adx_cost_day " +
            "${ew.customSqlSegment}")
    IPage<AdxDailyCostListDTO> listAdxDailyCost(IPage<AdxDailyCostListDTO> page,
                                                @Param(ConstantUtils.WRAPPER) Wrapper<AdxCostDay> wrapper);

    @Select("SELECT SUM(`adx_view`) AS `adx_view`,SUM(`adx_cost`) AS `adx_cost`," +
            "SUM(`dsp_view`) AS `dsp_view`,SUM(`dsp_unique_view`) AS `dsp_unique_view`," +
            "SUM(`dsp_cost`) AS `dsp_cost`,SUM(`dsp_unique_cost`) AS `dsp_unique_cost`," +
            "SUM(`dsp_view`) - SUM(`adx_view`) AS `view_diff`," +
            "SUM(`dsp_unique_view`) - SUM(`adx_view`) AS `unique_view_diff`," +
            "ROUND(SUM(`dsp_cost`) - SUM(`adx_cost`), 3) AS `cost_diff`," +
            "ROUND(SUM(`dsp_unique_cost`) - SUM(`adx_cost`), 3) AS `unique_cost_diff`," +
            "ROUND((SUM(`dsp_view`) - SUM(`adx_view`)) / SUM(`adx_view`) * 100, 3) AS `view_diff_rate`," +
            "ROUND((SUM(`dsp_unique_view`) - SUM(`adx_view`)) / SUM(`adx_view`) * 100, 3) AS `unique_view_diff_rate`," +
            "ROUND((SUM(`dsp_cost`) - SUM(`adx_cost`)) / SUM(`adx_cost`) * 100, 3) AS `cost_diff_rate`," +
            "ROUND((SUM(`dsp_unique_cost`) - SUM(`adx_cost`)) / SUM(`adx_cost`) * 100, 3) AS `unique_cost_diff_rate` " +
            "FROM m_dsp_flow_adx_cost_day " +
            "${ew.customSqlSegment}")
    AdxDailyCostListDTO getAdxDailyCostTotal(@Param(ConstantUtils.WRAPPER) Wrapper<AdxCostDay> wrapper);

    @Select("SELECT id AS adxId,cost_coefficient FROM iflytek_overseas_dsp.d_adx " +
            "${ew.customSqlSegment}")
    List<AdxDTO> listAdxId(@Param(ConstantUtils.WRAPPER) Wrapper<AdxCostDay> wrapper);

    @Select("SELECT FROM_UNIXTIME(dim_day,'%Y-%m-%d') AS `date`,customer_cost AS `cost`,dsp_view AS `impressions` " +
            "FROM m_dsp_flow_adx_cost_day " +
            "${ew.customSqlSegment}")
    List<XiaoMiDailyCostListDTO> listXiaoMiAdxCost(@Param(ConstantUtils.WRAPPER) Wrapper<AdxCostDay> wrapper);
}
