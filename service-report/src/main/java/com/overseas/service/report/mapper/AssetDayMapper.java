package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.AssetBarListDTO;
import com.overseas.common.dto.report.AssetReportListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.AssetDay;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface AssetDayMapper extends BaseMapper<AssetDay> {

    /**
     * 获取指定素材报表数据
     */
    @Select("SELECT ${ew.sqlSelect} FROM m_dsp_flow_asset_day ${ew.customSqlSegment}")
    IPage<AssetReportListDTO> getAssetReportList(IPage<AssetReportListDTO> page, @Param(ConstantUtils.WRAPPER) Wrapper<AssetDay> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM m_dsp_flow_asset_day ${ew.customSqlSegment}")
    AssetReportListDTO getAssetReportTotal(@Param(ConstantUtils.WRAPPER) Wrapper<AssetDay> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM m_dsp_flow_asset_day ${ew.customSqlSegment}")
    List<AssetReportListDTO> listAssetReport(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM m_dsp_flow_asset_day " +
            "left join iflytek_overseas_dsp.m_asset ON m_dsp_flow_asset_day.dim_asset_id = m_asset.id " +
            " ${ew.customSqlSegment}")
    List<AssetReportListDTO> listAssetReportWithAsset(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM m_dsp_flow_asset_day " +
            "left join iflytek_overseas_dsp.m_asset ON m_dsp_flow_asset_day.dim_asset_id = m_asset.id " +
            " ${ew.customSqlSegment}")
    List<AssetBarListDTO> listAssetBar(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 根据时间戳批量汇总素材小时数据插入到天表中
     *
     * @param reportDate 日期时间戳
     */
    @Insert("INSERT INTO m_dsp_flow_asset_day " +
            "( dim_day, dim_agent_id, dim_master_id, dim_campaign_id, dim_plan_id, dim_asset_id, idx_bid, idx_inner_bid," +
            " idx_win, idx_view, idx_user_view, idx_click, idx_user_click, idx_media_cost, idx_platform_cost," +
            " idx_report_cost, idx_reach, idx_action, idx_num_action, idx_action1, idx_action2, idx_action3," +
            " idx_action4, idx_action5, idx_action6, idx_action7, idx_action8, idx_action9, idx_action10, idx_action11," +
            " idx_action12, idx_action13, idx_action14, idx_action15, idx_action16, idx_action17, idx_action18," +
            " idx_action19, idx_action20, idx_action21, idx_action22, idx_action23, idx_action24, idx_action25," +
            " idx_action26, idx_action27, idx_action28, idx_action29, idx_action30," +
            " idx_action31, idx_action32, idx_action33, idx_action34, idx_action35," +
            " idx_action36, idx_action37, idx_action38, idx_action39, idx_action40," +
            " idx_settlement, idx_click_uniq_session)" +
            " SELECT * FROM (" +
            " SELECT dim_day,dim_agent_id,dim_master_id,dim_campaign_id,dim_plan_id,dim_asset_id," +
            "SUM(idx_bid) AS idx_bid,SUM(idx_inner_bid) AS idx_inner_bid,SUM(idx_win) AS idx_win," +
            "SUM(idx_view) AS idx_view,SUM(idx_user_view) AS idx_user_view,SUM(idx_click) AS idx_click," +
            "SUM(idx_user_click) AS idx_user_click,SUM(idx_media_cost) AS idx_media_cost," +
            "SUM(idx_platform_cost) AS idx_platform_cost,SUM(idx_report_cost) AS idx_report_cost," +
            "SUM(idx_reach) AS idx_reach,SUM(idx_action) AS idx_action,SUM(idx_num_action) AS idx_num_action," +
            "SUM(idx_action1) AS idx_action1,SUM(idx_action2) AS idx_action2,SUM(idx_action3) AS idx_action3," +
            "SUM(idx_action4) AS idx_action4,SUM(idx_action5) AS idx_action5,SUM(idx_action6) AS idx_action6," +
            "SUM(idx_action7) AS idx_action7,SUM(idx_action8) AS idx_action8,SUM(idx_action9) AS idx_action9," +
            "SUM(idx_action10) AS idx_action10,SUM(idx_action11) AS idx_action11,SUM(idx_action12) AS idx_action12," +
            "SUM(idx_action13) AS idx_action13,SUM(idx_action14) AS idx_action14,SUM(idx_action15) AS idx_action15," +
            "SUM(idx_action16) AS idx_action16,SUM(idx_action17) AS idx_action17,SUM(idx_action18) AS idx_action18," +
            "SUM(idx_action19) AS idx_action19,SUM(idx_action20) AS idx_action20,SUM(idx_action21) AS idx_action21," +
            "SUM(idx_action22) AS idx_action22,SUM(idx_action23) AS idx_action23,SUM(idx_action24) AS idx_action24," +
            "SUM(idx_action25) AS idx_action25,SUM(idx_action26) AS idx_action26,SUM(idx_action27) AS idx_action27," +
            "SUM(idx_action28) AS idx_action28,SUM(idx_action29) AS idx_action29,SUM(idx_action30) AS idx_action30, " +
            "SUM(idx_action31) AS idx_action31,SUM(idx_action32) AS idx_action32,SUM(idx_action33) AS idx_action33, " +
            "SUM(idx_action34) AS idx_action34,SUM(idx_action35) AS idx_action35," +
            "SUM(idx_action36) AS idx_action36,SUM(idx_action37) AS idx_action37,SUM(idx_action38) AS idx_action38, " +
            "SUM(idx_action39) AS idx_action39,SUM(idx_action40) AS idx_action40," +
            "SUM(idx_settlement) AS idx_settlement," +
            "SUM(idx_click_uniq_session) AS idx_click_uniq_session " +
            "FROM t_ads_dsp_flow_asset_hour WHERE dim_day = #{reportDate} GROUP BY dim_day,dim_asset_id " +
            ") AS tmp ON DUPLICATE KEY UPDATE idx_bid = tmp.idx_bid, idx_inner_bid = tmp.idx_inner_bid," +
            " idx_win = tmp.idx_win, idx_view = tmp.idx_view, idx_user_view = tmp.idx_user_view," +
            " idx_click = tmp.idx_click, idx_user_click = tmp.idx_user_click, idx_media_cost = tmp.idx_media_cost," +
            " idx_platform_cost = tmp.idx_platform_cost, idx_report_cost = tmp.idx_report_cost," +
            " idx_reach = tmp.idx_reach, idx_action = tmp.idx_action, idx_num_action = tmp.idx_num_action," +
            " idx_action1 = tmp.idx_action1, idx_action2 = tmp.idx_action2, idx_action3 = tmp.idx_action3," +
            " idx_action4 = tmp.idx_action4, idx_action5 = tmp.idx_action5, idx_action6 = tmp.idx_action6," +
            " idx_action7 = tmp.idx_action7, idx_action8 = tmp.idx_action8, idx_action9 = tmp.idx_action9," +
            " idx_action10 = tmp.idx_action10, idx_action11 = tmp.idx_action11, idx_action12 = tmp.idx_action12," +
            " idx_action13 = tmp.idx_action13, idx_action14 = tmp.idx_action14, idx_action15 = tmp.idx_action15," +
            " idx_action16 = tmp.idx_action16, idx_action17 = tmp.idx_action17, idx_action18 = tmp.idx_action18," +
            " idx_action19 = tmp.idx_action19, idx_action20 = tmp.idx_action20, idx_action21 = tmp.idx_action21," +
            " idx_action22 = tmp.idx_action22, idx_action23 = tmp.idx_action23, idx_action24 = tmp.idx_action24," +
            " idx_action25 = tmp.idx_action25, idx_action26 = tmp.idx_action26, idx_action27 = tmp.idx_action27," +
            " idx_action28 = tmp.idx_action28, idx_action29 = tmp.idx_action29, idx_action30 = tmp.idx_action30," +
            " idx_action31 = tmp.idx_action31, idx_action32 = tmp.idx_action32, idx_action33 = tmp.idx_action33," +
            " idx_action34 = tmp.idx_action34, idx_action35 = tmp.idx_action35, " +
            " idx_action36 = tmp.idx_action36, idx_action37 = tmp.idx_action37, idx_action38 = tmp.idx_action38," +
            " idx_action39 = tmp.idx_action39, idx_action40 = tmp.idx_action40, " +
            "idx_settlement = tmp.idx_settlement," +
            " idx_click_uniq_session = tmp.idx_click_uniq_session")
    void batchInsertAssetDayReport(@Param("reportDate") Long reportDate);

    @Update("UPDATE m_dsp_flow_asset_day AS report " +
            "INNER JOIN iflytek_overseas_dsp.m_asset AS ma ON report.dim_asset_id = ma.id " +
            "SET report.dim_user_id = ma.create_uid " +
            "WHERE report.dim_user_id = 0 AND report.dim_day = #{reportDate}")
    void batchUpdateAssetDayUserId(@Param("reportDate") Long reportDate);
}
