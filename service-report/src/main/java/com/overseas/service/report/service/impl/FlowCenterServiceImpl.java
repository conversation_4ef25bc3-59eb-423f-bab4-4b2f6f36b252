package com.overseas.service.report.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.chart.LegendDTO;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.chart.SeriesDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterCommonListDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterCompareListDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterDiagnosisDTO;
import com.overseas.common.dto.report.flow.center.distribute.FlowDistributeDTO;
import com.overseas.common.dto.report.flow.center.overview.FlowOverviewChartDTO;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.report.flow.center.FlowCenterDataEnum;
import com.overseas.common.enums.report.flow.center.FlowCenterDayOfEnum;
import com.overseas.common.enums.report.flow.center.FlowCenterDimensionEnum;
import com.overseas.common.enums.report.flow.center.FlowCenterLevelEnum;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.campaign.CampaignSelectGetVO;
import com.overseas.common.vo.market.master.MasterSelectByInfoVO;
import com.overseas.common.vo.market.plan.PlanSelectGetVO;
import com.overseas.common.vo.market.plan.direct.AdxSelectGetVO;
import com.overseas.common.vo.market.plan.direct.EpSelectGetVO;
import com.overseas.common.vo.report.flow.center.FlowCenterReportListVO;
import com.overseas.common.vo.report.flow.center.compare.FlowCenterCompareListDownloadVO;
import com.overseas.common.vo.report.flow.center.compare.FlowCenterCompareListVO;
import com.overseas.common.vo.report.flow.center.detail.FlowCenterDetailChartVO;
import com.overseas.common.vo.report.flow.center.detail.FlowCenterDetailListDownloadVO;
import com.overseas.common.vo.report.flow.center.detail.FlowCenterDetailListVO;
import com.overseas.common.vo.report.flow.center.distribute.FlowCenterDistributeListDownloadVO;
import com.overseas.common.vo.report.flow.center.distribute.FlowCenterDistributeVO;
import com.overseas.common.vo.report.flow.center.overview.FlowCenterOverviewVO;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.common.vo.sys.project.ProjectSelectByInfoVO;
import com.overseas.service.report.feign.FgMarketService;
import com.overseas.service.report.feign.FgSystemService;
import com.overseas.service.report.service.FlowCenterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class FlowCenterServiceImpl implements FlowCenterService {

    private final FgMarketService fgMarketService;

    private final FgSystemService fgSystemService;

    @Override
    public List<FlowOverviewChartDTO> overviewChart(FlowCenterOverviewVO overviewVO) {
        FlowCenterReportListVO listVO = new FlowCenterReportListVO();
        listVO.setStart(overviewVO.getStartDate());
        listVO.setEnd(overviewVO.getEndDate());
        listVO.setLevel(FlowCenterLevelEnum.REQUEST.getName());
        listVO.setDayOf(List.of(overviewVO.getDayOf()));
        List<JSONObject> current = this.getFlowData(listVO, new TypeReference<>() {
        });
        List<JSONObject> last = null;
        if (overviewVO.getIsCompare() == 1) {
            //如果开始时间是今天， 则需要设置昨日的设置开始小时和结束小时
            String today = DateUtils.format(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getStart()), listVO.getTimezone(), 0));
            if (null != today && today.equals(DateUtils.format(new Date()))) {
                listVO.setStartHour(0);
                listVO.setEndHour(DateUtils.getDateHour(TimeZoneEnum.getUTC_8Date(new Date(), listVO.getTimezone(), 0), 0));
            }
            //获取昨日时间
            DateUtils.Cycle cycle = DateUtils.lastCycle(overviewVO.getStartDate(), overviewVO.getEndDate());
            listVO.setStart(DateUtils.format(cycle.getStart()));
            listVO.setEnd(DateUtils.format(cycle.getEnd()));
            last = this.getFlowData(listVO, new TypeReference<>() {
            });
        }
        List<String> range;
        List<String> lastRange;
        if (Objects.equals(overviewVO.getDayOf(), FlowCenterDayOfEnum.DAY.getId())) {
            range = DateUtils.getBetweenDate(overviewVO.getStartDate(), overviewVO.getEndDate(), DateUtils.DATE_PATTERN);
            lastRange = DateUtils.getBetweenDate(listVO.getStart(), listVO.getEnd(), DateUtils.DATE_PATTERN);
        } else {
            range = DateUtils.getBetweenHour(overviewVO.getStartDate());
            lastRange = range;
        }
        List<FlowOverviewChartDTO> list = completeChart(current, last, range, lastRange, overviewVO.getDayOf(),
                Arrays.stream(FlowCenterDataEnum.values()).collect(Collectors.toMap(FlowCenterDataEnum::getField, FlowCenterDataEnum::getName))
        );
        Map<String, FlowOverviewChartDTO> result = list.stream().collect(Collectors.toMap(FlowOverviewChartDTO::getKey, Function.identity()));
        FlowOverviewChartDTO req = result.get(FlowCenterDataEnum.REQ.getField());
        FlowOverviewChartDTO bidFailed = result.get(FlowCenterDataEnum.BID_FAILED.getField());
        FlowOverviewChartDTO failedRatio = result.get(FlowCenterDataEnum.FAILED_RATIO.getField());
        failedRatio.setCurrent(DoubleUtils.getRate(BigDecimal.valueOf(bidFailed.getCurrent()), BigDecimal.valueOf(req.getCurrent())));
        failedRatio.setLast(DoubleUtils.getRate(BigDecimal.valueOf(bidFailed.getLast()), BigDecimal.valueOf(req.getLast())));
        return list;
    }

    @Override
    public FlowCenterDiagnosisDTO overviewDiagnosis(FlowCenterOverviewVO overviewVO) {
        FlowCenterReportListVO listVO = new FlowCenterReportListVO();
        listVO.setStart(overviewVO.getStartDate());
        listVO.setEnd(overviewVO.getEndDate());
        //增加项目
        if (CollectionUtils.isNotEmpty(overviewVO.getProjectIds())) {
            listVO.setProjectIds(overviewVO.getProjectIds());
        }
        //增加账户数据
        if (CollectionUtils.isNotEmpty(overviewVO.getMasterIds())) {
            listVO.setMasterIds(overviewVO.getMasterIds());
        }
        //账户维度 or 请求维度
        if (ObjectUtils.isNotNullOrZero(overviewVO.getMasterId())) {
            listVO.setMasterIds(List.of(overviewVO.getMasterId().longValue()));
        }
        listVO.setAdxIds(overviewVO.getAdxIds());
        listVO.setEpIds(overviewVO.getEpIds());
        listVO.setAreaIds(overviewVO.getAreaIds());
        listVO.setPlanIds(overviewVO.getPlanIds());
        listVO.setCampaignIds(overviewVO.getCampaignIds());
        if (StringUtils.isNotBlank(overviewVO.getPkg())) {
            listVO.setPkgs(List.of(overviewVO.getPkg()));
        }
        listVO.setDayOf(List.of(overviewVO.getDayOf()));
        listVO.setTimezone(overviewVO.getTimezone());
        FlowCenterDiagnosisDTO current = this.getDiagnosisData(listVO);
        Map<String, FlowCenterDiagnosisDTO.Diagnose> lastDiagnoseMap;
        List<String> hour = DateUtils.getBetweenHour(DateUtils.format(DateUtils.string2Date(listVO.getStart())));
        if (overviewVO.getIsCompare() == 1) {
            //如果开始时间是今天， 则需要设置昨日的设置开始小时和结束小时
            String today = DateUtils.format(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getStart()), listVO.getTimezone(), 0));
            if (null != today && today.equals(DateUtils.format(new Date()))) {
                listVO.setStartHour(0);
                listVO.setEndHour(DateUtils.getDateHour(TimeZoneEnum.getUTC_8Date(new Date(), listVO.getTimezone(), 0), 0));
            }
            DateUtils.Cycle cycle = DateUtils.lastCycle(overviewVO.getStartDate(), overviewVO.getEndDate());
            listVO.setStart(DateUtils.format(cycle.getStart()));
            listVO.setEnd(DateUtils.format(cycle.getEnd()));
            lastDiagnoseMap = this.getDiagnosisData(listVO).getDiagnoses().stream().collect(Collectors.toMap(FlowCenterDiagnosisDTO.BaseDiagnose::getName, Function.identity()));
        } else {
            lastDiagnoseMap = new HashMap<>();
        }
        Map<String, FlowCenterDiagnosisDTO.Diagnose> currentDiagnoseMap = current.getDiagnoses().stream().collect(Collectors.toMap(FlowCenterDiagnosisDTO.BaseDiagnose::getName, Function.identity()));
        for (String diagName : combineName(current.getDiagnoses(), new ArrayList<>(lastDiagnoseMap.values()))) {
            FlowCenterDiagnosisDTO.Diagnose diag = currentDiagnoseMap.get(diagName);
            FlowCenterDiagnosisDTO.Diagnose lastDiagnosis = lastDiagnoseMap.get(diagName);
            //一级数据处理
            if (null != diag) {
                diag.setBidFailed(diag.getIn() - diag.getOut());
                diag.setFailedRatio(DoubleUtils.getRate(BigDecimal.valueOf(diag.getBidFailed()), BigDecimal.valueOf(diag.getIn())).floatValue());
                diag.setSuccessRatio(DoubleUtils.getRate(BigDecimal.valueOf(diag.getOut()), BigDecimal.valueOf(diag.getIn())).floatValue());
                diag.setRatio(BigDecimal.valueOf(100).subtract(BigDecimal.valueOf(diag.getRatio())).setScale(2, RoundingMode.HALF_UP).floatValue());
            } else {
                diag = new FlowCenterDiagnosisDTO.Diagnose();
                diag.setName(diagName);
            }
            if (null != lastDiagnosis) {
                diag.setLastIn(lastDiagnosis.getIn());
                diag.setLastOut(lastDiagnosis.getOut());
                diag.setLastData(lastDiagnosis.getData());
                diag.setLastRatio(BigDecimal.valueOf(100).subtract(BigDecimal.valueOf(lastDiagnosis.getRatio())).setScale(2, RoundingMode.HALF_UP).floatValue());
                diag.setLastBidFailed(diag.getLastIn() - diag.getLastOut());
                diag.setLastFailedRatio(DoubleUtils.getRate(BigDecimal.valueOf(diag.getLastBidFailed()), BigDecimal.valueOf(diag.getLastIn())).floatValue());
                diag.setLastSuccessRatio(DoubleUtils.getRate(BigDecimal.valueOf(diag.getLastOut()), BigDecimal.valueOf(diag.getLastIn())).floatValue());
            }
            diag.setChart(completeChart(JSONObject.parseArray(JSONObject.toJSONString(diag.getData()), JSONObject.class),
                    JSONObject.parseArray(JSONObject.toJSONString(diag.getLastData()), JSONObject.class),
                    hour, hour, FlowCenterDayOfEnum.HOUR.getId(), Map.of("out", "数量")
            ).get(0).getData());

            //二级数据
            Map<String, FlowCenterDiagnosisDTO.SubDiagnose> lastSubDiagnosisMap = null == lastDiagnosis ? new HashMap<>()
                    : lastDiagnosis.getSubDiagnoses().stream().collect(Collectors.toMap(FlowCenterDiagnosisDTO.BaseDiagnose::getName, Function.identity()));
            Map<String, FlowCenterDiagnosisDTO.SubDiagnose> currentSubDiagnosisMap =
                    diag.getSubDiagnoses().stream().collect(Collectors.toMap(FlowCenterDiagnosisDTO.BaseDiagnose::getName, Function.identity()));
            for (String subDiagName : combineName(diag.getSubDiagnoses(), new ArrayList<>(lastSubDiagnosisMap.values()))) {
                FlowCenterDiagnosisDTO.SubDiagnose currentSubDiag = currentSubDiagnosisMap.get(subDiagName);
                FlowCenterDiagnosisDTO.SubDiagnose lastSubDiag = lastSubDiagnosisMap.get(subDiagName);
                if (null == currentSubDiag) {
                    currentSubDiag = new FlowCenterDiagnosisDTO.SubDiagnose();
                    currentSubDiag.setName(subDiagName);
                } else {
                    currentSubDiag.setRatio(BigDecimal.valueOf(currentSubDiag.getRatio()).setScale(2, RoundingMode.HALF_UP).floatValue());
                }
                if (null != lastSubDiag) {
                    currentSubDiag.setLastIn(lastSubDiag.getIn());
                    currentSubDiag.setLastOut(lastSubDiag.getOut());
                    currentSubDiag.setLastFilterNum(lastSubDiag.getFilterNum());
                    currentSubDiag.setLastRatio(BigDecimal.valueOf(100).subtract(BigDecimal.valueOf(lastSubDiag.getRatio())).setScale(2, RoundingMode.HALF_UP).floatValue());
                    currentSubDiag.setLastData(lastSubDiag.getData());
                }
            }
        }
        return current;
    }


    private List<String> combineName(List<? extends FlowCenterDiagnosisDTO.BaseDiagnose> current,
                                     List<? extends FlowCenterDiagnosisDTO.BaseDiagnose> last) {
        return Stream.of(current.stream().map(FlowCenterDiagnosisDTO.BaseDiagnose::getName),
                last.stream().map(FlowCenterDiagnosisDTO.BaseDiagnose::getName)
        ).flatMap(u -> u).distinct().collect(Collectors.toList());
    }

    @Override
    public List<FlowDistributeDTO> distributeChart(FlowCenterDistributeVO distributeVO) {
        FlowCenterDimensionEnum dimensionEnum = FlowCenterDimensionEnum.getByKey(distributeVO.getDimension());
        if (null == dimensionEnum) {
            return List.of();
        }
        distributeVO.setDimensions(List.of(distributeVO.getDimension()));
        List<JSONObject> result = this.distributeChartData(distributeVO, null);
        List<FlowDistributeDTO> flows = result.stream().map(u -> {
            FlowDistributeDTO distribute = new FlowDistributeDTO();
            distribute.setId(u.getString(dimensionEnum.getField()));
            distribute.setVal(u.getLong(distributeVO.getCustomIndex()));
            return distribute;
        }).collect(Collectors.toList());
        Map<String, String> targetMap = this.findDimMap(dimensionEnum, flows.stream().map(u -> Long.parseLong(u.getId())).collect(Collectors.toList()));
        long sum = flows.stream().mapToLong(FlowDistributeDTO::getVal).sum();
        flows.forEach(flow -> {
            flow.setTarget(targetMap.getOrDefault(flow.getId(), ConstantUtils.UNKNOWN));
            flow.setRatio(DoubleUtils.getRate(new BigDecimal(flow.getVal()), new BigDecimal(sum)).floatValue());
        });
        return flows;
    }

    @Override
    public MultiIndexChartDTO distributeChart2(FlowCenterDistributeVO distributeVO) {
        List<FlowCenterDimensionEnum> dimensionEnums = distributeVO.getDimensions().stream().map(FlowCenterDimensionEnum::getByKey)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dimensionEnums)) {
            return new MultiIndexChartDTO();
        }
        List<JSONObject> result = this.distributeChartData(distributeVO, null);
        List<JSONObject> result2 = result.stream().filter(u -> {
            if (u.getLong(distributeVO.getCustomIndex()) != 0) {
                if (u.getLong("bid") > 0) {
                    return true;
                } else if (u.getLong("req") > 100) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
        Set<Long> dims1 = new HashSet<>(), dims2 = new HashSet<>();
        result2.forEach(u -> {
            dims1.add(u.getLong(dimensionEnums.get(0).getField()));
            dims2.add(u.getLong(dimensionEnums.get(1).getField()));
        });
        Map<String, JSONObject> resultMap = result.stream().collect(Collectors.toMap(
                u -> String.format("%s-%s", u.getString(dimensionEnums.get(0).getField()), u.getString(dimensionEnums.get(1).getField())), Function.identity()
        ));
        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        Map<String, String> name1Map = findDimMap(dimensionEnums.get(0), new ArrayList<>(dims1));
        Map<String, String> name2Map = findDimMap(dimensionEnums.get(1), new ArrayList<>(dims2));
        multiIndexChartDTO.setXAxis(List.of(new LegendDTO(new ArrayList<>(dims1.stream().map(u -> name1Map.getOrDefault(u.toString(), ConstantUtils.UNKNOWN)).collect(Collectors.toList())))));
        multiIndexChartDTO.setSeries(new ArrayList<>());
        dims2.forEach(dim2 -> {
            SeriesDTO seriesDTO = new SeriesDTO();
            seriesDTO.setName(name2Map.getOrDefault(dim2.toString(), ConstantUtils.UNKNOWN));
            seriesDTO.setType("bar");
            seriesDTO.setData(dims1.stream().map(dim1 -> resultMap.getOrDefault(String.format("%s-%s", dim1, dim2), new JSONObject())
                    .getOrDefault(distributeVO.getCustomIndex(), 0)).collect(Collectors.toList()));
            multiIndexChartDTO.getSeries().add(seriesDTO);
        });
        return multiIndexChartDTO;
    }


    @Override
    public List<FlowCenterCommonListDTO> distributeList(FlowCenterDistributeVO distributeVO) {
        Map<String, String> replaceMap = new HashMap<>() {{
            put("epId", "ep");
            put("adxId", "adx");
            put("areaCode", "area");
        }};
        distributeVO.setDimensions(distributeVO.getDimensions().stream()
                .map(u -> replaceMap.getOrDefault(u, u))
                .distinct()
                .collect(Collectors.toList()));
        if (StringUtils.isBlank(distributeVO.getSortField())) {
            distributeVO.setSortField("req");
            distributeVO.setSortType("desc");
        }
        List<JSONObject> result = this.distributeChartData(distributeVO, 1000);
        if (result.isEmpty()) {
            return List.of();
        }
        completeName(distributeVO.getDimensions(), result);
        return JSONObject.parseObject(JSONObject.toJSONString(result), new TypeReference<>() {
        });
    }

    /**
     * 流量分布数据获取
     *
     * @param distributeVO 分布
     * @return 返回数据
     */
    private List<JSONObject> distributeChartData(FlowCenterDistributeVO distributeVO, Integer limit) {
        FlowCenterReportListVO listVO = new FlowCenterReportListVO();
        listVO.setStart(distributeVO.getStartDate());
        listVO.setEnd(distributeVO.getEndDate());
        listVO.setTimezone(distributeVO.getTimeZone());
        listVO.setAdxIds(distributeVO.getAdxIds());
        listVO.setEpIds(distributeVO.getEpIds());
        listVO.setAreaIds(distributeVO.getAreaIds());
        if (StringUtils.isNotBlank(distributeVO.getPkg())) {
            listVO.setPkgs(List.of(distributeVO.getPkg()));
        }
        if (StringUtils.isNotBlank(distributeVO.getSortField()) && StringUtils.isNotBlank(distributeVO.getSortType())) {
            listVO.setOrderBy(distributeVO.getSortField());
            listVO.setIsAsc("asc".equalsIgnoreCase(distributeVO.getSortType()));
        }
        if (ObjectUtils.isNotNullOrZero(limit)) {
            listVO.setLimit(limit);
        }
        listVO.setDayOf(List.of());
        listVO.setDimensions(distributeVO.getDimensions());
        List<JSONObject> result = this.getFlowData(listVO, new TypeReference<>() {
        });
        if (result.isEmpty()) {
            return List.of();
        }
        return result;
    }

    @Override
    public void distributeListDownload(FlowCenterDistributeListDownloadVO downloadVO, HttpServletResponse response) throws IOException {
        List<FlowCenterCommonListDTO> list = this.distributeList(downloadVO);
        ExcelUtils.download(response, "流量中心-分布详情-地域", "列表", list,
                exportHeader(null, List.of(downloadVO.getDimension()), downloadVO.getDownloads())
        );
    }

    @Override
    public MultiIndexChartDTO detailChart(FlowCenterDetailChartVO detailVO) {
        FlowCenterReportListVO listVO = new FlowCenterReportListVO();
        BeanUtils.copyProperties(detailVO, listVO);
        listVO.setStart(detailVO.getStartDate());
        listVO.setEnd(detailVO.getEndDate());
        listVO.setTimezone(detailVO.getTimeZone());
        listVO.setAdxIds(detailVO.getAdxIds());
        listVO.setEpIds(detailVO.getEpIds());
        listVO.setAreaIds(detailVO.getAreaIds());
        if (StringUtils.isNotBlank(detailVO.getPkg())) {
            listVO.setPkgs(List.of(detailVO.getPkg()));
        }
        listVO.setLevel(FlowCenterLevelEnum.findLevel(detailVO.getDimensions()).getName());
        listVO.setDayOf(List.of(detailVO.getDayOf()));
        //活动选择前10
        if (detailVO.getDimensions().contains(FlowCenterDimensionEnum.CAMPAIGN.getKey()) &&
                CollectionUtils.isNotEmpty(detailVO.getCampaignIds())) {
            listVO.setOrderBy(detailVO.getDimensions().get(0));
            listVO.setIsAsc(false);
            listVO.setLimit(10);
        }
        List<JSONObject> result = this.getFlowData(listVO, new TypeReference<>() {
        });
        List<String> dateRange;
        boolean isHour = detailVO.getDayOf().equals(FlowCenterDayOfEnum.HOUR.getId());
        if (isHour) {
            dateRange = DateUtils.getBetweenHour(detailVO.getStartDate());
        } else {
            dateRange = DateUtils.getBetweenDate(detailVO.getStartDate(), detailVO.getEndDate(), DateUtils.DATE_PATTERN);
        }
        FlowCenterDimensionEnum dimensionEnum = FlowCenterDimensionEnum.getByKey(detailVO.getDimensions().get(0));
        if (null == dimensionEnum) {
            return new MultiIndexChartDTO();
        }
        Map<Long, List<JSONObject>> dimMap = result.stream().collect(Collectors.groupingBy(u -> u.getLong(dimensionEnum.getField())));
        MultiIndexChartDTO chartDTO = new MultiIndexChartDTO();
        Map<String, String> targetMap = findDimMap(dimensionEnum, new ArrayList<>(dimMap.keySet()));
        //结合指标展示数据
        List<FlowCenterDataEnum> dataEnums = FlowCenterDataEnum.getByFields(detailVO.getCustomIndexes());
        chartDTO.setLegend(new LegendDTO(targetMap.values().stream().flatMap(target -> dataEnums.stream().map(custom -> String.format("%s-%s", target, custom.getName()))).collect(Collectors.toList())));
        //x轴数据
        chartDTO.setXAxis(List.of(new LegendDTO(dateRange)));
        //y轴数据
        dimMap.forEach((key, value) -> {
            Map<String, JSONObject> map = value.stream().collect(Collectors.toMap(u -> u.getString(detailVO.getDayOf()), Function.identity(), (o, n) -> n));
            dataEnums.forEach(customIndex -> {
                SeriesDTO seriesDTO = new SeriesDTO();
                seriesDTO.setName(String.format("%s-%s", targetMap.get(key.toString()), customIndex.getName()));
                seriesDTO.setType("line");
                seriesDTO.setData(this.rangeData(customIndex.getField(), dateRange, isHour, map));
                double max = seriesDTO.getData().stream().collect(Collectors.summarizingDouble(x -> Double.parseDouble(x.toString()))).getMax();
                seriesDTO.setData(seriesDTO.getData().stream().map(u -> {
                    if (max == 0) {
                        return Map.of("ratio", 0, "val", u);
                    } else {
                        return Map.of("ratio", new BigDecimal(u.toString()).multiply(BigDecimal.valueOf(100d)).divide(BigDecimal.valueOf(max), 2, RoundingMode.HALF_UP),
                                "val", u);
                    }
                }).collect(Collectors.toList()));
                chartDTO.getSeries().add(seriesDTO);
            });
        });
        return chartDTO;
    }

    @Override
    public List<FlowCenterCommonListDTO> detailList(FlowCenterDetailListVO listVO) {
        FlowCenterReportListVO dataListVO = new FlowCenterReportListVO();
        BeanUtils.copyProperties(listVO, dataListVO);
        dataListVO.setStart(listVO.getStartDate());
        dataListVO.setEnd(listVO.getEndDate());
        dataListVO.setTimezone(listVO.getTimeZone());
        dataListVO.setLevel(FlowCenterLevelEnum.findLevel(listVO.getDimensions()).getName());
        dataListVO.setOrderBy(StringUtils.isNotEmpty(listVO.getSortField()) ? listVO.getSortField() : "req");
        dataListVO.setIsAsc("asc".equalsIgnoreCase(listVO.getSortType()));
        if (CollectionUtils.isEmpty(listVO.getDayOf())) {
            dataListVO.setDayOf(listVO.getDimensions().stream().filter(u -> List.of("day", "hour").contains(u)).collect(Collectors.toList()));
        }
        if (null != listVO.getHour()) {
            dataListVO.setStartHour(listVO.getHour());
            dataListVO.setEndHour(listVO.getHour());
        }
        List<JSONObject> list = this.getFlowData(dataListVO, new TypeReference<>() {
        });
        completeName(listVO.getDimensions(), list);
        return JSONObject.parseObject(JSONObject.toJSONString(list), new TypeReference<>() {
        });
    }

    @Override
    public void detailListDownload(FlowCenterDetailListDownloadVO downloadVO, HttpServletResponse response) throws IOException {
        List<FlowCenterCommonListDTO> list = this.detailList(downloadVO);
        ExcelUtils.download(response, "流量中心-详情列表", "列表", list,
                exportHeader(downloadVO.getDayOf(), downloadVO.getDimensions(), downloadVO.getDownloads())
        );
    }

    @Override
    public List<FlowCenterCompareListDTO> compareList(FlowCenterCompareListVO listVO) {
        FlowCenterDiagnosisDTO current = this.diagListData(listVO, listVO.getStartDate());
        FlowCenterDiagnosisDTO target = this.diagListData(listVO, listVO.getEndDate());

        List<FlowCenterDiagnosisDTO.SubDiagnose> currentSubDiagnoses = current.getDiagnoses().stream()
                .flatMap(u -> u.getSubDiagnoses().stream()).collect(Collectors.toList());

        List<FlowCenterDiagnosisDTO.SubDiagnose> targetSubDiagnoses = target.getDiagnoses().stream()
                .flatMap(u -> u.getSubDiagnoses().stream()).collect(Collectors.toList());

        Map<String, FlowCenterDiagnosisDTO.SubDiagnose> currentMap = currentSubDiagnoses.stream()
                .collect(Collectors.toMap(FlowCenterDiagnosisDTO.BaseDiagnose::getName, Function.identity()));
        Map<String, FlowCenterDiagnosisDTO.SubDiagnose> targetMap = targetSubDiagnoses.stream()
                .collect(Collectors.toMap(FlowCenterDiagnosisDTO.BaseDiagnose::getName, Function.identity()));

        Map<String, FlowCenterCompareListDTO> result = new HashMap<>();
        Stream.of(currentMap.keySet(), targetMap.keySet()).flatMap(Collection::stream)
                .forEach(reason -> {
                    FlowCenterCompareListDTO dto = result.computeIfAbsent(reason, k -> new FlowCenterCompareListDTO());
                    dto.setName(reason);
                    FlowCenterDiagnosisDTO.SubDiagnose currentSub = currentMap.get(reason);
                    FlowCenterDiagnosisDTO.SubDiagnose targetSub = targetMap.get(reason);
                    if (null != currentSub) {
                        dto.setTodayIn(currentSub.getIn());
                        dto.setTodayOut(currentSub.getOut());
                        dto.setTodayRatio(BigDecimal.valueOf(100).subtract(BigDecimal.valueOf(currentSub.getRatio())).setScale(2, RoundingMode.HALF_UP));
                    }
                    if (null != targetSub) {
                        dto.setTargetIn(targetSub.getIn());
                        dto.setTargetOut(targetSub.getOut());
                        dto.setTargetRatio(BigDecimal.valueOf(100).subtract(BigDecimal.valueOf(targetSub.getRatio())).setScale(2, RoundingMode.HALF_UP));
                    }
                    //差值
                    dto.setDiffIn(dto.getTargetIn() - dto.getTodayIn());
                    dto.setDiffOut(dto.getTargetOut() - dto.getTodayOut());
                    dto.setDiffRatio(dto.getTargetRatio().subtract(dto.getTodayRatio()));
                    //差值比例
                    dto.setDiffInRatio(DoubleUtils.getRate(BigDecimal.valueOf(dto.getDiffIn()), BigDecimal.valueOf(dto.getTargetIn())));
                    dto.setDiffOutRatio(DoubleUtils.getRate(BigDecimal.valueOf(dto.getDiffOut()), BigDecimal.valueOf(dto.getTargetOut())));
                    dto.setDiffRatioRatio(DoubleUtils.getRate(dto.getDiffRatio(), dto.getTargetRatio()));
                });
        return new ArrayList<>(result.values());
    }

    /**
     * 获取数据
     *
     * @param overviewVO 条件
     * @param day        天
     * @return 返回数据
     */
    private FlowCenterDiagnosisDTO diagListData(FlowCenterCompareListVO overviewVO, String day) {
        FlowCenterReportListVO listVO = new FlowCenterReportListVO();
        listVO.setStart(day);
        listVO.setEnd(day);
        listVO.setDayOf(List.of("day"));
        //增加项目
        if (CollectionUtils.isNotEmpty(overviewVO.getProjectIds())) {
            listVO.setProjectIds(overviewVO.getProjectIds());
        }
        listVO.setStartHour(overviewVO.getStartHour());
        listVO.setEndHour(overviewVO.getEndHour());
        listVO.setProjectIds(overviewVO.getProjectIds());
        listVO.setAdxIds(overviewVO.getAdxIds());
        listVO.setEpIds(overviewVO.getEpIds());
        listVO.setAreaIds(overviewVO.getAreaIds());
        listVO.setPlanIds(overviewVO.getPlanIds());
        listVO.setCampaignIds(overviewVO.getCampaignIds());
        if (StringUtils.isNotBlank(overviewVO.getPkg())) {
            listVO.setPkgs(List.of(overviewVO.getPkg()));
        }
        listVO.setTimezone(overviewVO.getTimezone());
        return this.getDiagnosisData(listVO);
    }

    @Override
    public void compareListDownload(FlowCenterCompareListDownloadVO downloadVO, HttpServletResponse response) throws IOException {
        List<FlowCenterCompareListDTO> list = this.compareList(downloadVO);
        ExcelUtils.download(response, "流量中心-对比数据", list, new ArrayList<>());
    }

    /**
     * 完善 chart 数据
     *
     * @param current   当前数据
     * @param last      上期数据
     * @param range     数据
     * @param lastRange 上期周期
     * @param dayOf     天
     * @param fields    返回数据字段
     * @return 返回数据
     */
    @Override
    public List<FlowOverviewChartDTO> completeChart(List<JSONObject> current, List<JSONObject> last,
                                                    List<String> range, List<String> lastRange,
                                                    String dayOf, Map<String, String> fields) {
        List<String> legend = new ArrayList<>() {{
            add("本期");
        }};
        boolean isHour = Objects.equals(dayOf, FlowCenterDayOfEnum.HOUR.getId());
        Map<String, JSONObject> currentMap = current.stream().collect(Collectors.toMap(u -> {
            if (isHour) {
                return u.getString("hour");
            }
            return u.getString("day");
        }, Function.identity(), (o, n) -> n));
        Map<String, JSONObject> lastMap;
        if (null != last) {
            lastMap = last.stream().collect(Collectors.toMap(u -> {
                if (isHour) {
                    return u.getString("hour");
                }
                return u.getString("day");
            }, Function.identity(), (o, n) -> n));
            legend.add("上期");
        } else {
            lastMap = null;
        }
        List<FlowOverviewChartDTO> result = new ArrayList<>();
        fields.forEach((field, name) -> {
            FlowOverviewChartDTO chartDTO = new FlowOverviewChartDTO();
            chartDTO.setKey(field);
            chartDTO.setTitle(name);
            MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
            multiIndexChartDTO.setLegend(new LegendDTO(legend));
            multiIndexChartDTO.setXAxis(List.of(new LegendDTO(range)));
            //本期数据
            List<Object> seriesData = this.rangeData(field, range, isHour, currentMap);
            multiIndexChartDTO.getSeries().add(new SeriesDTO("本期", "line", seriesData));
            chartDTO.setCurrent(seriesData.stream().mapToDouble(u -> Double.parseDouble(u.toString())).sum());
            //上期数据
            if (null != lastMap) {
                List<Object> lastSeriesData = this.rangeData(field, lastRange, isHour, lastMap);
                multiIndexChartDTO.getSeries().add(new SeriesDTO("上期", "line", lastSeriesData));
                chartDTO.setLast(lastSeriesData.stream().mapToDouble(u -> Double.parseDouble(u.toString())).sum());
            }
            chartDTO.setData(multiIndexChartDTO);
            if (ObjectUtils.isNullOrZero(chartDTO.getLast())) {
                chartDTO.setRatio(0f);
            } else {
                chartDTO.setRatio(DoubleUtils.getRate(BigDecimal.valueOf(chartDTO.getCurrent() - chartDTO.getLast()), BigDecimal.valueOf(chartDTO.getLast())).floatValue());
            }
            result.add(chartDTO);
        });
        return result;
    }

    /**
     * 获取维度名称
     *
     * @param dimensionEnum 维度美剧
     * @param infoIds       数据
     * @return 返回数据
     */
    private Map<String, String> findDimMap(FlowCenterDimensionEnum dimensionEnum, List<Long> infoIds) {
        if (infoIds.isEmpty()) {
            return Map.of();
        }
        Map<String, String> targetMap;
        switch (dimensionEnum) {
            case PROJECT:
                targetMap = fgSystemService.selectProjectByInfo(
                        ProjectSelectByInfoVO.builder().ids(infoIds).build()
                ).getData().stream().collect(Collectors.toMap(u -> u.getId().toString(), SelectDTO::getName));
                break;
            case MASTER:
                targetMap = fgMarketService.selectMasterByInfo(
                        MasterSelectByInfoVO.builder().masterIds(infoIds).build()
                ).getData().stream().collect(Collectors.toMap(u -> u.getId().toString(), SelectDTO::getName));
                break;
            case CAMPAIGN:
                targetMap = fgMarketService.selectCampaign(
                        CampaignSelectGetVO.builder().campaignIds(infoIds).build()
                ).getData().stream().collect(Collectors.toMap(u -> u.getId().toString(), SelectDTO::getName));
                break;
            case PLAN:
                targetMap = fgMarketService.selectPlan(
                        PlanSelectGetVO.builder().planIds(infoIds).build()
                ).getData().stream().collect(Collectors.toMap(u -> u.getId().toString(), SelectDTO::getName));
                break;
            case AREA:
                targetMap = fgSystemService.selectCountry(
                        AreaCountrySelectVO.builder().countryIds(infoIds).build()
                ).getData().stream().collect(Collectors.toMap(u -> u.getId().toString(), SelectDTO::getName, (o, n) -> o));
                break;
            case ADX:
                targetMap = fgMarketService.selectAdx(
                        AdxSelectGetVO.builder().adxIds(infoIds).build()
                ).getData().stream().collect(Collectors.toMap(u -> u.getId().toString(), SelectDTO::getName));
                break;
            case EP:
                targetMap = fgMarketService.selectEp(
                        EpSelectGetVO.builder().epIds(infoIds).build()
                ).getData().stream().collect(Collectors.toMap(u -> u.getId().toString(), SelectDTO::getName));
                break;
            default:
                targetMap = new HashMap<>();
        }
        return targetMap;
    }


    /**
     * 处理数据
     *
     * @param dimensions 维度
     * @param list       list
     */
    private void completeName(List<String> dimensions, List<JSONObject> list) {
        for (String dimKey : dimensions) {
            FlowCenterDimensionEnum dimensionEnum = FlowCenterDimensionEnum.getByKey(dimKey.replace("Name", ""));
            if (null == dimensionEnum) {
                continue;
            }
            completeName(dimensionEnum, list);
        }
    }

    /**
     * 完善名称
     *
     * @param dimensionEnum 类型
     * @param list          列表
     */
    private void completeName(FlowCenterDimensionEnum dimensionEnum, List<JSONObject> list) {
        if (FlowCenterDimensionEnum.PKG.equals(dimensionEnum)) {
            return;
        }
        Map<String, String> targetMap = this.findDimMap(dimensionEnum,
                list.stream().map(u -> u.getLong(dimensionEnum.getField())).collect(Collectors.toList())
        );
        list.forEach(datum ->
                datum.put(
                        String.format("%sName", dimensionEnum.getKey()),
                        targetMap.getOrDefault(datum.getString(dimensionEnum.getField()), ConstantUtils.UNKNOWN)
                ));
    }

    /**
     * 循环提取数据
     *
     * @param field     字段
     * @param range     日期
     * @param isHour    是否小时
     * @param resultMap 结果map
     * @return 返回数据
     */
    private List<Object> rangeData(String field, List<String> range,
                                   Boolean isHour, Map<String, JSONObject> resultMap) {
        return range.stream().map(ran -> {
            String idK = isHour ? String.valueOf(Integer.parseInt(ran.split(":")[0])) : ran;
            if (resultMap.containsKey(idK)) {
                return resultMap.get(idK).getString(field);
            }
            return 0L;
        }).collect(Collectors.toList());
    }

    /**
     * 导出 header
     *
     * @param dayOf      时间
     * @param dimensions 维度
     * @param downloads  下载数据
     * @return 返回数据
     */
    private List<SelectDTO2> exportHeader(List<String> dayOf, List<String> dimensions, List<String> downloads) {
        List<SelectDTO2> headers = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dayOf)) {
            headers.addAll(Arrays.stream(FlowCenterDayOfEnum.values())
                    .filter(u -> dayOf.contains(u.getId()))
                    .map(u -> new SelectDTO2(u.getId(), u.getName())).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(dimensions)) {
            headers.addAll(Arrays.stream(FlowCenterDimensionEnum.values())
                    .filter(u -> dimensions.contains(u.getKey()))
                    .flatMap(u -> Stream.of(
                            new SelectDTO2(u.getField(), String.format("%sID", u.getName())),
                            new SelectDTO2(String.format("%sName", u.getKey()), String.format("%s", u.getName()))
                    )).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(downloads)) {
            headers.addAll(Arrays.stream(FlowCenterDataEnum.values())
                    .filter(u -> downloads.contains(u.getField()))
                    .map(u -> new SelectDTO2(u.getField(), u.getName())).collect(Collectors.toList()));
        }
        return headers;
    }


    /**
     * 获取list 数据
     *
     * @param listVO         条件
     * @param tTypeReference 类型
     * @param <T>            类型
     * @return 返回数据
     */
    @Override
    public <T> T getFlowData(FlowCenterReportListVO listVO, TypeReference<T> tTypeReference) {
        this.parseListVO(listVO);
        if (CollectionUtils.isNotEmpty(listVO.getDayOf())) {
            if (CollectionUtils.isNotEmpty(listVO.getDimensions())) {
                listVO.getDimensions().addAll(listVO.getDayOf());
            } else {
                listVO.setDimensions(listVO.getDayOf());
            }
        }
        listVO.setDimensions(listVO.getDimensions().stream().distinct().collect(Collectors.toList()));
        return PlanDiagnosisUtils.diagnosisList(JSONObject.parseObject(JSONObject.toJSONString(listVO)), tTypeReference);
    }


    /**
     * 获取错误信息数据
     *
     * @param listVO 筛选条件
     * @return 返回数据
     */
    @Override
    public FlowCenterDiagnosisDTO getDiagnosisData(FlowCenterReportListVO listVO) {
        this.parseListVO(listVO);
        return PlanDiagnosisUtils.diagnosisData(JSONObject.parseObject(JSONObject.toJSONString(listVO)), new TypeReference<>() {
        });
    }

    /**
     * 处理参数
     *
     * @param listVO 返回数据
     */
    private void parseListVO(FlowCenterReportListVO listVO) {
        int startHour = 0;
        int endHour = 23;
        if (null != listVO.getStartHour()) {
            startHour = listVO.getStartHour();
        }
        if (null != listVO.getEndHour()) {
            endHour = listVO.getEndHour();
        }
        listVO.setStartDate(DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getStart()), listVO.getTimezone(), startHour)));
        listVO.setEndDate(DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getEnd()), listVO.getTimezone(), endHour)));
//        //去除小时，天数据
//        if (CollectionUtils.isNotEmpty(listVO.getDimensions())) {
//            listVO.setDimensions(listVO.getDimensions().stream().filter(u -> !List.of("day", "hour").contains(u)).collect(Collectors.toList()));
//        }
    }


}
