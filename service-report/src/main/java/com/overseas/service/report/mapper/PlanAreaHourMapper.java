package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.CreativeHourReportListDTO;
import com.overseas.common.dto.report.DimensionTotalDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.PlanAreaHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PlanAreaHourMapper extends BaseMapper<PlanAreaHour> {

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_area_hour ${ew.customSqlSegment}")
    IPage<CreativeHourReportListDTO> listAreaReportPage(IPage<CreativeHourReportListDTO> page, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_area_hour ${ew.customSqlSegment}")
    List<CreativeHourReportListDTO> listAreaReport(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_area_hour ${ew.customSqlSegment}")
    CreativeHourReportListDTO getAreaReportTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 维度数据查询
     *
     * @param wrapper      筛选条件
     * @param whereWrapper 筛选外部条件
     * @return 返回数据
     */
    @Select("select * from( " +
            "SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_area_hour " +
            "LEFT JOIN iflytek_overseas_dsp.m_plan ON t_ads_dsp_flow_plan_area_hour.dim_plan_id = iflytek_overseas_dsp.m_plan.id " +
            "${ew.customSqlSegment}) " +
            "t ${where.customSqlSegment}")
    List<DimensionTotalDTO> dimensionTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,  @Param("where") Wrapper<?> whereWrapper);
}
