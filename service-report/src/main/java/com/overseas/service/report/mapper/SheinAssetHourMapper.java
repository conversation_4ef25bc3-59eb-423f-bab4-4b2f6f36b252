package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.AssetReportListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.SheinAssetHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
public interface SheinAssetHourMapper extends BaseMapper<SheinAssetHour> {


    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_shein_asset_hour ${ew.customSqlSegment}")
    IPage<AssetReportListDTO> getAssetReportList(IPage<?> iPage, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
