package com.overseas.service.report.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.overseas.common.dto.R;
import com.overseas.common.dto.report.openApi.ae.AeCostResultDTO;
import com.overseas.common.dto.report.openApi.ae.AeDailyCostListDTO;
import com.overseas.common.enums.MachineRoomEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.HttpUtils;
import com.overseas.common.utils.SignUtil;
import com.overseas.common.vo.report.AdxReportListVO;
import com.overseas.common.vo.report.openApi.DailyCostListVO;
import com.overseas.common.vo.report.openApi.RevenuePkgListVO;
import com.overseas.service.report.service.AdxReportService;
import com.overseas.service.report.service.AeDailyCostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "openApi-对外的接口")
@RestController
@RequestMapping("/report/v1/api")
@RequiredArgsConstructor
@Slf4j
public class OpenApiController {

    private final AeDailyCostService aeDailyCostService;

    private final AdxReportService adxReportService;

    /**
     * 用增消耗获取接口，暂无投放使用
     *
     * @param listVO 查询对象
     * @return 数据结果
     */
    @ApiOperation(value = "ae投放消耗数据", produces = "application/json")
    @PostMapping("/ae/dailyCost/list")
    public R getReportPageList(@Validated @RequestBody DailyCostListVO listVO) {
        this.checkAeToken(listVO);
        HashMap<String, List<AeDailyCostListDTO>> result = new HashMap<>();
        Map<String, AeDailyCostListDTO> allDataMap = new HashMap<>();
        List<MachineRoomEnum> enumList = MachineRoomEnum.listMachineRoomEnum();
        for (MachineRoomEnum machineRoomEnum : enumList) {
            String tempResult = HttpUtils.post(machineRoomEnum.getApiUrl() + "/report/v1/api/ae/daily/list",
                    JSONObject.parseObject(JSON.toJSONString(listVO)));
            AeCostResultDTO aeCostResultDTO = JSONObject.parseObject(tempResult, AeCostResultDTO.class);
            if (!aeCostResultDTO.getData().getDataList().isEmpty()) {
                aeCostResultDTO.getData().getDataList().forEach(aeDailyCostListDTO -> {
                    AeDailyCostListDTO temp = allDataMap.get(aeDailyCostListDTO.getCampaignId());
                    if (null != temp) {
                        temp.setImpressions(temp.getImpressions() + aeDailyCostListDTO.getImpressions());
                        temp.setClicks(temp.getClicks() + aeDailyCostListDTO.getClicks());
                        temp.setCost(temp.getCost() + aeDailyCostListDTO.getCost());
                        allDataMap.put(temp.getCampaignId(), temp);
                    } else {
                        allDataMap.put(aeDailyCostListDTO.getCampaignId(), aeDailyCostListDTO);
                    }
                });
            }
        }
        result.put("data_list", new ArrayList<>(allDataMap.values()));
        return R.data(result);
    }

    @ApiOperation(value = "ae投放消耗数据", produces = "application/json")
    @PostMapping("/ae/daily/list")
    public R getAeReportList(@Validated @RequestBody DailyCostListVO listVO) {
        this.checkAeToken(listVO);
        HashMap<String, List<AeDailyCostListDTO>> result = new HashMap<>();
        List<AeDailyCostListDTO> dataList = this.aeDailyCostService.listAeCampaignDailyCost(listVO);
        result.put("data_list", dataList);
        return R.data(result);
    }

    @ApiOperation(value = "获取指定时间段adx的数据", produces = "application/json")
    @PostMapping("/adxReport/list")
    public R getAdxReportList(@Validated @RequestBody AdxReportListVO listVO) {
        return R.data(this.adxReportService.getAdxReport(listVO.getStartDate(), listVO.getEndDate(),
                listVO.getAdxIds(), listVO.getTimeZone()));
    }

    @ApiOperation(value = "获取小米对账报表数据", produces = "application/json")
    @GetMapping("/cost/list")
    public R getXiaoMiReport(@RequestParam("token") String token,
                             @RequestParam("startDate") String startDate,
                             @RequestParam("endDate") String endDate,
                             @RequestParam("adxId") Integer adxId) {
        return R.data(this.adxReportService.listAdxCostByDate(token, startDate, endDate, adxId));
    }

    @PostMapping("/revenue/pkg/list")
    public R revenuePkgList(@Validated @RequestBody RevenuePkgListVO listVO) {
        return R.data(this.adxReportService.revenuePkg(listVO));
    }


    private void checkAeToken(DailyCostListVO listVO) {
        try {
            log.info("ae params：" + JSONObject.toJSONString(listVO));
            String appSecret = "HZofC4e3n7BGR4dMabgmmFKrSB3oMqFYNVkvCR1lG9or9YJcQEawuW3wILzQi845";
            String sign = SignUtil.signCostRequest(listVO.getBizdate(), listVO.getCampaignIds(),
                    listVO.getChannel(), listVO.getTimestamp(), appSecret);
            if (!listVO.getSign().equals(sign)) {
                throw new CustomException(1003, "签名参数校验错误");
            }
        } catch (Exception e) {
            throw new CustomException(1002, "密钥解析异常");
        }
    }
}
