package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.*;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.PlanHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PlanGroupHourMapper extends BaseMapper<PlanHour> {

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_shein_plan_group_hour ${ew.customSqlSegment}")
    IPage<CreativeHourReportListDTO> getReportPageList(IPage<CreativeHourReportListDTO> page,
                                                       @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_shein_plan_group_hour ${ew.customSqlSegment}")
    CreativeHourReportListDTO getTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_shein_plan_group_hour ${ew.customSqlSegment}")
    List<CreativeHourReportListDTO> getReportList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
