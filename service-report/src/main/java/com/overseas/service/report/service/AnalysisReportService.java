package com.overseas.service.report.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.report.AnalysisReportListDTO;
import com.overseas.common.entity.User;
import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.report.AnalysisReportChartVO;
import com.overseas.common.vo.report.AnalysisReportListVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public interface AnalysisReportService {

    MultiIndexChartDTO chartAnalysisReport(AnalysisReportChartVO chartVO, User user);

    PageUtils<AnalysisReportListDTO> listAnalysisReport(AnalysisReportListVO listVO);

    AnalysisReportListDTO getAnalysisReportTotal(AnalysisReportListVO listVO, QueryWrapper<?> queryWrapper,
                                                 String finalSelectSql, String filterSql);

    void exportReport(AnalysisReportListVO listVO, HttpServletResponse response) throws IOException;

    ResultStatusEnum exportReportToPath(AnalysisReportListVO listVO);
}
