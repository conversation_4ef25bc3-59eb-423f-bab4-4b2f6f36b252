package com.overseas.service.report.enums;

import com.overseas.common.enums.ICommonEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum DateTypeEnum implements ICommonEnum {

    DEFAULT(0, "未定义"),
    DAY(1, "天"),
    HOUR(2, "小时");

    private final Integer id;

    private final String name;

    public static DateTypeEnum get(Integer id) {
        for (DateTypeEnum dateTypeEnum : values()) {
            if (dateTypeEnum.getId().equals(id)) {
                return dateTypeEnum;
            }
        }
        return DEFAULT;
    }
}
