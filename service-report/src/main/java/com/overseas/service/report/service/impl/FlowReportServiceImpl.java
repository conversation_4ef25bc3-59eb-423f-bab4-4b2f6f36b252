package com.overseas.service.report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.chart.LegendDTO;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.chart.SeriesDTO;
import com.overseas.common.dto.report.FlowReportListDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.enums.market.PlanFlowTypeEnum;
import com.overseas.common.enums.market.bahaviorApp.BehaviorAppTypeEnum;
import com.overseas.common.enums.report.ReportFlowDataTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.plan.direct.AdxSelectGetVO;
import com.overseas.common.vo.market.plan.direct.EpSelectGetVO;
import com.overseas.common.vo.report.flow.FlowReportListVO;
import com.overseas.common.vo.report.flow.FlowReportSizeSelectVO;
import com.overseas.common.vo.report.flow.FlowReportTopListVO;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.service.report.entity.FlowHour;
import com.overseas.service.report.feign.FgMarketService;
import com.overseas.service.report.feign.FgSystemService;
import com.overseas.service.report.mapper.FlowHourMapper;
import com.overseas.service.report.service.FlowReportService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FlowReportServiceImpl extends ServiceImpl<FlowHourMapper, FlowHour> implements FlowReportService {

    private final FgMarketService fgMarketService;

    private final FgSystemService fgSystemService;

    private final Map<String, String> dimensionMap = new HashMap<>() {{
        put("reportDate", "`day`");
        put("areaName", "`area_id`");
        put("adxName", "`adx_id`");
        put("epName", "`ep_id`");
        put("pkg", "`pkg`");
        put("flowTypeName", "`flow_type`");
        put("slotTypeName", "`slot_type`");
        put("imageSize", "`image_size`");
        put("videoSize", "`video_size`");
        put("osName", "`os`");
        put("adxSlotId", "`adx_slot_id`");
    }};
    private final Map<String, String> dimensionGroupMap = new HashMap<>() {{
        put("reportDate", "`dim_day` AS `day`");
        put("areaName", "`dim_area_id` AS `area_id`");
        put("adxName", "`dim_adx_id` AS `adx_id`");
        put("epName", "`dim_ep_id` AS `ep_id`");
        put("pkg", "`dim_pkg` AS `pkg`");
        put("flowTypeName", "`dim_flow_type` AS `flow_type`");
        put("slotTypeName", "`dim_slot_type` AS `slot_type`");
        put("imageSize", "`dim_image_size` AS `image_size`");
        put("videoSize", "`dim_video_size` AS `video_size`");
        put("osName", "`dim_os` AS `os`");
        put("adxSlotId", "`dim_adx_slot_id` AS `adx_slot_id`");
    }};

    private final String selectFields = "CEILING(SUM(`idx_req` * (100 / `dim_flow_ratio`))) AS `req`," +
            "SUM(`idx_bid_success`) as `bid_success`," +
            "ROUND(SUM(`idx_bid_success`) / SUM(`idx_req` * (100 / `dim_flow_ratio`)) * 100,2) AS bid_rate";

    @Override
    public PageUtils<FlowReportListDTO> listFlowReport(FlowReportListVO listVO) {
        IPage<FlowReportListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        List<String> dimensions = listVO.getDimensions().stream()
                .map(this::getRealDimension).collect(Collectors.toList());
        List<String> dimensionFields = listVO.getDimensions().stream()
                .map(this::getDimensionSelectField).collect(Collectors.toList());
        
        QueryWrapper<FlowHour> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(StringUtils.join(dimensionFields, ",") + "," + this.selectFields)
                .groupBy(StringUtils.join(dimensions, ","))
                .orderBy(StringUtils.isNotBlank(listVO.getSortField()),
                        listVO.getSortType().equals("asc"), HumpLineUtils.humpToLine2(listVO.getSortField()))
                .lambda().ne(FlowHour::getFlowRatio, 0)
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAdxId()), FlowHour::getAdxId, listVO.getAdxId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getEpId()), FlowHour::getEpId, listVO.getEpId())
                .like(StringUtils.isNotBlank(listVO.getPkg()), FlowHour::getPkg, listVO.getPkg())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAreaId()), FlowHour::getAreaId, listVO.getAreaId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getSlotType()), FlowHour::getSlotType, listVO.getSlotType())
                .eq(ObjectUtils.isNotAll(listVO.getFlowType()), FlowHour::getFlowType, listVO.getFlowType())
                .eq(StringUtils.isNotBlank(listVO.getImageSize()), FlowHour::getImageSize, listVO.getImageSize())
                .eq(StringUtils.isNotBlank(listVO.getVideoSize()), FlowHour::getVideoSize, listVO.getVideoSize())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getOs()), FlowHour::getOs, listVO.getOs())
                .in(CollectionUtils.isNotEmpty(listVO.getPkgs()), FlowHour::getPkg, listVO.getPkgs())
                .in(CollectionUtils.isNotEmpty(listVO.getAdxSlotIds()), FlowHour::getAdxSlotId, listVO.getAdxSlotIds())
                .between(FlowHour::getDay,
                        DateUtils.string2Long(listVO.getStartDate()), DateUtils.string2Long(listVO.getEndDate()));

        List<String> excludeFields = List.of("imageSize", "videoSize");
        List<String> dimensions1 = listVO.getDimensions().stream()
                .filter(dimension -> !excludeFields.contains(dimension))
                .map(this::getRealDimension).collect(Collectors.toList());
        String joinFields = this.getJoinFields(dimensions1);
        List<String> dimensionFields1 = listVO.getDimensions().stream()
                .filter(dimension -> !excludeFields.contains(dimension))
                .map(this::getDimensionSelectField).collect(Collectors.toList());
        QueryWrapper<FlowHour> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.select(StringUtils.join(dimensionFields1, ",")
                        + ",CEILING(SUM(`idx_req` * (100 / `dim_flow_ratio`))) AS `req`")
                .groupBy(StringUtils.join(dimensions1, ","))
                .orderBy(StringUtils.isNotBlank(listVO.getSortField()),
                        listVO.getSortType().equals("asc"), HumpLineUtils.humpToLine2(listVO.getSortField()))
                .lambda().ne(FlowHour::getFlowRatio, 0)
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAdxId()), FlowHour::getAdxId, listVO.getAdxId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getEpId()), FlowHour::getEpId, listVO.getEpId())
                .like(StringUtils.isNotBlank(listVO.getPkg()), FlowHour::getPkg, listVO.getPkg())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAreaId()), FlowHour::getAreaId, listVO.getAreaId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getSlotType()), FlowHour::getSlotType, listVO.getSlotType())
                .eq(ObjectUtils.isNotAll(listVO.getFlowType()), FlowHour::getFlowType, listVO.getFlowType())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getOs()), FlowHour::getOs, listVO.getOs())
                .in(CollectionUtils.isNotEmpty(listVO.getPkgs()), FlowHour::getPkg, listVO.getPkgs())
                .in(CollectionUtils.isNotEmpty(listVO.getAdxSlotIds()), FlowHour::getAdxSlotId, listVO.getAdxSlotIds())
                .between(FlowHour::getDay,
                        DateUtils.string2Long(listVO.getStartDate()), DateUtils.string2Long(listVO.getEndDate()));

        QueryWrapper<FlowHour> queryWrapperTotal = new QueryWrapper<>();
        queryWrapperTotal.select(this.selectFields)
                .lambda().ne(FlowHour::getFlowRatio, 0)
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAdxId()), FlowHour::getAdxId, listVO.getAdxId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getEpId()), FlowHour::getEpId, listVO.getEpId())
                .like(StringUtils.isNotBlank(listVO.getPkg()), FlowHour::getPkg, listVO.getPkg())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAreaId()), FlowHour::getAreaId, listVO.getAreaId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getSlotType()), FlowHour::getSlotType, listVO.getSlotType())
                .eq(ObjectUtils.isNotAll(listVO.getFlowType()), FlowHour::getFlowType, listVO.getFlowType())
                .eq(StringUtils.isNotBlank(listVO.getImageSize()), FlowHour::getImageSize, listVO.getImageSize())
                .eq(StringUtils.isNotBlank(listVO.getVideoSize()), FlowHour::getVideoSize, listVO.getVideoSize())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getOs()), FlowHour::getOs, listVO.getOs())
                .in(CollectionUtils.isNotEmpty(listVO.getPkgs()), FlowHour::getPkg, listVO.getPkgs())
                .in(CollectionUtils.isNotEmpty(listVO.getAdxSlotIds()), FlowHour::getAdxSlotId, listVO.getAdxSlotIds())
                .between(FlowHour::getDay,
                        DateUtils.string2Long(listVO.getStartDate()), DateUtils.string2Long(listVO.getEndDate()));

        // 获取分页数据
        boolean type = dimensions.contains("`image_size`") || dimensions.contains("`video_size`");
        IPage<FlowReportListDTO> pageData = type
                ? this.baseMapper.getFlowSearchReportMaterialPageList(page, queryWrapper, queryWrapper1, joinFields)
                : this.baseMapper.getFlowSearchReportPageList(page, queryWrapper);
        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }
        // 获取汇总数据
        FlowReportListDTO totalData = type
                ? this.baseMapper.getFlowSearchReportMaterialTotal(queryWrapperTotal)
                : this.baseMapper.getFlowSearchReportTotal(queryWrapperTotal);

        if (null == totalData) {
            totalData = new FlowReportListDTO();
        }
        totalData.setReportDate(ConstantUtils.PLACEHOLDER_2);
        totalData.setAreaName(ConstantUtils.PLACEHOLDER_2);
        totalData.setAdxId(0L);
        totalData.setAdxName(ConstantUtils.PLACEHOLDER_2);
        totalData.setEpId(0L);
        totalData.setEpName(ConstantUtils.PLACEHOLDER_2);
        totalData.setSlotTypeName(ConstantUtils.PLACEHOLDER_2);
        totalData.setFlowTypeName(ConstantUtils.PLACEHOLDER_2);
        totalData.setPkg(ConstantUtils.PLACEHOLDER_2);
        totalData.setImageSize(ConstantUtils.PLACEHOLDER_2);
        totalData.setVideoSize(ConstantUtils.PLACEHOLDER_2);
        totalData.setOsName(ConstantUtils.PLACEHOLDER_2);
        totalData.setAdxSlotId(ConstantUtils.PLACEHOLDER_2);
        totalData.setReqRatio(100D);
        Long req = totalData.getReq();

        // 获取广告形式
        List<SelectDTO> slotTypes = this.fgMarketService.selectSlotType().getData();
        Map<Long, String> slotTypeMap = slotTypes.stream()
                .collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
        Map<Long, String> flowTypeMap = ICommonEnum.list(PlanFlowTypeEnum.class).stream()
                .collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
        // 获取ADX
        List<SelectDTO> adxs = this.fgMarketService.selectAdx().getData();
        Map<Long, String> adxMap = adxs.stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
        // 获取EP
        EpSelectGetVO getVO = new EpSelectGetVO();
        getVO.setAdxIds(adxs.stream().map(SelectDTO::getId).collect(Collectors.toList()));
        Map<Long, String> epMap = this.fgMarketService.selectEp(getVO).getData()
                .stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
        // 获取地区名称
        Map<Long, String> countryMap = this.fgSystemService.selectCountry(new AreaCountrySelectVO()).getData()
                .stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName, (ov, nv) -> ov));
        // 填充名称
        pageData.getRecords().forEach(flowReportListDTO -> {
            flowReportListDTO.setReportDate(DateUtils.long2String(flowReportListDTO.getDay()));
            flowReportListDTO.setAdxName(adxMap.getOrDefault(flowReportListDTO.getAdxId(), ""));
            flowReportListDTO.setEpName(epMap.getOrDefault(flowReportListDTO.getEpId(), ""));
            flowReportListDTO.setSlotTypeName(
                    slotTypeMap.getOrDefault(flowReportListDTO.getSlotType(), ""));
            flowReportListDTO.setFlowTypeName(
                    flowTypeMap.getOrDefault(flowReportListDTO.getFlowType(), ""));
            flowReportListDTO.setAreaName(
                    countryMap.getOrDefault(flowReportListDTO.getAreaId(), "其他"));
            BehaviorAppTypeEnum behaviorAppTypeEnum = ICommonEnum.get(flowReportListDTO.getOs(),
                    BehaviorAppTypeEnum.class);
            flowReportListDTO.setOsName(null == behaviorAppTypeEnum
                    ? ConstantUtils.UNKNOWN : behaviorAppTypeEnum.getName());
            if (!type) {
                flowReportListDTO.setReqRatio(DoubleUtils.getRate(BigDecimal.valueOf(flowReportListDTO.getReq()),
                        BigDecimal.valueOf(req)));
            }
        });
        return new PageUtils<>(pageData, totalData);
    }

    @Override
    public void exportFlowReport(FlowReportListVO listVO, HttpServletResponse response) throws IOException {
        listVO.setPage(1L);
        listVO.setPageNum(50000L);
        if (StringUtils.isBlank(listVO.getSortField())) {
            listVO.setSortField("req");
            listVO.setSortType("desc");
        }
        PageUtils<FlowReportListDTO> result = this.listFlowReport(listVO);
        String fileName = "流量查询报表_";
        fileName += listVO.getStartDate().equals(listVO.getEndDate())
                ? listVO.getStartDate() : listVO.getStartDate() + "_" + listVO.getEndDate();
        List<String> headers = listVO.getDimensions();
        headers.addAll(List.of("req", "bidSuccess", "bidRate"));
        ExcelUtils.download(response, fileName, FlowReportListDTO.class, result.getData(), headers);
    }

    @Override
    public ResultStatusEnum exportFlowReportToPath(FlowReportListVO listVO) {

        List<FlowReportListDTO> reportList = this.listFlowReport(listVO).getData();
        if (reportList.isEmpty()) {
            throw new CustomException(4000, "无可下载数据");
        }
        try {
            ExcelUtils.download(
                    UploadUtils.getUploadPath(listVO.getFilePath()),
                    "报表数据",
                    FlowReportListDTO.class,
                    reportList, new ArrayList<>() {{
                        addAll(listVO.getDimensions());
                        addAll(List.of("req", "bidSuccess", "bidRate"));
                    }}
            );
            return ResultStatusEnum.SUCCESS;
        } catch (Exception exception) {
            throw new CustomException(4000, exception.getMessage());
        }
    }

    @Override
    public Map<String, List<SelectDTO3>> selectSizeByDate(FlowReportSizeSelectVO selectVO) {

        Long startDate = DateUtils.string2Long(selectVO.getStartDate());
        Long endDate = DateUtils.string2Long(selectVO.getEndDate());
        List<SelectDTO3> imageSizes = this.baseMapper.getSizeSelect(new QueryWrapper<FlowHour>()
                .select("DISTINCT dim_image_size AS `key`,dim_image_size AS title,sum(idx_req) as `req`")
                .orderByDesc("`req`").last("LIMIT 200")
                .lambda().between(FlowHour::getDay, startDate, endDate)
                .ne(FlowHour::getImageSize, "")
                .groupBy(FlowHour::getImageSize)
        );

        List<SelectDTO3> videoSizes = this.baseMapper.getSizeSelect(new QueryWrapper<FlowHour>()
                .select("DISTINCT dim_video_size AS `key`,dim_video_size AS title,sum(idx_req) as `req`")
                .orderByDesc("`req`").last("LIMIT 200")
                .lambda().between(FlowHour::getDay, startDate, endDate)
                .ne(FlowHour::getVideoSize, "")
                .groupBy(FlowHour::getVideoSize)
        );
        Map<String, List<SelectDTO3>> result = new HashMap<>();
        result.put("imageSizes", imageSizes);
        result.put("videoSizes", videoSizes);
        return result;
    }

    @Override
    public MultiIndexChartDTO getFlowSearchReportChart(FlowReportListVO listVO) {

        if (StringUtils.isBlank(listVO.getIndicator())) {
            throw new CustomException("指标维度不能为空");
        }

        ReportFlowDataTypeEnum dataTypeEnum = ICommonEnum.get(listVO.getDataType(), ReportFlowDataTypeEnum.class);
        if (null == dataTypeEnum) {
            throw new CustomException("折线数据类型异常");
        }

        String groupField = listVO.getStartDate().equals(listVO.getEndDate()) ? "dim_hour" : "dim_day";
        String dataTypeField = dataTypeEnum.getGroupField();
        String fields = getFields(listVO, dataTypeField);

        // 获取分页数据
        QueryWrapper<FlowHour> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(fields)
                .groupBy(groupField + "," + dataTypeField).orderByAsc(groupField)
                .lambda().ne(FlowHour::getFlowRatio, 0)
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAdxId()), FlowHour::getAdxId, listVO.getAdxId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getEpId()), FlowHour::getEpId, listVO.getEpId())
                .like(StringUtils.isNotBlank(listVO.getPkg()), FlowHour::getPkg, listVO.getPkg())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAreaId()), FlowHour::getAreaId, listVO.getAreaId())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getSlotType()), FlowHour::getSlotType, listVO.getSlotType())
                .eq(ObjectUtils.isNotAll(listVO.getFlowType()), FlowHour::getFlowType, listVO.getFlowType())
                .eq(StringUtils.isNotBlank(listVO.getImageSize()), FlowHour::getImageSize, listVO.getImageSize())
                .eq(StringUtils.isNotBlank(listVO.getVideoSize()), FlowHour::getVideoSize, listVO.getVideoSize())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getOs()), FlowHour::getOs, listVO.getOs())
                .in(CollectionUtils.isNotEmpty(listVO.getPkgs()), FlowHour::getPkg, listVO.getPkgs())
                .in(CollectionUtils.isNotEmpty(listVO.getAdxSlotIds()), FlowHour::getAdxSlotId, listVO.getAdxSlotIds())
                .in(null != listVO.getAreaIds() && !listVO.getAreaIds().isEmpty(),
                        FlowHour::getAreaId, listVO.getAreaIds())
                .between(FlowHour::getDay,
                        DateUtils.string2Long(listVO.getStartDate()), DateUtils.string2Long(listVO.getEndDate()));
        // 如果查询条件中包含了视频和图片尺寸，则查询原流量报表
        List<FlowReportListDTO> listData = StringUtils.isNotBlank(listVO.getImageSize())
                || StringUtils.isNotBlank(listVO.getVideoSize())
                ? this.baseMapper.getFlowSearchReportMaterialList(queryWrapper)
                : this.baseMapper.getFlowSearchReportList(queryWrapper);

        if (listData.isEmpty()) {
            return new MultiIndexChartDTO();
        }

        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        List<String> dates = new ArrayList<>(); // 日期
        Map<String, Map<String, Object>> adxDateMap = new HashMap<>(); // 指标的数据

        for (FlowReportListDTO reportListDTO : listData) {
            reportListDTO.setQps(listVO.getStartDate().equals(listVO.getEndDate())
                    ? reportListDTO.getReq() / 3600
                    : reportListDTO.getReq() / (3600 * 24));
            // 填充时间轴
            switch (groupField) {
                case "dim_day":
                    dates.add(DateUtils.long2String(reportListDTO.getDay()));
                    break;
                case "dim_hour":
                    dates.add(DateUtils.hour2Name(reportListDTO.getHour()));
                    break;
            }
        }
        // 去重时间
        dates = dates.stream().distinct().collect(Collectors.toList());

        // 获取列表map
        Map<String, List<FlowReportListDTO>> listMap = listData.stream()
                .collect(Collectors.groupingBy(FlowReportListDTO::getDataTypeKey));

        // 填充数据轴
        for (Map.Entry<String, List<FlowReportListDTO>> entry : listMap.entrySet()) {
            // 初始化ADX映射Map
            for (FlowReportListDTO flowReportListDTO : entry.getValue()) {
                Object value = this.getObject2BigDecimal(flowReportListDTO, listVO.getIndicator());
                String dataTypeKey = entry.getKey();
                String date = groupField.equals("dim_day") ? DateUtils.long2String(flowReportListDTO.getDay()) :
                        DateUtils.hour2Name(flowReportListDTO.getHour());
                if (adxDateMap.get(dataTypeKey) == null) {
                    Map<String, Object> dateMap = new LinkedHashMap<>();
                    dates.forEach(u -> dateMap.put(u, date.equals(u) ? value : 0));
                    adxDateMap.put(dataTypeKey, dateMap);
                } else {
                    adxDateMap.get(dataTypeKey).put(date, value);
                }
            }
        }

        Map<String, String> dataTypeMap = this.getDataNameMap(dataTypeEnum, listData);
        // 补充数据
        multiIndexChartDTO.setXAxis(List.of(new LegendDTO(dates)));
        multiIndexChartDTO.setLegend(new LegendDTO(new ArrayList<>(adxDateMap.keySet())
                .stream().map(adxId -> dataTypeMap.getOrDefault(adxId, "未知"))
                .collect(Collectors.toList())));
        List<SeriesDTO> series = new ArrayList<>();
        adxDateMap.forEach(
                (key, value) -> series.add(
                        new SeriesDTO(dataTypeMap.getOrDefault(key, "未知"), "line",
                                new ArrayList<>(value.values()))
                )
        );
        multiIndexChartDTO.setSeries(series);
        return multiIndexChartDTO;
    }

    private String getFields(FlowReportListVO listVO, String dataTypeField) {
        String fields = listVO.getStartDate().equals(listVO.getEndDate()) ? "dim_hour AS `hour`" : "dim_day AS `day`";
        // 根据chart数据类型组装数据聚合key
        fields += "," + dataTypeField + " AS data_type_key," + this.selectFields;
        return fields;
    }

    @Override
    public PageUtils<FlowReportListDTO> getFlowSearchTopList(FlowReportTopListVO listVO) {
        String selectFields = "";
        switch (listVO.getType()) {
            case "pkg":
                selectFields += "dim_pkg AS `pkg`,";
                break;
            case "imageSize":
                selectFields += "dim_image_size AS `image_size`,";
                break;
            case "videoSize":
                selectFields += "dim_video_size AS `video_size`,";
                break;
        }
        selectFields += this.selectFields;
        QueryWrapper<FlowHour> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(selectFields).ne("dim_flow_ratio", 0)
                // 日期
                .eq("dim_day", DateUtils.string2Long(listVO.getReportDate()))
                .in(CollectionUtils.isNotEmpty(listVO.getHours()), "dim_hour", listVO.getHours())
                // 必填字段查询
                .eq("dim_area_id", listVO.getAreaId())
                .eq("dim_adx_id", listVO.getAdxId())
                .eq("dim_slot_type", listVO.getSlotType())
                // 详情查询
                .eq(StringUtils.isNotBlank(listVO.getPkg()), "dim_pkg", listVO.getPkg())
                .eq(StringUtils.isNotBlank(listVO.getImageSize()), "dim_image_size", listVO.getImageSize())
                .eq(StringUtils.isNotBlank(listVO.getVideoSize()), "dim_video_size", listVO.getVideoSize())
                .groupBy("dim_" + HumpLineUtils.humpToLine2(listVO.getType()))
                .orderByDesc("req");
        // 获取前100条数据
        List<FlowReportListDTO> listData = listVO.getType().equals("pkg")
                && StringUtils.isBlank(listVO.getImageSize())
                && StringUtils.isBlank(listVO.getVideoSize())
                ? this.baseMapper.listFlowHourTop100Report(queryWrapper)
                : this.baseMapper.listFlowHourTop100ReportMaterial(queryWrapper);

        return new PageUtils<>(listData, listVO.getPage(), listVO.getPageNum());
    }

    @Override
    public List<SelectDTO3> selectCountryByReq(FlowReportSizeSelectVO selectVO) {
        Long startDate = DateUtils.string2Long(selectVO.getStartDate());
        Long endDate = DateUtils.string2Long(selectVO.getEndDate());
        List<SelectDTO3> countries = this.baseMapper.getSizeSelect(new QueryWrapper<FlowHour>()
                .select("`dim_area_id` AS `key`,sum(idx_req) as `req`")
                .orderByDesc("`req`")
                .lambda().between(FlowHour::getDay, startDate, endDate).groupBy(FlowHour::getAreaId)
        );
        if (!countries.isEmpty()) {
            // 获取地区名称
            Map<Long, String> countryMap = this.fgSystemService.selectCountry(new AreaCountrySelectVO()).getData()
                    .stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName, (ov, nv) -> ov));
            countries.forEach(country -> country.setTitle(
                    countryMap.getOrDefault(Long.valueOf(country.getKey()), "未知"))
            );
        }
        return countries;
    }

    private String getRealDimension(String dimension) {
        return this.dimensionMap.get(dimension);
    }

    private String getDimensionSelectField(String dimension) {
        return this.dimensionGroupMap.get(dimension);
    }

    private BigDecimal getObject2BigDecimal(Object object, String indicator) {
        Object obj = ObjectUtils.getObjectValue(object, indicator);
        return ObjectUtils.isNullOrZero(obj) ? new BigDecimal(0) : new BigDecimal(obj.toString());
    }

    private Map<String, String> getDataNameMap(ReportFlowDataTypeEnum dataTypeEnum,
                                               List<FlowReportListDTO> reportData) {
        List<String> dataTypeKeys = reportData.stream().map(FlowReportListDTO::getDataTypeKey)
                .distinct().collect(Collectors.toList());
        switch (dataTypeEnum) {
            case COUNTRY:
                AreaCountrySelectVO selectVO = new AreaCountrySelectVO();
                selectVO.setCountryIds(dataTypeKeys.stream().map(Long::parseLong).collect(Collectors.toList()));
                return this.fgSystemService.selectCountry(selectVO).getData().stream()
                        .collect(Collectors.toMap(u -> String.valueOf(u.getId()), SelectDTO::getName, (ov, nv) -> ov));
            case ADX:
                AdxSelectGetVO getVO = new AdxSelectGetVO();
                getVO.setAdxIds(dataTypeKeys.stream().map(Long::parseLong).collect(Collectors.toList()));
                return this.fgMarketService.selectAdx(getVO).getData().stream()
                        .collect(Collectors.toMap(u -> String.valueOf(u.getId()), SelectDTO::getName));
            case EP:
                EpSelectGetVO epSelectGetVO = new EpSelectGetVO();
                epSelectGetVO.setEpIds(dataTypeKeys.stream().map(Long::parseLong).collect(Collectors.toList()));
                return this.fgMarketService.selectEp(epSelectGetVO).getData().stream()
                        .collect(Collectors.toMap(u -> String.valueOf(u.getId()), SelectDTO::getName));
        }
        return new HashMap<>();
    }

    private String getJoinFields(List<String> fields) {
        List<String> joinFields = fields.stream().map(field -> "a." + field + " = b." + field)
                .collect(Collectors.toList());
        return String.join(" AND ", joinFields);
    }
}
