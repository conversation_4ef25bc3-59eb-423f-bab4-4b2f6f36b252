package com.overseas.service.report.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.chart.LegendDTO;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.chart.SeriesDTO;
import com.overseas.common.dto.report.rta.RtaReportListDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexContentDTO;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.plan.direct.RtaSelectGetVO;
import com.overseas.common.vo.report.rta.RtaReportBaseVO;
import com.overseas.common.vo.report.rta.RtaReportListExportVO;
import com.overseas.common.vo.report.rta.RtaReportListVO;
import com.overseas.common.vo.report.rta.RtaReportTrendyVO;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.service.report.feign.FgMarketService;
import com.overseas.service.report.feign.FgSystemService;
import com.overseas.service.report.mapper.RtaReportMapper;
import com.overseas.service.report.service.RtaReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Slf4j
@Service
public class RtaReportServiceImpl implements RtaReportService {

    private final FgMarketService fgMarketService;

    private final FgSystemService fgSystemService;

    private final RtaReportMapper rtaReportMapper;

    public interface Dimension {
        String MASTER_ID = "masterId";
        String COUNTRY_ID = "countryId";
        String RTA_STRATEGY_ID = "rtaStrategyId";
        String RTA_GROUP_ID = "rtaGroupId";
        String ALL = "all";
    }

    @Override
    public PageUtils<?> passList(RtaReportListVO listVO) {
        IPage<RtaReportListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        QueryWrapper<?> queryWrapper = getQueryWrapper(listVO);
        if (null == queryWrapper) {
            return new PageUtils<>(iPage);
        }
        List<String> dimensions = listVO.getDimensions().stream().map(u -> u.replace("Name", "Id")).distinct().collect(Collectors.toList());
        queryWrapper.select(getSelect().stream().map(CustomIndexContentDTO::getRule).collect(Collectors.joining(",")) + ", master_id as master_id, m_dsp_rta_report.id");
        RtaReportListDTO total = rtaReportMapper.total(queryWrapper);
        iPage = rtaReportMapper.list(iPage,
                queryWrapper.groupBy(dimensions.stream().map(this::getTableField).distinct().collect(Collectors.toList())),
                new QueryWrapper<>().orderBy(StringUtils.isNotBlank(listVO.getSortField()), "asc".equalsIgnoreCase(listVO.getSortType()),
                                String.format("`%s`", this.getTableField(listVO.getSortField()).replace("idx_", "").replace("dim_", "")))
                        .orderByDesc("id"));
        if (iPage.getRecords().isEmpty()) {
            return new PageUtils<>(iPage);
        }
        Map<Long, String> rtaGroupMap = dimensions.contains(Dimension.RTA_GROUP_ID) ? this.getFieldMapByList(iPage.getRecords(), Dimension.RTA_GROUP_ID) : Map.of();
        Map<Long, String> rtaStrategyMap = dimensions.contains(Dimension.RTA_STRATEGY_ID) ? this.getFieldMapByList(iPage.getRecords(), Dimension.RTA_STRATEGY_ID) : Map.of();
        Map<Long, String> countryMap = dimensions.contains(Dimension.COUNTRY_ID) ? this.getFieldMapByList(iPage.getRecords(), Dimension.COUNTRY_ID) : Map.of();
        Map<Long, String> masterMap = this.getFieldMapByList(iPage.getRecords(), Dimension.MASTER_ID);
        iPage.getRecords().forEach(u -> {
            u.setRtaGroupName(rtaGroupMap.getOrDefault(u.getRtaGroupId(), ConstantUtils.PLACEHOLDER));
            u.setRtaStrategyName(rtaStrategyMap.getOrDefault(u.getRtaStrategyId(), ConstantUtils.PLACEHOLDER));
            u.setCountryName(countryMap.getOrDefault(u.getCountryId(), ConstantUtils.PLACEHOLDER));
            u.setMasterName(masterMap.getOrDefault(u.getMasterId(), ConstantUtils.PLACEHOLDER));
            if (ObjectUtils.isNotNullOrZero(u.getDay())) {
                u.setDayName(DateUtils.long2String(u.getDay()));
            }
            if (ObjectUtils.isNotNullOrZero(u.getHour())) {
                u.setHourName(DateUtils.hour2Name(u.getHour()));
            }
        });
        IPage<RtaReportListDTO> finalIPage = iPage;
        return new PageUtils<>(new ArrayList<>() {{
            add(total);
            addAll(finalIPage.getRecords());
        }}, iPage.getTotal());
    }

    @Override
    public MultiIndexChartDTO passTrendy(RtaReportTrendyVO trendyVO) {
        QueryWrapper<?> queryWrapper = getQueryWrapper(trendyVO);
        if (null == queryWrapper) {
            return new MultiIndexChartDTO();
        }
        Map<String, CustomIndexContentDTO> selectMap = getSelect().stream().collect(Collectors.toMap(CustomIndexContentDTO::getKey, Function.identity()));
        String dimension = trendyVO.getDimension().replace("Name", "Id");
        //获取数据字段
        queryWrapper.groupBy(Stream.of(trendyVO.getDayOf(), dimension).filter(u -> !"all".equals(u)).map(this::getTableField).collect(Collectors.toList()))
                .select(selectMap.values().stream().map(CustomIndexContentDTO::getRule).collect(Collectors.joining(",")));
        List<JSONObject> list = this.rtaReportMapper.list2(queryWrapper)
                .stream().map(u -> JSONObject.parseObject(JSONObject.toJSONString(u)))
                .collect(Collectors.toList());
        Map<Long, List<JSONObject>> result = list.stream().collect(Collectors.groupingBy(v -> {
            if (Dimension.ALL.equals(trendyVO.getDimension())) {
                return 0L;
            }
            return v.getLong(dimension);
        }));
        List<String> dateRange;
        switch (trendyVO.getDayOf()) {
            case "day":
                dateRange = DateUtils.getBetweenDate(trendyVO.getStartDate(), trendyVO.getEndDate(), DateUtils.DATE_PATTERN);
                break;
            case "hour":
                dateRange = DateUtils.getBetweenHour(trendyVO.getStartDate());
                break;
            default:
                throw new CustomException("时间趋势类型不合法");
        }
        List<SeriesDTO> series = new ArrayList<>();
        Map<Long, String> nameMap = this.getFieldMap(new ArrayList<>(result.keySet()), dimension);
        result.forEach((key, val) -> {
            Map<String, JSONObject> valMap = val.stream().collect(Collectors.toMap(u -> {
                if ("day".equals(trendyVO.getDayOf())) {
                    return DateUtils.long2String(u.getLong("day"));
                } else {
                    return DateUtils.formatHour(u.getInteger("hour"));
                }
            }, Function.identity()));
            trendyVO.getCustomIndexes().forEach(customIndex -> {
                CustomIndexContentDTO content = selectMap.get(customIndex);
                series.add(new SeriesDTO(String.format("%s-%s", nameMap.get(key), content.getTitle()),
                        "line",
                        dateRange.stream().map(ran -> {
                            if (valMap.containsKey(ran)) {
                                return valMap.get(ran).get(content.getKey());
                            }
                            return 0L;
                        }).collect(Collectors.toList())));
            });
        });
        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        multiIndexChartDTO.setXAxis(List.of(new LegendDTO(dateRange)));
        multiIndexChartDTO.setSeries(series);
        multiIndexChartDTO.setLegend(new LegendDTO(multiIndexChartDTO.getSeries().stream().map(SeriesDTO::getName).collect(Collectors.toList())));
        return multiIndexChartDTO;
    }

    @Override
    public void exportPassList(RtaReportListExportVO listVO, HttpServletResponse response) throws IOException {
        listVO.setPage(1L);
        listVO.setPageNum(10000L);
        PageUtils<?> pageUtils = this.passList(listVO);
        Map<String, String> map = this.getSelect().stream().collect(Collectors.toMap(CustomIndexContentDTO::getKey, CustomIndexContentDTO::getTitle));
        map.putAll(Map.of("masterId", "所属广告主ID", "masterName", "所属广告主名称"));
        ExcelUtils.download(response, "RTA通过率报表", "Sheet1", pageUtils.getData(),
                listVO.getHeaders().stream().map(key -> {
                    return new SelectDTO2(key, map.get(key));
                }).collect(Collectors.toList())
        );

    }

    @Override
    public List<RtaReportListDTO> passData(Long start, Long end) {
        return this.rtaReportMapper.list2(new QueryWrapper<>()
                .between("dim_report_hour", DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(start), "yyyy-MM-dd HH:mm:00")),
                        DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(end), "yyyy-MM-dd HH:mm:00")))
                .ge("dim_report_hour + dim_minute * 60", start)
                .lt("dim_report_hour + dim_minute * 60", end)
                .groupBy("dim_rta_strategy_id")
                .select("dim_rta_strategy_id as rta_strategy_id, " +
                        "ROUND(SUM(`idx_bid`) * 100 /SUM(`idx_all`), 2) AS `pass_rate` ")
        );
    }

    private QueryWrapper<?> getQueryWrapper(RtaReportBaseVO baseVO) {
        QueryWrapper<?> queryWrapper = new QueryWrapper<>()
                .eq(ObjectUtils.isNotNullOrZero(baseVO.getRtaGroupId()), "dim_rta_group_id", baseVO.getRtaGroupId())
                .eq(ObjectUtils.isNotNullOrZero(baseVO.getCountryId()), "dim_country_id", baseVO.getCountryId())
                .between("dim_day", DateUtils.string2Long(baseVO.getStartDate()), DateUtils.string2Long(baseVO.getEndDate()));
        RtaSelectGetVO getVO = new RtaSelectGetVO();
        if (ObjectUtils.isNotNullOrZero(baseVO.getMasterId())) {
            getVO.setMasterId(baseVO.getMasterId().longValue());
        }
        getVO.setRtaGroupId(baseVO.getRtaGroupId());
        if (StringUtils.isNotBlank(baseVO.getSearch())) {
            getVO.setSearch(baseVO.getSearch());
        }
        List<Long> ids = fgMarketService.selectRtaStrategy(getVO).getData().stream().map(SelectDTO::getId).collect(Collectors.toList());
        if (ids.isEmpty()) {
            return null;
        }
        return queryWrapper.in("dim_rta_strategy_id", ids);
    }

    /**
     * 获取表格字段
     *
     * @param field 字段
     * @return 返回数据
     */
    private String getTableField(String field) {
        if ("passRate".equals(field)) {
            return HumpLineUtils.humpToLine2(field);
        }
        field = field.replace("Name", "Id");
        if (Dimension.MASTER_ID.equals(field)) {
            return HumpLineUtils.humpToLine2(field);
        }
        if (List.of("dayId", "hourId", "day", "hour").contains(field)) {
            return String.format("dim_%s", field.replace("Id", ""));
        }
        field = field.replace("Name", "Id");
        if (List.of(Dimension.RTA_GROUP_ID, Dimension.RTA_STRATEGY_ID, Dimension.COUNTRY_ID).contains(field)) {
            return String.format("dim_%s", HumpLineUtils.humpToLine2(field));
        }
        return String.format("idx_%s", HumpLineUtils.humpToLine2(field));
    }

    /**
     * 获取map
     *
     * @param list  数据
     * @param field 字段
     * @return 返回数据
     */
    private Map<Long, String> getFieldMapByList(List<RtaReportListDTO> list, String field) {
        if (CollectionUtils.isEmpty(list)) {
            return Map.of();
        }
        switch (field) {
            case Dimension.RTA_STRATEGY_ID:
                return this.getFieldMap(list.stream().map(RtaReportListDTO::getRtaStrategyId).collect(Collectors.toList()), field);
            case Dimension.RTA_GROUP_ID:
                return fgMarketService.selectRtaGroup().getData().stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
            case Dimension.COUNTRY_ID:
                return this.getFieldMap(list.stream().map(RtaReportListDTO::getCountryId).collect(Collectors.toList()), field);
            case Dimension.MASTER_ID:
                return this.getFieldMap(List.of(0L), field);
            default:
                return Map.of();
        }
    }

    private Map<Long, String> getFieldMap(List<Long> ids, String field) {
        if (CollectionUtils.isEmpty(ids)) {
            return Map.of();
        }
        switch (field) {
            case Dimension.RTA_STRATEGY_ID:
                RtaSelectGetVO getVO = new RtaSelectGetVO();
                getVO.setIds(ids);
                return fgMarketService.selectRtaStrategy(getVO).getData().stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
            case Dimension.RTA_GROUP_ID:
                return fgMarketService.selectRtaGroup().getData().stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
            case Dimension.COUNTRY_ID:
                AreaCountrySelectVO selectVO = new AreaCountrySelectVO();
                selectVO.setCountryIds(ids);
                return fgSystemService.selectCountry(selectVO).getData().stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
            case Dimension.ALL:
                return Map.of(0L, "全部");
            case Dimension.MASTER_ID:
                return this.fgMarketService.selectMaster().getData().stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
            default:
                return Map.of();
        }
    }

    private List<CustomIndexContentDTO> getSelect() {
        CustomIndexGetVO customIndexGetVO = new CustomIndexGetVO();
        customIndexGetVO.setModule("rta_pass_report_list");
        return fgSystemService.getCustomIndexByModule(customIndexGetVO).getData();
    }

}
