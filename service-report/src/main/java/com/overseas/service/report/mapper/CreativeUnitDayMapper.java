package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.CreativeUnitDay;
import com.overseas.service.report.entity.CreativeUnitHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CreativeUnitDayMapper extends BaseMapper<CreativeUnitDay> {

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_creative_unit_day ${ew.customSqlSegment}")
    List<Long> getIds(@Param(ConstantUtils.WRAPPER) Wrapper<CreativeUnitHour> wrapper);
}
