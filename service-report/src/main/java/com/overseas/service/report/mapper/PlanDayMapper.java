package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.dto.report.UserCostDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.PlanDay;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PlanDayMapper extends BaseMapper<PlanDay> {

    @Select("SELECT dim_agent_id AS `agent_id`,dim_master_id AS `master_id`," +
            "ROUND((SUM(idx_report_cost) / 1000000), 3) AS `cost` " +
            "FROM t_ads_dsp_flow_plan_day " +
            "${ew.customSqlSegment}")
    List<UserCostDTO> getUserCost(@Param(ConstantUtils.WRAPPER) Wrapper<PlanDay> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_day ${ew.customSqlSegment}")
    List<Long> getIds(@Param(ConstantUtils.WRAPPER) Wrapper<PlanDay> wrapper);
}
