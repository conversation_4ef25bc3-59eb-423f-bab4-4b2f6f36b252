package com.overseas.service.report.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.report.FlowReportListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.FlowHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DS("ck")
public interface FlowHourMapper extends BaseMapper<FlowHour> {
    /**
     * 获取流量查询报表数据
     */
    @Select("SELECT ${ew.sqlSelect} FROM `t_ads_dsp_flow_hour` ${ew.customSqlSegment}")
    IPage<FlowReportListDTO> getFlowSearchReportPageList(IPage<FlowReportListDTO> page,
                                                         @Param(ConstantUtils.WRAPPER) Wrapper<FlowHour> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM `t_ads_dsp_flow_hour` ${ew.customSqlSegment}")
    FlowReportListDTO getFlowSearchReportTotal(@Param(ConstantUtils.WRAPPER) Wrapper<FlowHour> wrapper);

//    @Select("SELECT ${ew.sqlSelect} FROM `t_ads_dsp_flow_os_hour` ${ew.customSqlSegment}")
//    IPage<FlowReportListDTO> getFlowSearchReportMaterialPageList(IPage<FlowReportListDTO> page,
//                                                         @Param(ConstantUtils.WRAPPER) Wrapper<FlowHour> wrapper);

    @Select("SELECT a.*,ROUND((a.`req`/b.`req`)*100,2) AS `req_ratio`,b.`req` AS  `real_req` " +
            "FROM " +
            "(SELECT ${ew.sqlSelect} FROM `t_ads_dsp_flow_os_hour` ${ew.customSqlSegment}) AS a LEFT JOIN " +
            "(SELECT ${ew1.sqlSelect} FROM `t_ads_dsp_flow_hour` ${ew1.customSqlSegment}) AS b ON " +
            "${joinFields} ")
    IPage<FlowReportListDTO> getFlowSearchReportMaterialPageList(
            IPage<FlowReportListDTO> page, @Param(ConstantUtils.WRAPPER) Wrapper<FlowHour> wrapper,
            @Param("ew1") Wrapper<FlowHour> wrapper1, @Param("joinFields") String joinFields);

    @Select("SELECT ${ew.sqlSelect} FROM `t_ads_dsp_flow_os_hour` ${ew.customSqlSegment}")
    FlowReportListDTO getFlowSearchReportMaterialTotal(@Param(ConstantUtils.WRAPPER) Wrapper<FlowHour> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM `t_ads_dsp_flow_hour` ${ew.customSqlSegment}")
    List<FlowReportListDTO> getFlowSearchReportList(@Param(ConstantUtils.WRAPPER) QueryWrapper<FlowHour> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM `t_ads_dsp_flow_os_hour` ${ew.customSqlSegment}")
    List<FlowReportListDTO> getFlowSearchReportMaterialList(@Param(ConstantUtils.WRAPPER) QueryWrapper<FlowHour> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM `t_ads_dsp_flow_os_hour` ${ew.customSqlSegment}")
    List<SelectDTO3> getSizeSelect(@Param(ConstantUtils.WRAPPER) Wrapper<FlowHour> wrapper);

    /**
     * 获取流量查询小时报表数据
     */
    @Select("SELECT ${ew.sqlSelect} FROM `t_ads_dsp_flow_hour` ${ew.customSqlSegment} LIMIT 100")
    List<FlowReportListDTO> listFlowHourTop100Report(@Param(ConstantUtils.WRAPPER) Wrapper<FlowHour> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM `t_ads_dsp_flow_os_hour` ${ew.customSqlSegment} LIMIT 100")
    List<FlowReportListDTO> listFlowHourTop100ReportMaterial(@Param(ConstantUtils.WRAPPER) Wrapper<FlowHour> wrapper);
}
