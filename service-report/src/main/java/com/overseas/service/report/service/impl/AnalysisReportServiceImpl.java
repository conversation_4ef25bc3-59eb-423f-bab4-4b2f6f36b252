package com.overseas.service.report.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.market.plan.PlanDirectValueDTO;
import com.overseas.common.dto.report.AnalysisReportListDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexChildColumnDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexFieldDTO;
import com.overseas.common.entity.User;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.PlanFlowTypeEnum;
import com.overseas.common.enums.market.PlanSlotTypeEnum;
import com.overseas.common.enums.report.ReportDirectEnum;
import com.overseas.common.enums.report.ReportTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.campaign.CampaignSelectGetVO;
import com.overseas.common.vo.market.monitor.monitorEvent.MonitorEventIdsGetVO;
import com.overseas.common.vo.market.plan.PlanDirectGetVO;
import com.overseas.common.vo.market.plan.PlanSelectGetVO;
import com.overseas.common.vo.market.plan.direct.EpSelectGetVO;
import com.overseas.common.vo.market.plan.direct.RtaSelectGetVO;
import com.overseas.common.vo.market.slot.SlotSelectGetVO;
import com.overseas.common.vo.report.AnalysisReportChartVO;
import com.overseas.common.vo.report.AnalysisReportListVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.service.report.enums.AnalysisReportFieldEnum;
import com.overseas.service.report.feign.FgMarketService;
import com.overseas.service.report.feign.FgSystemService;
import com.overseas.service.report.mapper.*;
import com.overseas.service.report.service.AnalysisReportService;
import com.overseas.service.report.service.ReportService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnalysisReportServiceImpl implements AnalysisReportService {

    private final FgMarketService fgMarketService;

    private final FgSystemService fgSystemService;

    private final PackageHourMapper packageHourMapper;

    private final SlotPackageUnitHourMapper slotPackageUnitHourMapper;

    private final ReportService reportService;

    private final PlanSspHourMapper planSspHourMapper;

    private final DealHourMapper dealHourMapper;

    private final PlanEpHourMapper planEpHourMapper;

    @Override
    public MultiIndexChartDTO chartAnalysisReport(AnalysisReportChartVO chartVO, User user) {
        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        switch (chartVO.getDateType()) {
            case 1:
                chartVO.setCompareField("reportDate");
                break;
            case 2:
                chartVO.setCompareField("reportHour");
                break;
            default:
        }
        chartVO.setOldDimensions(chartVO.getDimensions());
        chartVO.setDimensions(List.of(chartVO.getCompareField()));
        // 2.获取列表数据
        Object data = this.getDataInfo(chartVO, true);
        if (null == data) {
            return multiIndexChartDTO;
        }
        List<AnalysisReportListDTO> list = (List<AnalysisReportListDTO>) data;
        if (list.isEmpty()) {
            return multiIndexChartDTO;
        }
        Map<String, String> keyMap = fgSystemService.getUserCustomIndexReport(CustomIndexGetVO.builder()
                        .module(chartVO.getModule()).identify(chartVO.getIdentify()).build()).getData()
                .stream().collect(Collectors.toMap(CustomIndexChildColumnDTO::getKey, CustomIndexChildColumnDTO::getTitle));
        return MultiIndexChartUtils.initChart(list, chartVO.getIndicator(), chartVO.getComparisonIndicator(), keyMap,
                "reportHour".equals(chartVO.getCompareField()) ? "dayHour" : "day");
    }

    @Override
    public PageUtils<AnalysisReportListDTO> listAnalysisReport(AnalysisReportListVO listVO) {
        listVO.setDimensions(listVO.getDimensions().stream().distinct().collect(Collectors.toList()));
        Object data = this.getDataInfo(listVO, false);
        if (null == data) {
            return new PageUtils<>(List.of(), 0L);
        }
        AnalysisPage analysisPage = (AnalysisPage) data;
        // 4.填充数据
        this.fillData(analysisPage.getPageData().getRecords(), listVO);
        // 5.添加汇总数据
        return new PageUtils<>(analysisPage.getPageData(), analysisPage.getTotal());
    }

    /**
     * 获取数据
     *
     * @param listVO  条件
     * @param isChart 是否图标
     * @return 返回数据
     */
    private Object getDataInfo(AnalysisReportListVO listVO, Boolean isChart) {
        //流量类型不区分 ab，普通活动
        if (this.isAdxFlowReport(listVO)) {
            listVO.setReportMode(null);
        }
        //判定是否需要根据报表mode获取活动ID
        if (ObjectUtils.isNotNullOrZero(listVO.getReportMode())) {
            CampaignSelectGetVO selectGetVO = new CampaignSelectGetVO();
            selectGetVO.setCampaignMode(List.of(listVO.getReportMode()));
            if (ObjectUtils.isNotNullOrZero(listVO.getMasterId())) {
                selectGetVO.setMasterId(listVO.getMasterId());
            }

            FeignR<List<SelectDTO>> selects = fgMarketService.selectCampaign(selectGetVO);
            log.info("select campaigns result : {}", JSONObject.toJSONString(selects));
            List<Long> campIds = selects.getData()
                    .stream().map(SelectDTO::getId).collect(Collectors.toList());
            if (campIds.isEmpty()) {
                return null;
            }
            //取活动合集
            if (CollectionUtils.isNotEmpty(listVO.getCampaignIds())) {
                listVO.getCampaignIds().retainAll(campIds);
            } else {
                listVO.setCampaignIds(campIds);
            }
        }

        // 1.获取指标
        CustomIndexGetVO getVO = new CustomIndexGetVO();
        getVO.setIdentify(listVO.getIdentify());
        getVO.setModule(listVO.getModule());
        Map<String, CustomIndexFieldDTO> keyFieldMap = this.fgSystemService.getCustomIndexFieldMap(getVO).getData()
                .stream().collect(Collectors.toMap(CustomIndexFieldDTO::getKey, Function.identity()));

        // 获取公共查询SQL
        List<String> commonSelectSql = this.getCommonSelectSql(listVO);
        // 获取内查询的SUM SQL
        String innerSelectSql = String.join(",", commonSelectSql) + "," + this.getInnerSelectSql(keyFieldMap, "list");
        // 获取需要过滤的SQL
        Map<String, String> conditionMap = Map.of("gt", ">", "ge", ">=", "equal", "=",
                "lt", "<", "le", "<=");
        String filterSql = "";
        String filterTotal = "";
        if (CollectionUtils.isNotEmpty(listVO.getFieldRules())
                && listVO.getDimensions().contains(AnalysisReportFieldEnum.PACKAGE.getKey())) {
            filterSql = " WHERE " + listVO.getFieldRules().stream()
                    .map(fieldRule -> String.format("report.%s %s %s", fieldRule.getKey(), conditionMap.get(fieldRule.getCondition()), fieldRule.getValue()))
                    .collect(Collectors.joining(" AND "));
            filterTotal = "WHERE " + listVO.getFieldRules().stream()
                    .map(fieldRule -> keyFieldMap.get(fieldRule.getKey()).getRule().replace("SUM", "")
                            + conditionMap.get(fieldRule.getCondition()) + fieldRule.getValue())
                    .collect(Collectors.joining(" AND "));

        }
        if (StringUtils.isNotBlank(listVO.getSortField())) {
            String orderBy;
            List<String> orderBys = AnalysisReportFieldEnum.getFieldsByKeys(List.of(listVO.getSortField()));
            if (CollectionUtils.isEmpty(orderBys)) {
                orderBy = listVO.getSortField();
            } else {
                orderBy = orderBys.get(0).replace("dim_", "");
            }
            if (!"normal".equalsIgnoreCase(listVO.getSortType())) {
                filterSql = String.format("%s order by %s %s", filterSql, orderBy, listVO.getSortType());
            }
        }
        // 2.获取列表数据
        QueryWrapper<?> queryWrapper = this.getWrapper(listVO, innerSelectSql);
        if (null == queryWrapper) {
            return null;
        }
        if (isChart) {
            return this.getReportList(listVO, queryWrapper,
                    this.getFinalSelectSql(listVO, commonSelectSql, keyFieldMap, "list"), filterSql);
        } else {
            IPage<AnalysisReportListDTO> pageData = this.getReportPage(listVO,
                    new Page<>(listVO.getPage(), listVO.getPageNum()), queryWrapper,
                    this.getFinalSelectSql(listVO, commonSelectSql, keyFieldMap, "list"), filterSql);
            if (pageData.getRecords().isEmpty()) {
                return null;
            }
            QueryWrapper<?> queryWrapper2 = this.getWrapper(listVO,
                    String.join(",", commonSelectSql) + "," + this.getInnerSelectSql(keyFieldMap, "total"));
            AnalysisReportListDTO analysisReportListDTO = this.getAnalysisReportTotal(listVO, queryWrapper2,
                    this.getFinalSelectSql(listVO, commonSelectSql, keyFieldMap, "total"), filterTotal);
            return AnalysisPage.builder().pageData(pageData).total(analysisReportListDTO).build();

        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AnalysisPage {

        private IPage<AnalysisReportListDTO> pageData;

        private AnalysisReportListDTO total;
    }


    /**
     * 获取通用字段
     *
     * @param listVO 筛选条件
     * @return 返回数据
     */
    private List<String> getCommonSelectSql(AnalysisReportListVO listVO) {
        // 小时
        int hourRatio = Objects.requireNonNull(TimeZoneEnum.get(listVO.getTimeZone())).getFormatHour();
        return new ArrayList<>() {{
            // 如果聚合了日期
            if (listVO.getDimensions().contains(AnalysisReportFieldEnum.REPORT_DATE.getKey())) {
                add("UNIX_TIMESTAMP(FROM_UNIXTIME(dim_report_hour + " + 3600 * hourRatio + ",'%Y-%m-%d')) AS `day`");
            }
            // 如果聚合了小时
            if (listVO.getDimensions().contains(AnalysisReportFieldEnum.REPORT_HOUR.getKey())) {
                if (List.of(ReportTypeEnum.CUSTOM.getId(), ReportTypeEnum.SLOT_PKG.getId()).contains(listVO.getReportType())) {
                    add("CAST(FROM_UNIXTIME(dim_report_hour + " + 3600 * hourRatio + ",'%H') AS Int8) AS `day_hour`");
                } else {
                    add("dim_report_hour + " + 3600 * hourRatio + " AS `day_hour`");
                }
            }
            List<String> fieldList = AnalysisReportFieldEnum.getFieldsByKeys(listVO.getDimensions());
            fieldList.removeAll(List.of("`day`", "`day_hour`"));
            addAll(ReportTypeEnum.translateField(fieldList));
        }};
    }

    /**
     * 获取内查询的SUM SQL
     *
     * @param keyFieldMap key-field映射
     * @return 返回数据
     */
    private String getInnerSelectSql(Map<String, CustomIndexFieldDTO> keyFieldMap, String type) {
        if ("total".equals(type)) {
            return keyFieldMap.values().stream()
                    .flatMap(keyField -> {
                        List<String> fieldList = new ArrayList<>();
                        // 获取对应的field
                        Pattern pattern = Pattern.compile("SUM\\([a-z0-9_]*\\)");
                        Matcher matcher = pattern.matcher(keyField.getRule());
                        while (matcher.find()) {
                            fieldList.add(matcher.group());
                        }
                        return fieldList.stream().map(this::getKeyField);
                    }).distinct().collect(Collectors.joining(","));
        } else {
            return keyFieldMap.values().stream().map(keyField -> String.format("%s AS %s", keyField.getRule(), keyField.getKey())).distinct().collect(Collectors.joining(","));
        }
    }


    /**
     * 数据sql
     *
     * @param sumSql sum sql
     * @return 返回数据
     */
    private String getKeyField(String sumSql) {
        String asName = "";
        // 获取对应的field
        Pattern pattern = Pattern.compile("\\([a-z0-9_]*\\)");
        Matcher matcher = pattern.matcher(sumSql);
        while (matcher.find()) {
            asName = matcher.group();
        }
        return sumSql + " AS " + asName.replace("(", "").replace(")", "").replaceAll("`", "");
    }

    /**
     * 获取返回数据问题
     *
     * @param commonSelectSql 同类数据
     * @param keyFieldMap     字段映射
     * @param type            类型
     * @return 返回数据
     */
    private String getFinalSelectSql(AnalysisReportListVO listVO, List<String> commonSelectSql, Map<String, CustomIndexFieldDTO> keyFieldMap, String type) {
        boolean isNotCk = List.of(ReportTypeEnum.SSP.getId(), ReportTypeEnum.DEAL.getId(),
                ReportTypeEnum.ADX.getId()).contains(listVO.getReportType()) || isFlowSsp(listVO);

        String selectInfoSql = keyFieldMap.values().stream().map(keyField -> {
            if ("total".equals(type)) {
                return String.format(" %s AS `%s`", keyField.getRule(), keyField.getKey());
            }
            String field = keyField.getKey();
            if (!isNotCk) {
                field = String.format("IF(isNaN(%s), 0, IF(isInfinite(%s), 0, %s))", keyField.getKey(), keyField.getKey(), keyField.getKey());
            }
            return String.format(" %s AS `%s` ", field, keyField.getKey());
        }).collect(Collectors.joining(","));
        if ("total".equals(type)) {
            return selectInfoSql;
        }
        String commonSql = commonSelectSql.stream().map(field -> {
            String[] str = field.split("AS");
            return str[str.length - 1].trim();
        }).collect(Collectors.joining(","));
        return commonSql + "," + selectInfoSql;
    }

    @Override
    public AnalysisReportListDTO getAnalysisReportTotal(AnalysisReportListVO listVO, QueryWrapper<?> queryWrapper,
                                                        String finalSelectSql, String filterSql) {
        AnalysisReportListDTO analysisReportListDTO = this.getReportSummaryData(listVO, queryWrapper,
                finalSelectSql, filterSql);
        analysisReportListDTO.setMasterName(ConstantUtils.ALL_2);
        analysisReportListDTO.setFlowTypeName(ConstantUtils.ALL_2);
        analysisReportListDTO.setReportDate(ConstantUtils.ALL_2);
        analysisReportListDTO.setReportHour(ConstantUtils.ALL_2);
        analysisReportListDTO.setCampaignName(ConstantUtils.ALL_2);
        analysisReportListDTO.setPlanName(ConstantUtils.ALL_2);
        analysisReportListDTO.setAdxName(ConstantUtils.ALL_2);
        analysisReportListDTO.setEpName(ConstantUtils.ALL_2);
        analysisReportListDTO.setSlotTypeName(ConstantUtils.ALL_2);
        analysisReportListDTO.setPkg(ConstantUtils.ALL_2);
        analysisReportListDTO.setSsp(ConstantUtils.ALL_2);
        analysisReportListDTO.setDealId(ConstantUtils.ALL_2);
        analysisReportListDTO.setDealName(ConstantUtils.ALL_2);
        analysisReportListDTO.setSupplierChain(ConstantUtils.ALL_2);
        analysisReportListDTO.setRtaName(ConstantUtils.ALL_2);
        analysisReportListDTO.setSlotName(ConstantUtils.ALL_2);
        for (int i = 1; i < 41; i++) {
            String key = "actionD" + i;
            Object val = ObjectUtils.getObjectValue(analysisReportListDTO, key);
            if (val instanceof Double) {
                double t = (double) val;
                if (Double.isNaN(t) || Double.isInfinite(t)) {
                    ObjectUtils.setObjectValue(analysisReportListDTO, key, 0d);
                }
            }
        }

        return analysisReportListDTO;
    }

    /**
     * 获取汇总数据查询参数
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    private QueryWrapper<?> getSummaryWrapper(AnalysisReportListVO listVO, String sqlSelect) {

        Long startDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(
                DateUtils.string2Date(listVO.getStartDate()), listVO.getTimeZone(), 0)),
                endDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(
                        DateUtils.string2Date(listVO.getEndDate()), listVO.getTimeZone(), 23));

        Long startDay = DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(startDate))),
                endDay = DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(endDate)));

        List<Integer> masterIds;
        //流量类型不支持过滤账户
        if (this.isAdxFlowReport(listVO) || ReportTypeEnum.FLOW_SSP_PKG.getId().equals(listVO.getReportType())) {
            masterIds = List.of();
        } else {
            masterIds = (ObjectUtils.isNotNullOrZero(listVO.getMasterId()) || CollectionUtils.isNotEmpty(listVO.getMasterIds())) ?
                    List.of() : this.fgSystemService.getMasterIds().getData();
        }
        List<Long> optimizeTargets = new ArrayList<>() {{
            if (ObjectUtils.isNotNullOrZero(listVO.getOptimizeTargetId())) {
                MonitorEventIdsGetVO getVO = new MonitorEventIdsGetVO();
                getVO.setMasterId(listVO.getMasterId());
                getVO.setActionIds(List.of(listVO.getOptimizeTargetId()));
                addAll(fgMarketService.listMonitorEventIdsByAction(getVO).getData());
            }
        }};
        //获取特殊条件内容
        if (CollectionUtils.isNotEmpty(optimizeTargets) || CollectionUtils.isNotEmpty(listVO.getTemplateIds())
                || ObjectUtils.isNotNullOrZero(listVO.getBidType())) {
            List<Long> planIds = fgMarketService.selectPlan(PlanSelectGetVO.builder()
                    .masterId(listVO.getMasterId())
                    .planMode(ObjectUtils.isNotNullOrZero(listVO.getReportMode()) ? List.of(listVO.getReportMode()) : List.of(1, 3))
                    .optimizeTargetIds(optimizeTargets)
                    .templateIds(listVO.getTemplateIds())
                    .bidType(listVO.getBidType())
                    .build()
            ).getData().stream().map(SelectDTO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(listVO.getPlanIds())) {
                listVO.setPlanIds(planIds);
            } else {
                listVO.setPlanIds((List<Long>) CollectionUtils.intersection(listVO.getPlanIds(), planIds));
            }
            if (CollectionUtils.isEmpty(listVO.getPlanIds())) {
                return null;
            }
        }
        if (StringUtils.isNotBlank(listVO.getSlotName())) {
            SlotSelectGetVO getVO = new SlotSelectGetVO();
            getVO.setSlotStatus(-1);
            getVO.setSlotName(listVO.getSlotName());
            List<Long> slotIds = fgMarketService.getSlotSelect(getVO).getData().stream().map(SelectDTO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(slotIds)) {
                return null;
            }
            listVO.setSlotIds(slotIds);
        }

        //是否需要替换 dim_ad_type( ck 特有字段)
        Boolean isReplaceDimAdType = this.isReplaceDimAdType(listVO);
        return new QueryWrapper<>().select(sqlSelect)
                // 为了走分区索引，添加dim_day查询
                .between("dim_day", startDay, endDay)
                .between("dim_report_hour", startDate, endDate)
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()),
                        "dim_master_id", listVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(masterIds),
                        "dim_master_id", masterIds)
                .in(CollectionUtils.isNotEmpty(listVO.getMasterIds()),
                        "dim_master_id", listVO.getMasterIds())
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()),
                        "dim_campaign_id", listVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(listVO.getFlowTypes()),
                        "dim_flow_type", listVO.getFlowTypes())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()),
                        "dim_plan_id", listVO.getPlanIds())
                .in(CollectionUtils.isNotEmpty(listVO.getAdxIds()),
                        "dim_adx_id", listVO.getAdxIds())
                .in(CollectionUtils.isNotEmpty(listVO.getEpIds()),
                        "dim_ep_id", listVO.getEpIds())
                .in(CollectionUtils.isNotEmpty(listVO.getRtaStrategyIds()),
                        "dim_rta_id", listVO.getRtaStrategyIds())
                // 需要查plan表
                .in(isReplaceDimAdType && CollectionUtils.isNotEmpty(listVO.getSlotTypes()),
                        "m_plan.slot_type", listVO.getSlotTypes())
                .in(!isReplaceDimAdType && CollectionUtils.isNotEmpty(listVO.getSlotTypes()),
                        "dim_ad_type", listVO.getSlotTypes())
                .like(StringUtils.isNotBlank(listVO.getSearch()) &&
                        (List.of(ReportTypeEnum.PACKAGE_NAME.getId(), ReportTypeEnum.SLOT_PKG.getId()).contains(listVO.getReportType()) ||
                                listVO.getDimensions().contains(AnalysisReportFieldEnum.PACKAGE.getKey()) ||
                                (CollectionUtils.isNotEmpty(listVO.getOldDimensions()) && listVO.getOldDimensions().contains(AnalysisReportFieldEnum.PACKAGE.getKey()))
                        ), "dim_pkg", listVO.getSearch())
                .eq(StringUtils.isNotBlank(listVO.getSearch()) && isFlowSsp(listVO),
                        "dim_ssp", listVO.getSearch())
                .in(CollectionUtils.isNotEmpty(listVO.getSlotIds()), "dim_slot_id", listVO.getSlotIds());
    }

    /**
     * 获取列表数据查询参数
     *
     * @param listVO    传入参数
     * @param sqlSelect 字段指标
     * @return 返回数据
     */
    private QueryWrapper<?> getWrapper(AnalysisReportListVO listVO, String sqlSelect) {

        QueryWrapper<?> queryWrapper = this.getSummaryWrapper(listVO, sqlSelect);
        if (null == queryWrapper) {
            return null;
        }
        // 设置默认排序
        if (List.of("", "normal").contains(listVO.getSortType())
                || List.of("id", "").contains(listVO.getSortField())) {
            listVO.setSortField(
                    (listVO.getDimensions().contains(AnalysisReportFieldEnum.REPORT_DATE.getKey())
                            || listVO.getDimensions().contains(AnalysisReportFieldEnum.REPORT_HOUR.getKey()))
                            ? "dim_report_hour" : "masterCost"
            );
        }
        if ("reportDate".equals(listVO.getSortField())) {
            listVO.setSortField("`day`");
        }
        if ("reportHour".equals(listVO.getSortField())) {
            listVO.setSortField("`day_hour`");
        }
        if (listVO.getReportType().equals(ReportTypeEnum.SSP.getId())) {
            if (StringUtils.isNotBlank(listVO.getSearch())) {
                queryWrapper.eq("dim_ssp", listVO.getSearch());
            }
            if (CollectionUtils.isNotEmpty(listVO.getSupplierChain())) {
                if (listVO.getSupplierChain().contains(100)) {
                    queryWrapper.and(q ->
                            q.in("dim_supplier_chain", listVO.getSupplierChain())
                                    .or().gt("dim_supplier_chain", "5")
                    );
                } else {
                    queryWrapper.in("dim_supplier_chain", listVO.getSupplierChain());
                }
            }
        }
        if (listVO.getReportType().equals(ReportTypeEnum.DEAL.getId())) {
            if (StringUtils.isNotBlank(listVO.getSearch())) {
                queryWrapper.like("dim_deal_id", listVO.getSearch());
            }
            if (ObjectUtils.isNotNullOrZero(listVO.getDealId()) && StringUtils.isNotBlank(listVO.getDealId())) {
                queryWrapper.eq("dim_deal_id", listVO.getDealId());
            }
        }
        String groupBys = String.join(",", AnalysisReportFieldEnum.getFieldsByKeys(listVO.getDimensions()));
        if (isReplaceDimAdType(listVO)) {
            groupBys = groupBys.replace("dim_ad_type", "slot_type");
        }
        queryWrapper.groupBy(groupBys);
        return queryWrapper;
    }

    /**
     * 获取总数
     *
     * @param listVO         条件
     * @param queryWrapper   条件
     * @param finalSelectSql 最终数据
     * @param filterSql      过滤数据
     * @return 返回数据
     */
    private AnalysisReportListDTO getReportSummaryData(AnalysisReportListVO listVO, QueryWrapper<?> queryWrapper,
                                                       String finalSelectSql, String filterSql) {
        if (isReplaceDimAdType(listVO)) {
            queryWrapper.select(queryWrapper.getSqlSelect().replaceAll("dim_ad_type", "slot_type"));
        }
        // 如果聚合维度包含包名，直接取临时表数据
        if (listVO.getReportType().equals(ReportTypeEnum.SSP.getId()) || isFlowSsp(listVO)) {
            return this.planSspHourMapper.getSspSummaryReportData(queryWrapper, finalSelectSql);
        } else if (listVO.getReportType().equals(ReportTypeEnum.DEAL.getId())) {
            return this.dealHourMapper.getDealSummaryReportData(queryWrapper, finalSelectSql);
        } else if (isAdxFlowReport(listVO)) {
            return this.planEpHourMapper.getAdxRtaSummaryReportData(queryWrapper, finalSelectSql);
        } else {
            for (String key : AnalysisReportFieldEnum.getFieldsByKeys(listVO.getDimensions())) {
                String val = key.replace("dim_", "");
                finalSelectSql = finalSelectSql.replace(val + ",", "").replace(val, "");
            }
            queryWrapper.select(queryWrapper.getSqlSelect().replaceAll("UNIX_TIMESTAMP", "toUnixTimestamp"));
            if (listVO.getReportType().equals(ReportTypeEnum.SLOT_PKG.getId())) {
                return this.slotPackageUnitHourMapper.getPackageSummaryReportDataByTemp(queryWrapper, finalSelectSql, filterSql);
            } else {
                return this.packageHourMapper.getPackageSummaryReportDataByTemp(queryWrapper, finalSelectSql, filterSql);
            }
        }
    }


    /**
     * 获取 list
     *
     * @param listVO       条件
     * @param queryWrapper wrapper条件
     * @return 返回数据
     */
    private List<AnalysisReportListDTO> getReportList(AnalysisReportListVO listVO, QueryWrapper<?> queryWrapper,
                                                      String finalSelectSql, String filterSql) {
        if (isReplaceDimAdType(listVO)) {
            queryWrapper.select(queryWrapper.getSqlSelect().replaceAll("dim_ad_type", "slot_type"));
        }
        if (listVO.getReportType().equals(ReportTypeEnum.SSP.getId()) || isFlowSsp(listVO)) {
            return this.planSspHourMapper.listSspReportAnalysis(queryWrapper, finalSelectSql, filterSql);
        } else if (listVO.getReportType().equals(ReportTypeEnum.DEAL.getId())) {
            return this.dealHourMapper.listDealReportAnalysis(queryWrapper, finalSelectSql, filterSql);
        } else if (isAdxFlowReport(listVO)) {
            return this.planEpHourMapper.getAdxReportListAnalysis(queryWrapper, finalSelectSql, filterSql);
        } else {
            queryWrapper.select(queryWrapper.getSqlSelect().replaceAll("UNIX_TIMESTAMP", "toUnixTimestamp"));
            if (listVO.getDimensions().contains(AnalysisReportFieldEnum.REPORT_HOUR.getKey())) {
                queryWrapper.groupBy("dim_report_hour");
            }
            if (listVO.getReportType().equals(ReportTypeEnum.SLOT_PKG.getId())) {
                return this.slotPackageUnitHourMapper.listPackageReportByTemp(queryWrapper, finalSelectSql, filterSql);
            } else {
                return this.packageHourMapper.listPackageReportByTemp(queryWrapper, finalSelectSql, filterSql);
            }
        }
    }

    /**
     * 获取数据
     *
     * @param listVO         条件
     * @param page           分页
     * @param queryWrapper   条件
     * @param finalSelectSql 最终数据
     * @param filterSql      过滤数据
     * @return 返回数据
     */
    private IPage<AnalysisReportListDTO> getReportPage(AnalysisReportListVO listVO, IPage<AnalysisReportListDTO> page,
                                                       QueryWrapper<?> queryWrapper, String finalSelectSql,
                                                       String filterSql) {
        if (isReplaceDimAdType(listVO)) {
            queryWrapper.select(queryWrapper.getSqlSelect().replaceAll("dim_ad_type", "slot_type"));
        }
        if (listVO.getReportType().equals(ReportTypeEnum.SSP.getId()) || isFlowSsp(listVO)) {
            return this.planSspHourMapper.listAnalysisSspReport(page, queryWrapper, finalSelectSql, filterSql);
        } else if (listVO.getReportType().equals(ReportTypeEnum.DEAL.getId())) {
            return this.dealHourMapper.listAnalysisDealReport(page, queryWrapper, finalSelectSql, filterSql);
        } else if (isAdxFlowReport(listVO)) {
            return this.planEpHourMapper.listAdxRtaReport(page, queryWrapper, finalSelectSql, filterSql);
        } else {
            queryWrapper.select(queryWrapper.getSqlSelect().replaceAll("UNIX_TIMESTAMP", "toUnixTimestamp"));
            if (listVO.getDimensions().contains(AnalysisReportFieldEnum.REPORT_HOUR.getKey())) {
                queryWrapper.groupBy("dim_report_hour");
            }
            // 如果是下载，则查询list，不需要查询count
            if (listVO.getIsDownload()) {
                IPage<AnalysisReportListDTO> pageData = new Page<>();
                if (listVO.getReportType().equals(ReportTypeEnum.SLOT_PKG.getId())) {
                    pageData.setRecords(this.slotPackageUnitHourMapper.listPackageReportByTemp(queryWrapper, finalSelectSql,
                            filterSql + (" LIMIT " + listVO.getPageNum())));
                } else {
                    pageData.setRecords(this.packageHourMapper.listPackageReportByTemp(queryWrapper, finalSelectSql,
                            filterSql + (" LIMIT " + listVO.getPageNum())));
                }
                return pageData;
            } else {
                // 如果是页面查询，则需要查count
                if (listVO.getReportType().equals(ReportTypeEnum.SLOT_PKG.getId())) {
                    return this.slotPackageUnitHourMapper.listPackageReport(page, queryWrapper, finalSelectSql, filterSql);
                } else {
                    return this.packageHourMapper.listPackageReport(page, queryWrapper, finalSelectSql, filterSql);
                }
            }
        }
    }

    /**
     * 补充数据
     *
     * @param reportList 报表
     * @param listVO     条件
     */
    private void fillData(List<AnalysisReportListDTO> reportList, AnalysisReportListVO listVO) {
        // 获取当前报表类型对应数据，便于后续分析计划
        // 获取所有计划定向
        List<String> includes = new ArrayList<>();
        List<String> campaignIncludes = new ArrayList<>();
        Map<String, SelectDTO3> packagePrices = new HashMap<>();
        // 如果是下载或者聚合维度不仅只有包名，则不进行查询
        if (!listVO.getIsDownload() && this.isSearchPlanDirect(listVO.getDimensions())) {
            PlanDirectGetVO getVO = new PlanDirectGetVO();
            getVO.setCampaignIds(listVO.getCampaignIds());
            getVO.setPlanIds(listVO.getPlanIds());
            getVO.setReportType(ReportTypeEnum.PACKAGE_NAME.getId());
            getVO.setIsSearchCampaign(true);
            // 设置查询活动层级
            // 6：筛选出计划中存在选择定向的资源
            List<PlanDirectValueDTO> planDirectValueDTOS = this.fgMarketService.getPlanDirect(getVO).getData();
            if (CollectionUtils.isNotEmpty(planDirectValueDTOS)) {
                for (PlanDirectValueDTO planDirectValueDTO : planDirectValueDTOS) {
                    if (ReportDirectEnum.PACKAGE_PRICE.getId().equals(planDirectValueDTO.getDirectId())) {
                        packagePrices = planDirectValueDTO.getDirectValue()
                                .stream().collect(Collectors.toMap(SelectDTO3::getKey, Function.identity()));
                    }
                    if (!ReportDirectEnum.PACKAGE_PRICE.getId().equals(planDirectValueDTO.getDirectId())) {
                        List<SelectDTO3> selectDTO3s = planDirectValueDTO.getDirectValue();
                        if (listVO.getPlanIds().isEmpty()) {
                            includes = selectDTO3s.stream()
                                    .filter(u -> u.getTitle().equals("campaign"))
                                    .map(SelectDTO3::getKey).collect(Collectors.toList());
                        } else {
                            includes = selectDTO3s.stream()
                                    .filter(u -> u.getTitle().equals("plan"))
                                    .map(SelectDTO3::getKey).collect(Collectors.toList());
                            campaignIncludes = selectDTO3s.stream()
                                    .filter(u -> u.getTitle().equals("campaign"))
                                    .map(SelectDTO3::getKey).collect(Collectors.toList());
                        }
                    }
                }
            }
        }
        // 如果聚合维度包含账号，则查询账号
        Map<Long, String> masterMap = listVO.getDimensions().contains(AnalysisReportFieldEnum.MASTER.getKey())
                ? this.getMapByList(this.fgMarketService.selectAllMaster().getData()) : new HashMap<>();
        // 如果聚合维度包含活动，则查询活动
        Map<Long, String> campaignMap = new HashMap<>() {{
            if (listVO.getDimensions().contains(AnalysisReportFieldEnum.CAMPAIGN.getKey())) {
                CampaignSelectGetVO campaignGetVO = new CampaignSelectGetVO();
                campaignGetVO.setMasterId(listVO.getMasterId());
                campaignGetVO.setMasterIds(listVO.getMasterIds());
                campaignGetVO.setCampaignIds(reportList.stream().map(AnalysisReportListDTO::getCampaignId)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                putAll(getMapByList(fgMarketService.selectNoPermissionCampaign(campaignGetVO).getData()));
            }
        }};

        Map<Long, String> planMap = new HashMap<>() {{
            if (listVO.getDimensions().contains(AnalysisReportFieldEnum.PLAN.getKey())) {
                PlanSelectGetVO planSelectGetVO = new PlanSelectGetVO();
                planSelectGetVO.setMasterId(listVO.getMasterId());
                planSelectGetVO.setMasterIds(listVO.getMasterIds());
                planSelectGetVO.setPlanIds(reportList.stream().map(AnalysisReportListDTO::getPlanId)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                putAll(getMapByList(fgMarketService.selectPlan(planSelectGetVO).getData()));
            }
        }};
        // 如果聚合维度包含adx，则查询adx
        Map<Long, String> adxMap = listVO.getDimensions().contains(AnalysisReportFieldEnum.ADX.getKey())
                ? this.getMapByList(this.fgMarketService.selectAdx().getData()) : new HashMap<>();
        // 如果聚合维度包含ep，则查询ep
        Map<Long, String> epMap = listVO.getDimensions().contains(AnalysisReportFieldEnum.EP.getKey())
                ? this.getMapByList(this.fgMarketService.selectEp(new EpSelectGetVO()).getData()) : new HashMap<>();
        // 如果聚合维度包含RTA，则查询RTA
        Map<Long, String> rtaMap = new HashMap<>() {{
            RtaSelectGetVO getVO = new RtaSelectGetVO();
            getVO.setIsSearchMasterIds(0);
            getVO.setIds(reportList.stream().map(AnalysisReportListDTO::getRtaId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            if (listVO.getDimensions().contains(AnalysisReportFieldEnum.RTA.getKey())) {
                putAll(getMapByList(fgMarketService.selectRtaStrategy(getVO).getData()));
            }
        }};

        Map<Long, String> slotMap = new HashMap<>() {{
            if (listVO.getDimensions().contains(AnalysisReportFieldEnum.SLOT.getKey())) {
                SlotSelectGetVO getVO = new SlotSelectGetVO();
                getVO.setSlotIds(reportList.stream().map(AnalysisReportListDTO::getSlotId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                getVO.setSlotStatus(-1);
                putAll(fgMarketService.getSlotSelect(getVO).getData().stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName)));
            }
        }};

        // 如果聚合维度包含广告形式，则获取广告形式
        Map<Long, String> slotTypeMap = this.getMapByList(ICommonEnum.list(PlanSlotTypeEnum.class));
        Map<Long, String> flowTypeMap = this.getMapByList(ICommonEnum.list(PlanFlowTypeEnum.class));
        for (AnalysisReportListDTO entity : reportList) {
            entity.setReportDate(DateUtils.long2String(entity.getDay()));
            if (entity.getDayHour() > 24) {
                entity.setReportHour(DateUtils.format(DateUtils.long2Date(entity.getDayHour()), "HH:mm:ss"));
            } else {
                entity.setReportHour(entity.getDayHour().toString());
            }
            entity.setPackagePrice(packagePrices.containsKey(entity.getPkg())
                    ? Integer.parseInt(packagePrices.get(entity.getPkg()).getTitle()) : 0);
            entity.setCampaignStatus(campaignIncludes.contains(entity.getPkg()) ? 0 : 1);
            entity.setStatus(includes.contains(entity.getPkg()) ? 0 : 1);
            entity.setMasterName(masterMap.getOrDefault(entity.getMasterId(), ConstantUtils.PLACEHOLDER_2));
            entity.setCampaignName(campaignMap.getOrDefault(entity.getCampaignId(), ConstantUtils.PLACEHOLDER_2));
            entity.setPlanName(planMap.getOrDefault(entity.getPlanId(), ConstantUtils.PLACEHOLDER_2));
            entity.setAdxName(adxMap.getOrDefault(entity.getAdxId(), ConstantUtils.PLACEHOLDER_2));
            entity.setEpName(epMap.getOrDefault(entity.getEpId(), ConstantUtils.PLACEHOLDER_2));
            entity.setRtaName(rtaMap.getOrDefault(entity.getRtaId(), ConstantUtils.PLACEHOLDER_2));
            entity.setSlotTypeName(slotTypeMap.getOrDefault(entity.getAdType(), ConstantUtils.PLACEHOLDER_2));
            entity.setFlowTypeName(flowTypeMap.getOrDefault(entity.getFlowType(), ConstantUtils.PLACEHOLDER_2));
            entity.setSlotName(slotMap.getOrDefault(entity.getSlotId(), ConstantUtils.PLACEHOLDER_2));
        }
    }

    /**
     * 根据list 获取map
     *
     * @param selectDTOS list 数据
     * @return 返回数据
     */
    private Map<Long, String> getMapByList(List<SelectDTO> selectDTOS) {
        if (null != selectDTOS) {
            return selectDTOS.stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
        }
        return new HashMap<>();
    }

    /**
     * 校验是否需要查询计划定向，如果聚合维度仅有包名，则返回true
     *
     * @param dimensions 聚合维度
     * @return 返回结果
     */
    private boolean isSearchPlanDirect(List<String> dimensions) {
        return dimensions.contains(AnalysisReportFieldEnum.PACKAGE.getKey()) && dimensions.size() == 1;
    }

    @Override
    public void exportReport(AnalysisReportListVO listVO, HttpServletResponse response) throws IOException {
        listVO.setIsDownload(true);
        List<AnalysisReportListDTO> reportList = this.listAnalysisReport(listVO).getData();

        if (reportList.isEmpty()) {
            throw new CustomException("无可下载数据");
        }
        // 获取用户自定义列数据
        List<String> headerKey = new ArrayList<>() {{
            // 插入聚合维度名称
            addAll(listVO.getDimensions());
            // 插入报表数据
            if (CollectionUtils.isNotEmpty(listVO.getCustoms())) {
                addAll(listVO.getCustoms());
            }
        }};
        List<SelectDTO2> headers = setHeader(listVO, headerKey);

        String fileName = this.getFileNameByDimensions(listVO.getDimensions()) + "_报表_";
        fileName += listVO.getStartDate().equals(listVO.getEndDate())
                ? listVO.getStartDate() : listVO.getStartDate() + "_" + listVO.getEndDate();
        ExcelUtils.download(response, fileName, "报表数据", reportList, headers);
    }

    @Override
    public ResultStatusEnum exportReportToPath(AnalysisReportListVO listVO) {
        listVO.setIsDownload(true);
        List<AnalysisReportListDTO> reportList = this.listAnalysisReport(listVO).getData();

        if (reportList.isEmpty()) {
            throw new CustomException("无可下载数据");
        }
        List<String> headerKey = new ArrayList<>() {{
            // 插入聚合纬度
            if (CollectionUtils.isNotEmpty(listVO.getDimensions())) {
                addAll(listVO.getDimensions());
            }
            // 插入报表数据
            if (CollectionUtils.isNotEmpty(listVO.getCustoms())) {
                addAll(listVO.getCustoms());
            }
        }};
        List<SelectDTO2> headers = setHeader(listVO, headerKey);
        try {
            ExcelUtils.download(UploadUtils.getUploadPath(listVO.getFilePath()), "报表数据", reportList, headers);
            return ResultStatusEnum.SUCCESS;
        } catch (Exception exception) {
            throw new CustomException(4000, exception.getMessage());
        }
    }

    /**
     * 获取 维度名称-拼接
     *
     * @param dimensions 维度
     * @return 返回数据
     */
    private String getFileNameByDimensions(List<String> dimensions) {
        return dimensions.stream().map(dimension -> AnalysisReportFieldEnum.get(dimension).getName())
                .collect(Collectors.joining("_"));
    }

    /**
     * 设置header
     *
     * @param listVO    筛选数据
     * @param fieldsKey 字段
     * @return 返回数据
     */
    private List<SelectDTO2> setHeader(AnalysisReportListVO listVO, List<String> fieldsKey) {
        return reportService.findReportHeader(fieldsKey, listVO.getModule(), listVO.getIdentify(),
                AnalysisReportListDTO.class);
    }


    /**
     * 是否需要替换 dim ad type
     *
     * @param listVO 条件
     * @return 返回数据
     */
    private Boolean isReplaceDimAdType(AnalysisReportListVO listVO) {
        return isAdxFlowReport(listVO) || isFlowSsp(listVO);
    }

    /**
     * 是否流量类型
     *
     * @param listVO 列表
     * @return 结果
     */
    private Boolean isAdxFlowReport(AnalysisReportListVO listVO) {
        return Objects.equals(ReportTypeEnum.ADX.getId(), listVO.getReportType());
    }

    /**
     * 是否 flow ssp
     *
     * @param listVO 筛选条件
     * @return 返回数据
     */
    private Boolean isFlowSsp(AnalysisReportListVO listVO) {
        return listVO.getReportType().equals(ReportTypeEnum.FLOW_SSP_PKG.getId()) &&
                !listVO.getDimensions().contains(AnalysisReportFieldEnum.PACKAGE.getKey());
    }
}

