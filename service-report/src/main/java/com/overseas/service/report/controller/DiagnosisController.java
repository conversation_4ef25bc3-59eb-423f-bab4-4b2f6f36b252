package com.overseas.service.report.controller;

import com.overseas.common.dto.R;
import com.overseas.common.vo.report.diagnosis.DiagnosisDetailInfoChartVO;
import com.overseas.common.vo.report.diagnosis.DiagnosisDetailInfoVO;
import com.overseas.common.vo.report.diagnosis.DiagnosisDetailListVO;
import com.overseas.common.vo.report.diagnosis.DiagnosisPutInfoVO;
import com.overseas.common.vo.report.flow.center.overview.FlowCenterOverviewVO;
import com.overseas.service.report.service.DiagnosisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 **/
@RestController
@Slf4j
@RequiredArgsConstructor
@Api(tags = "诊断接口")
@RequestMapping("/report/diagnosis")
public class DiagnosisController {

    private final DiagnosisService diagnosisService;

    @PostMapping("/put/info")
    @ApiOperation(value = "数据概览-投放信息")
    public R putInfo(@RequestBody @Validated DiagnosisPutInfoVO putInfoVO) {
        return R.data(diagnosisService.putInfo(putInfoVO));
    }

    @PostMapping("/put/detail")
    @ApiOperation(value = "数据概览-投放信息详情")
    public R putDetail(@RequestBody @Validated FlowCenterOverviewVO overviewVO) {
        return R.data(diagnosisService.putDetail(overviewVO));
    }

    @PostMapping("/put/detail/chart")
    @ApiOperation(value = "数据概览-投放信息详情")
    public R putDetail(@RequestBody @Validated DiagnosisDetailInfoChartVO chartVO) {
        return R.data(diagnosisService.putDetailChart(chartVO));
    }


    @PostMapping("/detail/list")
    public R detailList(@RequestBody @Validated DiagnosisDetailListVO listVO) {
        return R.data(diagnosisService.detailList(listVO));
    }

    @PostMapping("/detail/info")
    @ApiOperation(value = "详细信息")
    public R detailInfo(@RequestBody @Validated DiagnosisDetailInfoVO infoVO) {
        return R.data(diagnosisService.detailInfo(infoVO));
    }


}
