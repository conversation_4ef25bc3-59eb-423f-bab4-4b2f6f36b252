package com.overseas.service.report.feign;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.market.adx.AdxGetDTO;
import com.overseas.common.dto.market.asset.AssetBaseDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitAllDataListDTO;
import com.overseas.common.dto.market.master.MasterProjectDTO;
import com.overseas.common.dto.market.master.MasterTimeZoneDTO;
import com.overseas.common.dto.market.plan.PlanByInfoGetDTO;
import com.overseas.common.dto.market.plan.PlanDirectValueDTO;
import com.overseas.common.dto.market.plan.PlanUpdateRecordListDTO;
import com.overseas.common.dto.market.reportField.ReportFieldDTO;
import com.overseas.common.dto.market.reportNote.ReportNoteDTO;
import com.overseas.common.dto.market.reportTask.ReportExportTaskDTO;
import com.overseas.common.dto.market.rtaStrategy.RtaCountryDTO;
import com.overseas.common.dto.market.rtaStrategy.RtaStrategyListDTO;
import com.overseas.common.utils.FeignR;
import com.overseas.common.vo.market.adx.AdxGetVO;
import com.overseas.common.vo.market.asset.AssetGetVO;
import com.overseas.common.vo.market.asset.AssetNewIdsVO;
import com.overseas.common.vo.market.asset.AssetSearchGetVO;
import com.overseas.common.vo.market.asset.assetLabel.AssetLabelSelectGetVO;
import com.overseas.common.vo.market.campaign.CampaignSelectGetVO;
import com.overseas.common.vo.market.channel.ChannelSelectVO;
import com.overseas.common.vo.market.creative.unit.CreativeUnitFillSizeVO;
import com.overseas.common.vo.market.master.MasterProjectGetVO;
import com.overseas.common.vo.market.master.MasterSelectByInfoVO;
import com.overseas.common.vo.market.master.MasterTimeZoneGetVO;
import com.overseas.common.vo.market.monitor.monitorEvent.MonitorEventIdsGetVO;
import com.overseas.common.vo.market.plan.*;
import com.overseas.common.vo.market.plan.direct.AdxSelectGetVO;
import com.overseas.common.vo.market.plan.direct.EpSelectGetVO;
import com.overseas.common.vo.market.plan.direct.RtaSelectGetVO;
import com.overseas.common.vo.market.reportField.ReportFieldGetVO;
import com.overseas.common.vo.market.reportNote.ReportNoteListVO;
import com.overseas.common.vo.market.reportTask.ReportExportTaskGetVO;
import com.overseas.common.vo.market.reportTask.ReportExportTaskUpdateVO;
import com.overseas.common.vo.market.slot.SlotSelectGetVO;
import com.overseas.common.vo.report.ReportListVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient("ai-overseas-service-market")
public interface FgMarketService {

    @PostMapping("/market/creatives/report/list")
    FeignR<List<CreativeUnitAllDataListDTO>> getCreativeAllDataList(ReportListVO listVO);

    @PostMapping("/market/campaigns/select")
    FeignR<List<SelectDTO>> selectCampaign(CampaignSelectGetVO getVO);

    @PostMapping("/market/campaigns/noPermission/select")
    FeignR<List<SelectDTO>> selectNoPermissionCampaign(CampaignSelectGetVO getVO);

    @PostMapping("/market/plans/select")
    FeignR<List<SelectDTO>> selectPlan(PlanSelectGetVO getVO);

    @PostMapping("/market/plans/get/plan/by/info")
    FeignR<List<PlanByInfoGetDTO>> getPlanByInfo(PlanByInfoGetVO getVO);

    @PostMapping("/market/plans/report/direct/get")
    FeignR<List<PlanDirectValueDTO>> getPlanDirect(PlanDirectGetVO getVO);

    @PostMapping("/market/plans/ids/get")
    FeignR<List<Long>> getPlanIds(PlanIdsGetVO getVO);

    @PostMapping("/market/plans/slotType/select")
    FeignR<List<SelectDTO>> selectSlotType();

    @PostMapping("/market/resources/adx/selectWorkable")
    FeignR<List<SelectDTO>> selectAdx();

    @PostMapping("/market/assets/get")
    FeignR<List<SelectDTO>> listAsset(AssetGetVO getVO);

    @PostMapping("/market/assets/assetInfo/get")
    FeignR<Map<Long, AssetBaseDTO>> getAssetInfoByIds(AssetGetVO getVO);

    @PostMapping("/market/plans/autoLink/list")
    FeignR<List<Long>> listAutoLinkPlan(AutoLinkPlanListVO listVO);

    @PostMapping("/market/assets/search/get")
    FeignR<List<Long>> getAssetIdsBySearch(AssetSearchGetVO getVO);

    @PostMapping("/market/planDirects/dock/adx/select")
    FeignR<List<SelectDTO>> getDockingAdxSelect();

    @PostMapping("/market/adx/get")
    FeignR<AdxGetDTO> getAdx(AdxGetVO getVO);

    @PostMapping("/market/rtaStrategies/select")
    FeignR<List<SelectDTO>> selectRtaStrategy(RtaSelectGetVO getVO);

    @PostMapping("/market/planDirects/ep/select")
    FeignR<List<SelectDTO>> selectEp(EpSelectGetVO getVO);

    @PostMapping("/market/rtaStrategies/ae/list")
    FeignR<List<RtaStrategyListDTO>> listAeRtaStrategy();

    @PostMapping("/market/plans/by/template/list")
    FeignR<List<Long>> listPlanByTemplate(PlanListByTemplateGetVO listVO);

    @PostMapping("/market/masters/select")
    FeignR<List<SelectDTO>> selectMaster();

    @PostMapping("/market/masters/select/by/info")
    FeignR<List<SelectDTO>> selectMasterByInfo(MasterSelectByInfoVO byInfoVO);

    @PostMapping("/market/masters/all/select")
    FeignR<List<SelectDTO>> selectAllMaster();

    @PostMapping("/market/reportFields/list")
    FeignR<List<ReportFieldDTO>> listReportField(ReportFieldGetVO getVO);

    @PostMapping("/market/rtaStrategies/byId/list")
    FeignR<List<RtaStrategyListDTO>> listRtaStrategyById(RtaSelectGetVO getVO);

    @PostMapping("/market/rtaStrategies/all/byId/list")
    FeignR<List<RtaStrategyListDTO>> listRtaStrategyAllById(RtaSelectGetVO getVO);

    @PostMapping("/market/reportNotes/list")
    FeignR<List<ReportNoteDTO>> listReportNote(ReportNoteListVO listVO);

    @PostMapping("/market/masters/timezone/get")
    FeignR<List<MasterTimeZoneDTO>> getMasterTimeZone(MasterTimeZoneGetVO getVO);

    @PostMapping("/market/masters/project/get")
    FeignR<List<MasterProjectDTO>> listMasterProject(MasterProjectGetVO getVO);

    @PostMapping("/market/plans/updateRecord/list")
    FeignR<List<PlanUpdateRecordListDTO>> listPlanUpdateRecord(PlanUpdateRecordListVO listVO);

    @PostMapping("/market/rtaStrategies/country/get")
    FeignR<List<RtaCountryDTO>> listRtaCountry();

    // 离线任务
    @PostMapping("/market/reportTasks/waiting/get")
    FeignR<ReportExportTaskDTO> getWaitToCreateTask(ReportExportTaskGetVO getVO);

    @PostMapping("/market/reportTasks/update")
    void updateReportTask(ReportExportTaskUpdateVO updateVO);

    @PostMapping("/market/monitorEvents/eventIds/get")
    FeignR<List<Long>> listMonitorEventIdsByAction(MonitorEventIdsGetVO getVO);

    @PostMapping("/market/assets/new/ids")
    FeignR<List<Long>> newAssetIds(AssetNewIdsVO assetNewIdsVO);

    @PostMapping("/market/planDirects/adx/select")
    FeignR<List<SelectDTO>> selectAdx(AdxSelectGetVO getVO);

    @PostMapping("/market/assets/label/select")
    FeignR<List<SelectDTO>> assetLabelSelect(AssetLabelSelectGetVO getVO);

    @PostMapping("/market/rtaStrategies/group/select")
    FeignR<List<SelectDTO>> selectRtaGroup();

    @PostMapping("/market/channel/select")
    FeignR<List<SelectDTO3>> channelSelect(ChannelSelectVO selectVO);

    @PostMapping("/market/channel/select/key")
    FeignR<List<SelectDTO3>> channelSelectByKey(ChannelSelectVO selectVO);

    @PostMapping("/market/resources/slot/select")
    FeignR<List<SelectDTO>> getSlotSelect(@RequestBody SlotSelectGetVO getVO);

    @PostMapping("/market/creatives/fill/size/creative/unit")
    FeignR<List<Long>> fillSizeToCreativeUnitId(@RequestBody CreativeUnitFillSizeVO fillSizeVO);

}
