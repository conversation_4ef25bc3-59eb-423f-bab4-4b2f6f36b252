package com.overseas.service.report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.market.master.MasterTimeZoneDTO;
import com.overseas.common.dto.report.diagnosis.DiagnosisDetailInfoDTO;
import com.overseas.common.dto.report.diagnosis.DiagnosisPutInfoDTO;
import com.overseas.common.dto.report.diagnosis.DiagnosisReportDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterCommonListDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterDiagnosisDTO;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.report.flow.center.FlowCenterDataEnum;
import com.overseas.common.enums.report.flow.center.FlowCenterDimensionEnum;
import com.overseas.common.enums.report.flow.center.FlowCenterLevelEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.DoubleUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.vo.market.master.MasterTimeZoneGetVO;
import com.overseas.common.vo.report.diagnosis.DiagnosisDetailInfoChartVO;
import com.overseas.common.vo.report.diagnosis.DiagnosisDetailInfoVO;
import com.overseas.common.vo.report.diagnosis.DiagnosisDetailListVO;
import com.overseas.common.vo.report.diagnosis.DiagnosisPutInfoVO;
import com.overseas.common.vo.report.flow.center.FlowCenterReportListVO;
import com.overseas.common.vo.report.flow.center.detail.FlowCenterDetailListVO;
import com.overseas.common.vo.report.flow.center.overview.FlowCenterOverviewVO;
import com.overseas.service.report.feign.FgMarketService;
import com.overseas.service.report.mapper.CreativeUnitHourMapper;
import com.overseas.service.report.mapper.PlanHourMapper;
import com.overseas.service.report.service.DiagnosisService;
import com.overseas.service.report.service.FlowCenterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@RequiredArgsConstructor
@Slf4j
@Service
public class DiagnosisServiceImpl implements DiagnosisService {

    private final FgMarketService fgMarketService;

    private final PlanHourMapper planHourMapper;

    private final CreativeUnitHourMapper creativeUnitHourMapper;

    private final FlowCenterService flowCenterService;


    @Override
    public DiagnosisPutInfoDTO putInfo(DiagnosisPutInfoVO putInfoVO) {
        List<MasterTimeZoneDTO> timeZone = fgMarketService.getMasterTimeZone(MasterTimeZoneGetVO.builder()
                .masterId(putInfoVO.getMasterId().longValue()).timeZone(101).build()
        ).getData();
        if (CollectionUtils.isEmpty(timeZone)) {
            throw new CustomException("账户未设置时区");
        }
        String today = DateUtils.format(new Date());
        int todayHour = DateUtils.getHour();
        Date todayDate = DateUtils.string2Date(today);
        long start = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(todayDate, timeZone.get(0).getTimeZone(), 0));
        long end = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(todayDate, timeZone.get(0).getTimeZone(), todayHour));

        List<Long> dimDay = List.of(DateUtils.date2Long(DateUtils.afterDay(todayDate, -1)),
                DateUtils.date2Long(todayDate),
                DateUtils.date2Long(DateUtils.afterDay(todayDate, 1)));
        List<DiagnosisReportDTO> planInfos = planHourMapper.planDiagnosis(new QueryWrapper<>()
                .eq("fph.dim_master_id", putInfoVO.getMasterId())
                .eq("fph.dim_agent_id", timeZone.get(0).getAgentId())
                .in("fph.dim_day", dimDay)
                .between("fph.dim_report_hour", start, end)
                .groupBy("mp.plan_status, mc.campaign_status")
        );
        Map<Integer, DiagnosisPutInfoDTO.InfoNum> planInfoMap = new HashMap<>() {{
            put(2, new DiagnosisPutInfoDTO.InfoNum());
            put(3, new DiagnosisPutInfoDTO.InfoNum());
        }};
        planInfos.forEach(u -> {
            if (u.getCampaignStatus().equals(2) && u.getPlanStatus().equals(2)) {
                addInfo(planInfoMap.get(2), u);
                return;
            }
            addInfo(planInfoMap.get(3), u);
        });
        List<DiagnosisReportDTO> creativeUnitInfo = creativeUnitHourMapper.creativeUnitDiagnosis(new QueryWrapper<>()
                .eq("fcuh.dim_master_id", putInfoVO.getMasterId())
                .eq("fcuh.dim_agent_id", timeZone.get(0).getAgentId())
                .in("fcuh.dim_day", dimDay)
                .between("fcuh.dim_report_hour", start, end)
                .groupBy(" mcu.creative_unit_status, mp.plan_status, mc.campaign_status")
        );
        Map<Integer, DiagnosisPutInfoDTO.InfoNum> creativeUnitInfoMap = new HashMap<>() {{
            put(2, new DiagnosisPutInfoDTO.InfoNum());
            put(3, new DiagnosisPutInfoDTO.InfoNum());
        }};
        creativeUnitInfo.forEach(u -> {
            if (u.getCampaignStatus().equals(2) && u.getPlanStatus().equals(2) && u.getCreativeUnitStatus().equals(2)) {
                addInfo(creativeUnitInfoMap.get(2), u);
                return;
            }
            addInfo(creativeUnitInfoMap.get(3), u);
        });
        DiagnosisPutInfoDTO putInfoDTO = new DiagnosisPutInfoDTO();
        putInfoDTO.setPutPlan(planInfoMap.get(2));
        putInfoDTO.setStopPlan(planInfoMap.get(3));
        putInfoDTO.setPutCreativeUnit(creativeUnitInfoMap.get(2));
        putInfoDTO.setStopCreativeUnit(creativeUnitInfoMap.get(3));
        return putInfoDTO;
    }

    @Override
    public FlowCenterDiagnosisDTO putDetail(FlowCenterOverviewVO overviewVO) {
        Integer timezone = this.getTimeZone(overviewVO.getMasterId());
        Date start = TimeZoneEnum.getTimeZoneDate(DateUtils.string2Date(overviewVO.getStartDate()),
                timezone, DateUtils.getHour());
        overviewVO.setStartDate(DateUtils.format(start, DateUtils.DATE_PATTERN));
        overviewVO.setEndDate(overviewVO.getStartDate());
        overviewVO.setTimezone(timezone);
        return flowCenterService.overviewDiagnosis(overviewVO);
    }

    @Override
    public MultiIndexChartDTO putDetailChart(DiagnosisDetailInfoChartVO chartVO) {
        List<String> ranges, lastRange;
        switch (chartVO.getDayOf()) {
            case "day":
                ranges = DateUtils.getBetweenDate(chartVO.getStart(), chartVO.getEnd(), DateUtils.DATE_PATTERN);
                DateUtils.Cycle cycle = DateUtils.lastCycle(chartVO.getStart(), chartVO.getEnd());
                lastRange = DateUtils.getBetweenDate(DateUtils.format(cycle.getStart()), DateUtils.format(cycle.getEnd()), DateUtils.DATE_PATTERN);
                break;
            case "hour":
                String today = DateUtils.format(new Date());
                if (today.equals(chartVO.getStart())) {
                    ranges = DateUtils.getBetweenHour(DateUtils.format(new Date()));
                } else {
                    ranges = DateUtils.getBetweenHour(chartVO.getStart());
                }
                lastRange = ranges;
                break;
            default:
                throw new CustomException("时间维度不合法");
        }
        return this.flowCenterService.completeChart(chartVO.getCurrent(), chartVO.getLast(),
                ranges, lastRange, chartVO.getDayOf(),
                Map.of(chartVO.getField(), "数量")).get(0).getData();
    }

    @Override
    public List<FlowCenterCommonListDTO> detailList(DiagnosisDetailListVO listVO) {
        Integer timezone = this.getTimeZone(listVO.getMasterId());
        FlowCenterDetailListVO detailListVO = new FlowCenterDetailListVO();
        detailListVO.setMasterIds(List.of(listVO.getMasterId().longValue()));
        Date start = TimeZoneEnum.getTimeZoneDate(DateUtils.string2Date(listVO.getDate()),
                timezone, ObjectUtils.isNotNullOrZero(listVO.getHour()) ? listVO.getHour() : DateUtils.getHour());
        detailListVO.setStartDate(DateUtils.format(start, DateUtils.DATE_PATTERN));
        detailListVO.setEndDate(detailListVO.getStartDate());
        if (null != listVO.getHour()) {
            detailListVO.setHour(DateUtils.getDateHour(start, 0));
        }
        detailListVO.setTimeZone(timezone);
        detailListVO.setDimensions(List.of(FlowCenterDimensionEnum.PLAN.getKey()));
        detailListVO.setSortField(FlowCenterDataEnum.BID_FAILED.getField());
        detailListVO.setSortType("desc");
        return flowCenterService.detailList(detailListVO);
    }

    @Override
    public DiagnosisDetailInfoDTO detailInfo(DiagnosisDetailInfoVO infoVO) {
        Integer timezone = this.getTimeZone(infoVO.getMasterId());
        FlowCenterReportListVO listVO = new FlowCenterReportListVO();
        if (ObjectUtils.isNotNullOrZero(infoVO.getPid())) {
            listVO.setPlanIds(List.of(infoVO.getPid()));
            listVO.setLevel(FlowCenterLevelEnum.PLAN.getName());
        }
        if (ObjectUtils.isNotNullOrZero(infoVO.getUniqId())) {
            listVO.setAdvertIds(List.of(infoVO.getUniqId()));
            listVO.setLevel(FlowCenterLevelEnum.ADVERT.getName());
        }
        if (StringUtils.isBlank(listVO.getLevel())) {
            throw new CustomException("诊断信息条件不完整，请确认后再试");
        }
        Date start = TimeZoneEnum.getTimeZoneDate(DateUtils.string2Date(infoVO.getDate()),
                timezone, ObjectUtils.isNotNullOrZero(infoVO.getHour()) ? infoVO.getHour() : DateUtils.getHour());
        listVO.setStart(DateUtils.format(start, DateUtils.DATE_PATTERN));
        listVO.setEnd(listVO.getStart());
        if (null != infoVO.getHour()) {
            listVO.setStartHour(DateUtils.getDateHour(start, 0));
            listVO.setEndHour(listVO.getStartHour());
        }
        listVO.setTimezone(timezone);
        FlowCenterDiagnosisDTO diagnosis = flowCenterService.getDiagnosisData(listVO);
        DiagnosisDetailInfoDTO diagnosisDetailInfoDTO = new DiagnosisDetailInfoDTO();
        diagnosisDetailInfoDTO.setBidSuccess(diagnosis.getBidSuccess());
        diagnosisDetailInfoDTO.setTotalReq(diagnosis.getTotalRequest());
        diagnosisDetailInfoDTO.setTotalBid(diagnosis.getTotalBid());
        long sum = diagnosisDetailInfoDTO.getTotalReq() - diagnosis.getTotalBid();
        diagnosisDetailInfoDTO.setDiagnoses(
                diagnosis.getDiagnoses().stream().flatMap(diag -> diag.getSubDiagnoses().stream())
                        .peek(u -> u.setFilterRatio(DoubleUtils.getRate(BigDecimal.valueOf(u.getFilterNum()), BigDecimal.valueOf(sum)).floatValue()))
                        .sorted(Comparator.comparing(FlowCenterDiagnosisDTO.SubDiagnose::getFilterNum)).
                        collect(Collectors.toList())
        );
        return diagnosisDetailInfoDTO;
    }

    /**
     * 添加数据
     *
     * @param info   info
     * @param report 报表数据
     */
    private void addInfo(DiagnosisPutInfoDTO.InfoNum info, DiagnosisReportDTO report) {
        info.setNum(info.getNum() + report.getNum());
        info.setCost(info.getCost().add(report.getCost()));
    }

    /**
     * 获取用户时区
     *
     * @param masterId 账户ID
     * @return 返回数据
     */
    private Integer getTimeZone(Integer masterId) {
        List<MasterTimeZoneDTO> timeZone = fgMarketService.getMasterTimeZone(MasterTimeZoneGetVO.builder()
                .masterId(masterId.longValue()).timeZone(101).build()
        ).getData();
        if (CollectionUtils.isEmpty(timeZone)) {
            throw new CustomException("账户未设置时区");
        }
        return timeZone.get(0).getTimeZone();
    }


}
