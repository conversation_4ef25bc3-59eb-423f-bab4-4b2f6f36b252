package com.overseas.service.report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.report.AssetReportListDTO;
import com.overseas.common.dto.report.asset.AssetCombineReportListDTO;
import com.overseas.common.entity.User;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.report.AssetChartListVO;
import com.overseas.common.vo.report.AssetCompareListVO;
import com.overseas.common.vo.report.AssetReportListVO;
import com.overseas.common.vo.report.asset.*;
import com.overseas.service.report.entity.AssetDay;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AssetReportService extends IService<AssetDay> {

    /**
     * 获取素材报表数据
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    IPage<AssetReportListDTO> getAssetReportList(AssetReportListVO listVO, User user);

    /**
     * 获取素材报表数据
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    PageUtils<AssetReportListDTO> getAssetReportPage(AssetReportListVO listVO, User user);

    /**
     * 时间对比报表列表税局
     *
     * @param listVO 筛选条件
     * @return 返回数据
     */
    List<AssetReportListDTO> getAssetCompareList(AssetCompareListVO listVO);

    /**
     * 导出时间对比下载
     *
     * @param listVO   条件
     * @param response response
     * @throws IOException 异常
     */
    void exportAssetCompareList(AssetCompareListVO listVO, HttpServletResponse response) throws IOException;

    /**
     * 获取素材趋势图
     *
     * @param listVO 条件
     * @return 返回数据
     */
    MultiIndexChartDTO getAssetReportChart(AssetChartListVO listVO);

    /**
     * 导出素材list
     *
     * @param listVO   listVo
     * @param response response
     */

    void exportAssetList(AssetReportListVO listVO, User user, HttpServletResponse response);

    /**
     * 获取素材趋势图数据
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    MultiIndexChartDTO getAssetChart(AssetChartGetVO getVO);

    /**
     * 更新素材ID设计师用户
     *
     * @param reportDate 天数据
     */
    void updateAssetUserId(Long reportDate);

    /**
     * 获取素材ID
     *
     * @return 素材ID
     */
    List<Long> getAssetIds();

    /**
     * 素材 bar 数据
     *
     * @param assetBarVO 条件
     * @return 返回数据
     */
    PageUtils<?> assetBar(AssetBarVO assetBarVO);

    /**
     * 素材 top list
     *
     * @param assetTopVO 条件
     * @return 返回数据
     */
    PageUtils<?> assetTop(AssetTopVO assetTopVO);

    /**
     * 组合数据获取
     *
     * @param listVO 列表数据
     * @param user   用户数据
     * @return 返回数据
     */
    PageUtils<AssetCombineReportListDTO> combineList(AssetCombineListVO listVO, User user);


    /**
     * 单字段数据获取
     *
     * @param listVO 条件
     * @param user   用户
     * @return 返回数据
     */
    PageUtils<AssetCombineReportListDTO> fieldList(AssetFieldListVO listVO, User user);

}
