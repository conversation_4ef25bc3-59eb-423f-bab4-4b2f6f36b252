package com.overseas.service.report.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.report.*;
import com.overseas.common.dto.report.asset.AssetCombineReportListDTO;
import com.overseas.common.dto.report.rta.RtaReportListDTO;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.cps.recycle.CpsRecycleListVO;
import com.overseas.common.vo.report.*;
import com.overseas.common.vo.report.asset.*;
import com.overseas.common.vo.report.assetLabel.AssetLabelListVO;
import com.overseas.common.vo.report.assetLabel.AssetLabelTrendVO;
import com.overseas.common.vo.report.flow.FlowReportListVO;
import com.overseas.common.vo.report.flow.FlowReportSizeSelectVO;
import com.overseas.common.vo.report.flow.FlowReportTopListVO;
import com.overseas.common.vo.report.plan.PlanHourReportListVO;
import com.overseas.common.vo.report.revenue.*;
import com.overseas.common.vo.report.rta.RtaReportListExportVO;
import com.overseas.common.vo.report.rta.RtaReportListVO;
import com.overseas.common.vo.report.rta.RtaReportTrendyVO;
import com.overseas.service.report.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@Api(tags = "report-报表相关接口")
@RestController
@RequestMapping("/report")
@RequiredArgsConstructor
@Slf4j
public class ReportController extends AbstractController {

    private final ReportService reportService;

    private final AssetReportService assetReportService;

    private final AnalysisReportService analysisReportService;

    private final FlowReportService flowReportService;

    private final ReportScheduleService reportScheduleService;

    private final AggregatedReportService aggregatedReportService;

    private final RtaReportService rtaReportService;

    private final AssetLabelReportService assetLabelReportService;

    private final TrackingGroupService trackingGroupService;

    @ApiOperation(value = "获取趋势图数据", produces = "application/json", response = MultiIndexChartDTO.class)
    @PostMapping("/chart")
    public R getTrendChart(@Validated @RequestBody ReportListVO listVO) {
        return R.data(this.reportService.getTrendChart(listVO, this.getUserNoException()));
    }

    @ApiOperation(value = "获取报表列表数据", produces = "application/json", response = CreativeHourReportListDTO.class)
    @PostMapping("/list")
    public R getReportPageList(@Validated @RequestBody ReportListVO listVO) {
        return R.page(this.reportService.getReportPageList(listVO, this.getUserNoException()));
    }

    @ApiOperation(value = "获取报表列表数据（自定义列项目标识）", produces = "application/json",
            response = CreativeHourReportListDTO.class)
    @PostMapping("/list/identify")
    public R getReportPageListIdentify(@Validated @RequestBody ReportListVO listVO) {
        return R.data(this.reportService.getReportPageListIdentify(listVO, this.getUserNoException()));
    }

    @ApiOperation(value = "通过ID获取报表列表数据（不查询投放数据、汇总数据）", produces = "application/json",
            response = CreativeHourReportListDTO.class)
    @PostMapping("/report/list")
    public R getReportList(@Validated @RequestBody ReportListVO listVO) {
        return R.page(new PageUtils<>(this.reportService.getReportList(listVO, true,
                listVO.getSearchIds(), this.getUserNoException())));
    }

    @ApiOperation(value = "获取报表汇总数据", produces = "application/json", response = CreativeHourReportListDTO.class)
    @PostMapping("/total")
    public R totalReport(@Validated @RequestBody ReportListVO listVO) {
        return R.data(this.reportService.getSummaryData(listVO, true, listVO.getSearchIds(),
                this.getUserNoException()));
    }

    @ApiOperation(value = "导出报表数据", produces = "application/json")
    @PostMapping("/export")
    public void exportReport(@RequestBody @Validated ReportListVO listVO, HttpServletResponse response) {
        this.reportService.download(listVO, this.getUserNoException(), response);
    }

    @ApiOperation(value = "获取含有消费的指定数据信息", produces = "application/json")
    @PostMapping("/ids/get")
    public R getHasCostIds(@Validated @RequestBody ReportHasCostGetVO getVO) {
        return R.data(this.reportService.getHasCostIds(getVO));
    }

    @PostMapping("/records/list")
    public R listHasCostRecord(@Validated @RequestBody ReportHasCostListVO listVO) {
        return R.data(this.reportService.listHasCostRecord(listVO));
    }

    @ApiOperation(value = "获取素材趋势图数据", produces = "application/json", response = MultiIndexChartDTO.class)
    @PostMapping("/assets/chart")
    public R getAssetChart(@Validated @RequestBody AssetChartGetVO getVO) {
        return R.data(this.assetReportService.getAssetChart(getVO));
    }

    @ApiOperation(value = "获取素材bar数据", produces = "application/json", response = AssetBarDTO.class)
    @PostMapping("/asset/bar")
    public R getAssetBar(@RequestBody @Validated AssetBarVO assetBarVO) {
        return R.page(this.assetReportService.assetBar(assetBarVO));
    }

    @ApiOperation(value = "获取素材top数据", produces = "application/json", response = AssetReportListDTO.class)
    @PostMapping("/asset/top")
    public R getAssetTop(@RequestBody @Validated AssetTopVO assetTopVO) {
        return R.page(this.assetReportService.assetTop(assetTopVO));
    }

    @ApiOperation(value = "获取素材报表分页数据", notes = "获取素材报表分页数据", produces = "application/json",
            response = AssetReportListDTO.class)
    @PostMapping("/assets/list")
    public R getAssetReportPage(@Validated @RequestBody AssetReportListVO listVO) {
        return R.page(this.assetReportService.getAssetReportPage(listVO, this.getUserNoException()));
    }

    @ApiOperation(value = "获取素材报表趋势图", produces = "application/json", response = AssetCombineReportListDTO.class)
    @PostMapping("/asset/combine/list")
    public R getAssetCombineList(@Validated @RequestBody AssetCombineListVO listVO) {
        return R.page(this.assetReportService.combineList(listVO, this.getUser()));
    }

    @ApiOperation(value = "获取素材报表趋势图", produces = "application/json", response = AssetCombineReportListDTO.class)
    @PostMapping("/asset/field/list")
    public R getAssetFieldList(@Validated @RequestBody AssetFieldListVO listVO) {
        return R.page(this.assetReportService.fieldList(listVO, this.getUser()));
    }

    @ApiOperation(value = "获取素材报表趋势图", produces = "application/json", response = MultiIndexChartDTO.class)
    @PostMapping("/assets/list/chart")
    public R getAssetChartList(@Validated @RequestBody AssetChartListVO listVO) {
        return R.data(this.assetReportService.getAssetReportChart(listVO));
    }

    @ApiOperation(value = "获取素材报表分页数据", notes = "获取素材报表分页数据", produces = "application/json",
            response = AssetReportListDTO.class)
    @PostMapping("/assets/report/list")
    public R getAssetReportList(@Validated @RequestBody AssetReportListVO listVO) {
        return R.page(new PageUtils<>(this.assetReportService.getAssetReportList(listVO, this.getUserNoException())));
    }

    @ApiOperation(value = "获取素材时间对比报表", produces = "application/json", response = AssetReportListDTO.class)
    @PostMapping("/assets/compare/list")
    public R getAssetCompareList(@Validated @RequestBody AssetCompareListVO listVO) {
        return R.data(this.assetReportService.getAssetCompareList(listVO));
    }

    @ApiOperation(value = "素材时间对比报表下载", produces = "application/json")
    @PostMapping("/assets/compare/export")
    public void exportAssetCompareList(@Validated @RequestBody AssetCompareListVO listVO, HttpServletResponse response)
            throws IOException {
        this.assetReportService.exportAssetCompareList(listVO, response);
    }

    @ApiOperation(value = "获取素材报表分页数据", notes = "获取素材报表分页数据", produces = "application/json",
            response = AssetReportListDTO.class)
    @PostMapping("/assets/export")
    public void exportAssetList(@Validated @RequestBody AssetReportListVO listVO, HttpServletResponse response) {
        this.assetReportService.exportAssetList(listVO, this.getUserNoException(), response);
    }

    @ApiOperation(value = "获取有投放数据的素材ID", produces = "application/json")
    @PostMapping("/assets/ids/get")
    public R getAssetIds() {
        return R.data(this.assetReportService.getAssetIds());
    }

    @ApiOperation(value = "素材标签趋势报表", produces = "application/json")
    @PostMapping("/assets/label/trend")
    public R assetLabelTrend(@RequestBody @Validated AssetLabelTrendVO trendVO) {
        return R.data(this.assetLabelReportService.assetLabelTrend(trendVO, this.getUser()));
    }

    @ApiOperation(value = "素材标签报表列表", produces = "application/json")
    @PostMapping("/assets/label/list")
    public R assetLabelList(@RequestBody @Validated AssetLabelListVO listVO) {
        return R.page(this.assetLabelReportService.assetLabelList(listVO, this.getUser()));
    }

    @ApiOperation(value = "素材标签报表列表下载", produces = "application/json")
    @PostMapping("/assets/label/list/export")
    public void exportAssetLabelList(@RequestBody @Validated AssetLabelListVO listVO, HttpServletResponse response)
            throws IOException {
        this.assetLabelReportService.exportAssetLabelList(response, listVO, this.getUser());
    }

    @ApiOperation(value = "获取内容分析报表图表数据", notes = "获取内容分析报表分页数据", produces = "application/json",
            response = AnalysisReportListDTO.class)
    @PostMapping("/analysis/chart")
    public R getAnalysisReportChart(@Validated @RequestBody AnalysisReportChartVO chartVO) {
        return R.data(this.analysisReportService.chartAnalysisReport(chartVO, this.getUser()));
    }

    @ApiOperation(value = "获取内容分析报表分页数据", notes = "获取内容分析报表分页数据", produces = "application/json",
            response = AnalysisReportListDTO.class)
    @PostMapping("/analysis/list")
    public R getAnalysisReportList(@Validated @RequestBody AnalysisReportListVO listVO) {
        return R.page(this.analysisReportService.listAnalysisReport(listVO));
    }

    @ApiOperation(value = "下载内容分析报表数据", notes = "下载内容分析报表数据", produces = "application/json")
    @PostMapping("/analysis/export")
    public void exportAnalysisReport(@Validated @RequestBody AnalysisReportListVO listVO, HttpServletResponse response)
            throws IOException {
        this.analysisReportService.exportReport(listVO, response);
    }

    @ApiOperation(value = "下载内容分析报表数据", notes = "下载内容分析报表数据", produces = "application/json")
    @PostMapping("/analysis/toPath/export")
    public R exportAnalysisReportToPath(@Validated @RequestBody AnalysisReportListVO listVO) {
        return R.data(this.analysisReportService.exportReportToPath(listVO));
    }

    @ApiOperation(value = "获取内容分析报表分页数据", notes = "获取内容分析报表分页数据", produces = "application/json",
            response = AnalysisReportListDTO.class)
    @PostMapping("/trackingGroup/list")
    public R getTrackingGroupReportList(@Validated @RequestBody TrackingGroupReportListVO listVO) {
        return R.page(this.trackingGroupService.listTrackingGroupReport(listVO));
    }

    @ApiOperation(value = "下载内容分析报表数据", notes = "下载内容分析报表数据", produces = "application/json")
    @PostMapping("/trackingGroup/export")
    public void exportTrackingGroupReport(@Validated @RequestBody TrackingGroupReportListVO listVO,
                                          HttpServletResponse response) throws IOException {
        this.trackingGroupService.exportReport(listVO, response);
    }

    @ApiOperation(value = "获取计划小时报表数据", produces = "application/json", response = ReportListDTO.class)
    @PostMapping("/plan/hour/list")
    public R getPlanHourReportList(@Validated @RequestBody PlanHourReportListVO listVO) {
        return R.data(this.reportService.getPlanHourReportList(listVO));
    }

    @ApiOperation(value = "获取流量查询报表数据", produces = "application/json", response = FlowReportListDTO.class)
    @PostMapping("/flow/list")
    public R listFlowReport(@Validated @RequestBody FlowReportListVO listVO) {
        return R.page(this.flowReportService.listFlowReport(listVO));
    }

    @ApiOperation(value = "获取流量查询报表Top级别数据", produces = "application/json", response = FlowReportListDTO.class)
    @PostMapping("/flow/top/list")
    public R getFlowSearchTopList(@Validated @RequestBody FlowReportTopListVO listVO) {
        return R.page(this.flowReportService.getFlowSearchTopList(listVO));
    }

    @ApiOperation(value = "获取流量查询趋势图数据", produces = "application/json", response = MultiIndexChartDTO.class)
    @PostMapping("/flow/chart")
    public R getFlowSearchChart(@Validated @RequestBody FlowReportListVO listVO) {
        return R.data(this.flowReportService.getFlowSearchReportChart(listVO));
    }

    @ApiOperation(value = "下载流量查询报表数据", produces = "application/json", response = FlowReportListDTO.class)
    @PostMapping("/flow/export")
    public void exportFlowReport(@Validated @RequestBody FlowReportListVO listVO, HttpServletResponse response)
            throws IOException {
        this.flowReportService.exportFlowReport(listVO, response);
    }

    @ApiOperation(value = "下载流量查询报表数据", produces = "application/json", response = FlowReportListDTO.class)
    @PostMapping("/flow/toPath/export")
    public R exportFlowReportToPath(@Validated @RequestBody FlowReportListVO listVO) {
        return R.data(this.flowReportService.exportFlowReportToPath(listVO));
    }

    @ApiOperation(value = "获取流量报表国家", produces = "application/json", response = SelectDTO3.class)
    @PostMapping("/flow/countries/select")
    public R listFlowReportCountry(@Validated @RequestBody FlowReportSizeSelectVO selectVO) {
        return R.data(this.flowReportService.selectCountryByReq(selectVO));
    }

    @ApiOperation(value = "获取流量报尺寸数据", produces = "application/json", response = SelectDTO3.class)
    @PostMapping("/flow/sizes/select")
    public R listFlowReport(@Validated @RequestBody FlowReportSizeSelectVO selectVO) {
        return R.data(this.flowReportService.selectSizeByDate(selectVO));
    }

    @ApiOperation(value = "获取用户账户消耗", produces = "application/json", response = UserCostDTO.class)
    @PostMapping("/user/cost/get")
    public R getUserCost(@Validated @RequestBody UserCostGetVO getVO) {
        return R.data(this.reportService.getUserCost(getVO));
    }

    @ApiOperation(value = "获取指定类型用户消耗信息", produces = "application/json")
    @PostMapping("/user/cost/list")
    public R listUserCost(@Validated @RequestBody UserCostListVO listVO) {
        return R.data(this.reportService.listUserCost(listVO));
    }

    @ApiOperation(value = "获取广告主计划概览数据", produces = "application/json", response = OverviewReportDTO.class)
    @PostMapping("/master/overview/get")
    public R getMasterOverviewReport(@Validated @RequestBody OverviewReportGetVO getVO) {
        return R.data(this.reportService.getOverviewReport(getVO, this.getUser()));
    }

    @ApiOperation(value = "获取广告主计划趋势图数据", produces = "application/json", response = MultiIndexChartDTO.class)
    @PostMapping("/master/chart/get")
    public R getOverviewTrendChart(@Validated @RequestBody OverviewChartGetVO getVO) {
        return R.data(this.reportService.getOverviewTrendChart(getVO, this.getUser()));
    }

    @ApiOperation(value = "获取趋势图数据", produces = "application/json", response = MultiIndexChartDTO.class)
    @PostMapping("/chart/get")
    public R getTrendChart(@Validated @RequestBody TrendChartGetVO getVO) {
        return R.data(this.reportService.getTrendChart(getVO));
    }

    @ApiOperation(value = "测试更新设计师账号ID")
    @PostMapping("/assets/userId/test/update")
    public R testUpdateAssetUserId() {
        this.assetReportService.updateAssetUserId(DateUtils.date2Long(DateUtils.getTodayDate()));
        return R.ok();
    }

    @ApiOperation(value = "获取DSP侧RTA数据", produces = "application/json")
    @PostMapping("/rta/list")
    public R listDspReportData(@Validated @RequestBody RevenueRtaReportListVO listVO) {
        return R.data(this.reportService.listDspReportData(listVO));
    }

    @ApiOperation(value = "获取某一维度数据总和", produces = "application/json")
    @PostMapping("/dimension/total")
    public R dimensionTotal(@Validated @RequestBody DimensionTotalVO dimensionTotalVO) {
        return R.data(this.reportService.dimensionTotal(dimensionTotalVO));
    }

    @ApiOperation(value = "获取某一监控数据指标", produces = "application/json")
    @PostMapping("/dimension/reset")
    public R dimensionRest(@Validated @RequestBody DimensionResetVO dimensionResetVO) {
        return R.data(this.reportService.dimensionReset(dimensionResetVO));
    }

    @ApiOperation(value = "测试离线导出任务定时器", produces = "application/json")
    @PostMapping("/task/export/schedule/test")
    public R testExportTask() {
        this.reportScheduleService.exportOfflineTask();
        return R.ok();
    }

    @ApiOperation(value = "测试离线导出任务定时器", produces = "application/json")
    @PostMapping("/task/export/pkg/schedule/test")
    public R exportOfflineIncludePkgTask() {
        this.reportScheduleService.exportOfflineIncludePkgTask();
        return R.ok();
    }

    @ApiOperation("测试素材报表数据同步")
    @PostMapping("/asset/day/report/insert")
    public R batchInsertAssetDayReport(@RequestParam(name = "day") String day) {
        if (StringUtils.isBlank(day)) {
            return R.error("时间不正确");
        }
        aggregatedReportService.batchInsertAssetDayReport(DateUtils.string2Long(day));
        return R.ok();
    }

    @ApiOperation(value = "获取RTA通过率列表", produces = "application/json", response = RtaReportListDTO.class)
    @PostMapping("/rta/pass/list")
    public R listRtaPass(@Validated @RequestBody RtaReportListVO listVO) {
        return R.page(this.rtaReportService.passList(listVO));
    }

    @ApiOperation(value = "获取RTA通过率趋势图", produces = "application/json", response = MultiIndexChartDTO.class)
    @PostMapping("/rta/pass/trendy")
    public R trendyRtaPass(@Validated @RequestBody RtaReportTrendyVO trendyVO) {
        return R.data(this.rtaReportService.passTrendy(trendyVO));
    }

    @ApiOperation(value = "获取RTA通过率列表", produces = "application/json", response = RtaReportListDTO.class)
    @PostMapping("/rta/pass/list/export")
    public void exportListRtaPass(@Validated @RequestBody RtaReportListExportVO listVO, HttpServletResponse response)
            throws IOException {
        this.rtaReportService.exportPassList(listVO, response);
    }

    @ApiOperation(value = "获取RTA通过率列表", produces = "application/json", response = RtaReportListDTO.class)
    @PostMapping("/rta/pass/data")
    public R rtaPassData(@RequestParam("start") Long start, @RequestParam("end") Long end) {
        if (ObjectUtils.isNullOrZero(start) || ObjectUtils.isNullOrZero(end)) {
            return R.ok();
        }
        return R.data(this.rtaReportService.passData(start, end));
    }

    @ApiOperation(value = "获取CPS ROI报表花费", produces = "application/json", response = Map.class)
    @PostMapping("/cps/recycle/cost")
    public R recycleCost(@Validated @RequestBody CpsRecycleListVO listVO) {
        return R.data(this.reportService.recycleCost(listVO));
    }

    @ApiOperation(value = "根据花费排序获取下拉", produces = "application/json", response = SelectDTO.class)
    @PostMapping("/select/sort/by/cost")
    public R selectSortByCost(@Validated @RequestBody SelectSortByCostVO sortByCostVO) {
        return R.data(this.reportService.selectSortByCost(sortByCostVO));
    }
}
