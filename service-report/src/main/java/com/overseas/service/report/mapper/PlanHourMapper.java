package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.*;
import com.overseas.common.dto.report.diagnosis.DiagnosisReportDTO;
import com.overseas.common.dto.report.openApi.ae.DailyCostListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.PlanHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface PlanHourMapper extends BaseMapper<PlanHour> {

    /**
     * 获取某个小时有花费的计划数据集合
     *
     * @param wrapper 查询参数
     * @return 数据结果
     */
    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_hour ${ew.customSqlSegment}")
    List<ReportListDTO> getPlanHourReportList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_hour ${ew.customSqlSegment}")
    IPage<CreativeHourReportListDTO> getReportPageList(IPage<CreativeHourReportListDTO> page,
                                                       @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT * from (SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_hour ${ew.customSqlSegment}) report ${customFieldsSql}")
    IPage<CreativeHourReportListDTO> getReportPageList2(IPage<CreativeHourReportListDTO> page,
                                                        @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                        @Param("customFieldsSql") String customFieldsSql);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_hour ${ew.customSqlSegment}")
    CreativeHourReportListDTO getTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT ${finalSelectSql} from (SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_hour ${ew.customSqlSegment}) report ${customFieldsSql}")
    CreativeHourReportListDTO getTotal2(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                        @Param("finalSelectSql") String finalSelectSql,
                                        @Param("customFieldsSql") String customFieldsSql);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_hour ${ew.customSqlSegment}")
    List<CreativeHourReportListDTO> getReportList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT * from (SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_hour ${ew.customSqlSegment}) report ${customFieldsSql}")
    List<CreativeHourReportListDTO> getReportList2(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                   @Param("customFieldsSql") String customFieldsSql);


    @Select("SELECT " +
            " prh.dim_rta_id,drs.campaign_id,drs.cost_rate,ROUND(SUM(idx_consumer_cost)/1000000,2) AS cost, " +
            " SUM(idx_view) AS impressions, SUM(idx_click) AS clicks " +
            "FROM " +
            "t_ads_dsp_flow_plan_rta_hour AS prh " +
            "INNER JOIN iflytek_overseas_dsp.d_rta_strategy AS drs ON drs.id = prh.dim_rta_id " +
            "${ew.customSqlSegment}"
    )
    List<DailyCostListDTO> getAeDailyCost(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 维度数据查询
     *
     * @param wrapper      筛选条件
     * @param whereWrapper 筛选外部条件
     * @return 返回数据
     */
    @Select("SELECT * from " +
            "(SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_hour " +
            "LEFT JOIN iflytek_overseas_dsp.m_plan ON " +
            "t_ads_dsp_flow_plan_hour.dim_plan_id = iflytek_overseas_dsp.m_plan.id " +
            "${ew.customSqlSegment} )" +
            " t ${where.customSqlSegment}")
    List<DimensionTotalDTO> dimensionTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                           @Param("where") Wrapper<?> whereWrapper);

    @Select("SELECT dim_agent_id AS `agent_id`,dim_master_id AS `master_id`," +
            "ROUND((SUM(idx_report_cost) / 1000000), 3) AS `cost`," +
            "ROUND((SUM(idx_platform_cost) / 1000000), 3) AS `show_cost` " +
            "FROM t_ads_dsp_flow_plan_hour " +
            "${ew.customSqlSegment}")
    List<UserCostDTO> getUserCost(@Param(ConstantUtils.WRAPPER) Wrapper<PlanHour> wrapper);


    @Select("SELECT ROUND( SUM( idx_report_cost / 1000000 ), 2 ) AS cost, count( fph.dim_plan_id ) AS num, " +
            "mp.plan_status AS plan_status, mc.campaign_status AS campaign_status " +
            "FROM t_ads_dsp_flow_plan_hour fph " +
            "LEFT JOIN iflytek_overseas_dsp.m_plan mp ON fph.dim_plan_id = mp.id " +
            "LEFT JOIN iflytek_overseas_dsp.m_campaign mc ON fph.dim_campaign_id = mc.id " +
            "${ew.customSqlSegment}")
    List<DiagnosisReportDTO> planDiagnosis(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 数据
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT ${ew.sqlSelect} from t_ads_dsp_flow_plan_hour ${ew.customSqlSegment} ")
    BigDecimal selectRestVal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
