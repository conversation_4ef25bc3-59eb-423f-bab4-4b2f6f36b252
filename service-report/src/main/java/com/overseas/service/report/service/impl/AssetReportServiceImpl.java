package com.overseas.service.report.service.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.chart.LegendDTO;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.chart.SeriesDTO;
import com.overseas.common.dto.market.asset.AssetBaseDTO;
import com.overseas.common.dto.report.*;
import com.overseas.common.dto.report.asset.AssetCombineReportListDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexChildColumnDTO;
import com.overseas.common.entity.User;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.PutEnum;
import com.overseas.common.enums.market.asset.AssetTypeEnum;
import com.overseas.common.enums.report.AssetReportTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.asset.AssetGetVO;
import com.overseas.common.vo.market.asset.AssetNewIdsVO;
import com.overseas.common.vo.market.asset.AssetSearchGetVO;
import com.overseas.common.vo.report.AssetChartListVO;
import com.overseas.common.vo.report.AssetCompareListVO;
import com.overseas.common.vo.report.AssetReportListVO;
import com.overseas.common.vo.report.asset.*;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.common.vo.sys.project.ProjectResourceVO;
import com.overseas.service.report.entity.AssetDay;
import com.overseas.service.report.entity.AssetHour;
import com.overseas.service.report.feign.FgMarketService;
import com.overseas.service.report.feign.FgSystemService;
import com.overseas.service.report.mapper.AssetDayMapper;
import com.overseas.service.report.mapper.AssetHourCkMapper;
import com.overseas.service.report.mapper.AssetHourMapper;
import com.overseas.service.report.mapper.SheinAssetHourMapper;
import com.overseas.service.report.service.AssetReportService;
import com.overseas.service.report.service.ReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AssetReportServiceImpl extends ServiceImpl<AssetDayMapper, AssetDay> implements AssetReportService {

    private final AssetHourMapper assetHourMapper;

    private final FgMarketService fgMarketService;

    private final ReportService reportService;

    private final FgSystemService fgSystemService;

    private final SheinAssetHourMapper sheinAssetHourMapper;

    private final SheinConfiguration sheinConfiguration;

    private final AssetHourCkMapper assetHourCkMapper;

    @Override
    public IPage<AssetReportListDTO> getAssetReportList(AssetReportListVO listVO, User user) {
        if (null == user) {
            listVO.setIsPut(PutEnum.IS_PUT.getId());
        } else {
            listVO.setIsPut(sheinConfiguration.isPut(listVO.getIsPut(), user.getRoleId()));
        }
        IPage<AssetReportListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        listVO = this.completeListVO(listVO);
        if (null == listVO) {
            return iPage;
        }
        QueryWrapper<AssetDay> queryWrapper = this.getQueryWrapper(listVO)
                .groupBy("dim_asset_id")
                .orderBy(StringUtils.isNotBlank(listVO.getSortField()),
                        "asc".equalsIgnoreCase(listVO.getSortType()), HumpLineUtils.humpToLine2(listVO.getSortField()))
                .orderByDesc("dim_asset_id");
        //shein素材报表
        if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
            return sheinAssetHourMapper.getAssetReportList(iPage, queryWrapper);
        }
        if (TimeZoneEnum.UTC_8.getId().equals(listVO.getTimeZone())) {
            return this.baseMapper.getAssetReportList(iPage, queryWrapper);
        } else {
            return this.assetHourMapper.listAssetReport(iPage, queryWrapper);
        }
    }

    @Override
    public PageUtils<AssetReportListDTO> getAssetReportPage(AssetReportListVO listVO, User user) {
        IPage<AssetReportListDTO> pageData = this.getAssetReportList(listVO, user);
        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }
        AssetGetVO getVO = new AssetGetVO();
        getVO.setAssetIds(pageData.getRecords().stream()
                .map(AssetReportListDTO::getAssetId).collect(Collectors.toList()));
        getVO.setMasterIds(!listVO.getMasterIds().isEmpty()
                ? listVO.getMasterIds()
                : (ObjectUtils.isNotNullOrZero(listVO.getUserId()) ? List.of(listVO.getUserId()) : List.of())
        );
        Map<Long, AssetBaseDTO> assetTypeMap = this.fgMarketService.getAssetInfoByIds(getVO).getData();
        pageData.getRecords().forEach(entity -> {
            AssetBaseDTO assetBaseDTO = assetTypeMap.get(entity.getAssetId());
            if (assetBaseDTO == null) {
                return;
            }
            entity.setAssetType(assetBaseDTO.getAssetType());
            entity.setAssetName(assetBaseDTO.getAssetName());
            entity.setAssetPath(UploadUtils.getNetworkUrl(assetBaseDTO.getAssetPath(), assetBaseDTO.getIsUpload()));
        });

        AssetReportListDTO assetReportListDTO = this.baseMapper.getAssetReportTotal(this.getQueryWrapper(listVO));
        assetReportListDTO.setAssetId(0L);
        assetReportListDTO.setAssetName(ConstantUtils.ALL_2);
        return new PageUtils<>(pageData, assetReportListDTO);
    }

    @Override
    public List<AssetReportListDTO> getAssetCompareList(AssetCompareListVO listVO) {
        AssetReportTypeEnum assetReportTypeEnum = ICommonEnum.get(listVO.getGroupBy(), AssetReportTypeEnum.class);
        if (null == assetReportTypeEnum) {
            throw new CustomException("比较维度不合法");
        }
        AssetReportListVO reportListVO = this.completeListVO(listVO);
        if (null == reportListVO) {
            return List.of();
        }
        if (StringUtils.isNotBlank(listVO.getAssetIdStr())) {
            try {
                listVO.setAssetIds(Arrays.stream(listVO.getAssetIdStr().split(","))
                        .filter(StringUtils::isNotBlank)
                        .map(u -> Long.parseLong(u.trim()))
                        .collect(Collectors.toList()));
            } catch (Exception e) {
                throw new CustomException("素材ID搜索不合法，请确认后再试");
            }
        }
        //当前数据获取
        listVO.setCurrentStartDate(listVO.getStartDate());
        listVO.setCurrentEndDate(listVO.getEndDate());
        List<String> dateRange1 = DateUtils.getBetweenDate(listVO.getCurrentStartDate(), listVO.getCurrentEndDate(), DateUtils.DATE_PATTERN);
        List<String> dateRange2 = DateUtils.getBetweenDate(listVO.getCompareStartDate(), listVO.getCompareEndDate(), DateUtils.DATE_PATTERN);
        if (dateRange1.size() != dateRange2.size()) {
            return List.of();
        }
        //当前数据获取
        AssetReportListDTO todayTotal = this.getTotalList(assetReportTypeEnum, reportListVO);
        List<AssetReportListDTO> todayList = this.getCompareList(assetReportTypeEnum, reportListVO, assetReportTypeEnum.getField());

        //比对数据
        reportListVO.setStartDate(listVO.getCompareStartDate());
        reportListVO.setEndDate(listVO.getCompareEndDate());
        List<AssetReportListDTO> compareList = this.getCompareList(assetReportTypeEnum, reportListVO, assetReportTypeEnum.getField());
        if (todayList.isEmpty() && compareList.isEmpty()) {
            return todayList;
        }
        AssetReportListDTO compareTotal = this.getTotalList(assetReportTypeEnum, reportListVO);
        //最终结果存储对象
        List<AssetReportListDTO> result = new ArrayList<>();
        //汇总数据添加
        AssetReportListDTO total = this.fillCompleteDto(todayTotal, compareTotal);
        total.setAssetName("汇总");
        total.setReportHour("汇总");
        total.setReportDate("汇总");
        total.setAssetId(null);
        result.add(total);

        switch (assetReportTypeEnum) {
            //天数据
            case DAY:
                Map<Long, AssetReportListDTO> todayDataMap1 = todayList.stream().collect(Collectors.toMap(AssetReportListDTO::getDay, Function.identity()));
                Map<Long, AssetReportListDTO> compareDataMap1 = compareList.stream().collect(Collectors.toMap(AssetReportListDTO::getDay, Function.identity()));
                int descIndex1 = 0;
                if (AssetReportTypeEnum.DAY.getName().equals(listVO.getSortField()) && "desc".equalsIgnoreCase(listVO.getSortField())) {
                    descIndex1 = -dateRange1.size() + 1;
                }
                for (int i = 0; i < dateRange1.size(); i++) {
                    int dayIndex = i + descIndex1;
                    if (dayIndex < 0) {
                        dayIndex = -dayIndex;
                    }
                    Long timestamp1 = DateUtils.string2Long(dateRange1.get(dayIndex));
                    Long timestamp2 = DateUtils.string2Long(dateRange2.get(dayIndex));
                    AssetReportListDTO news = this.fillCompleteDto(todayDataMap1.get(timestamp1), compareDataMap1.get(timestamp2));
                    news.setReportDate(String.format("指定天：%s \n 对比天：%s", dateRange1.get(i), dateRange2.get(i)));
                    result.add(news);
                }
                break;
            //小时数据
            case HOUR:
                Map<Integer, AssetReportListDTO> todayDataMap2 = todayList.stream().collect(Collectors.toMap(AssetReportListDTO::getHour, Function.identity()));
                Map<Integer, AssetReportListDTO> compareDataMap2 = compareList.stream().collect(Collectors.toMap(AssetReportListDTO::getHour, Function.identity()));
                List<Integer> hourRange = IntStream.range(0, 24).mapToObj(u -> Integer.parseInt(String.valueOf(u))).collect(Collectors.toList());
                int descIndex2 = 0;
                if (AssetReportTypeEnum.DAY.getName().equals(listVO.getSortField()) && "desc".equalsIgnoreCase(listVO.getSortField())) {
                    descIndex2 = -hourRange.size() + 1;
                }
                for (int i = 0; i < hourRange.size(); i++) {
                    int hourIndex = i + descIndex2;
                    if (hourIndex < 0) {
                        hourIndex = -hourIndex;
                    }
                    Integer hour = hourRange.get(hourIndex);
                    AssetReportListDTO news = this.fillCompleteDto(todayDataMap2.get(hour), compareDataMap2.get(hour));
                    news.setReportHour(String.format("%s:00:00", hour < 10 ? "0" + hour : hour));
                    result.add(news);
                }
                break;
            //素材数据
            case ASSET:
                List<Long> assetIds = Stream.of(
                        todayList.stream().map(AssetReportListDTO::getAssetId).filter(ObjectUtils::isNotNullOrZero),
                        compareList.stream().map(AssetReportListDTO::getAssetId).filter(ObjectUtils::isNotNullOrZero)
                ).flatMap(u -> u).distinct().sorted((o1, o2) -> {
                    if (AssetReportTypeEnum.ASSET.getName().equals(listVO.getSortField()) && "desc".equalsIgnoreCase(listVO.getSortField())) {
                        return Math.toIntExact(o2 - o1);
                    }
                    return Math.toIntExact(o1 - o2);
                }).collect(Collectors.toList());
                //赋值 资产素材ID
                AssetGetVO getVO = new AssetGetVO();
                getVO.setAssetIds(assetIds);
                getVO.setAssetIds(assetIds);
                getVO.setMasterIds(!listVO.getMasterIds().isEmpty()
                        ? listVO.getMasterIds()
                        : (ObjectUtils.isNotNullOrZero(listVO.getUserId()) ? List.of(listVO.getUserId()) : List.of())
                );
                Map<Long, AssetBaseDTO> assetTypeMap = this.fgMarketService.getAssetInfoByIds(getVO).getData();
                Map<Long, AssetReportListDTO> todayDataMap3 = todayList.stream().collect(Collectors.toMap(AssetReportListDTO::getAssetId, Function.identity()));
                Map<Long, AssetReportListDTO> compareDataMap3 = compareList.stream().collect(Collectors.toMap(AssetReportListDTO::getAssetId, Function.identity()));
                assetIds.forEach(assetId -> {
                    AssetBaseDTO assetBaseDTO = assetTypeMap.get(assetId);
                    if (assetBaseDTO == null) {
                        return;
                    }
                    AssetReportListDTO assetReportListDTO = this.fillCompleteDto(todayDataMap3.get(assetId), compareDataMap3.get(assetId));
                    assetReportListDTO.setAssetType(assetBaseDTO.getAssetType());
                    assetReportListDTO.setAssetName(assetBaseDTO.getAssetName());
                    assetReportListDTO.setAssetPath(UploadUtils.getNetworkUrl(assetBaseDTO.getAssetPath(), assetBaseDTO.getIsUpload()));
                    result.add(assetReportListDTO);
                });
                break;
            default:
        }
        return result;
    }

    @Override
    public void exportAssetCompareList(AssetCompareListVO listVO, HttpServletResponse response) throws IOException {
        List<AssetReportListDTO> listData = this.getAssetCompareList(listVO);
        if (listData.isEmpty()) {
            throw new CustomException("无可下载数据");
        }
        AssetReportTypeEnum assetReportTypeEnum = ICommonEnum.get(listVO.getGroupBy(), AssetReportTypeEnum.class);
        if (null == assetReportTypeEnum) {
            throw new CustomException("比较维度不合法");
        }
        Map<String, String> fieldMap = fgSystemService.getUserCustomIndexReportDownload(CustomIndexGetVO.builder()
                        .module(listVO.getModule()).identify(listVO.getIdentify()).build()).getData()
                .stream().flatMap(u -> u.getColumns().stream())
                .collect(Collectors.toMap(CustomIndexChildColumnDTO::getKey, CustomIndexChildColumnDTO::getTitle, (o, n) -> n));
        fieldMap.putAll(ObjectUtils.getFields(AssetReportListDTO.class)
                .stream().filter(field -> field.getAnnotation(ExcelProperty.class) != null)
                .collect(Collectors.toMap(Field::getName, field -> field.getAnnotation(ExcelProperty.class).value()[0])));
        //如果获取不到自定义列
        List<SelectDTO2> headers;
        headers = new ArrayList<>() {{
            if (AssetReportTypeEnum.ASSET.equals(assetReportTypeEnum)) {
                add(new SelectDTO2("assetName", "素材名称"));
                add(new SelectDTO2("assetId", "素材ID"));
            } else {
                add(new SelectDTO2(assetReportTypeEnum.getName(), assetReportTypeEnum.getFieldName()));
            }
            if (CollectionUtils.isNotEmpty(listVO.getCustoms())) {
                listVO.getCustoms().forEach(custom -> {
                    if (null == fieldMap.get(custom)) {
                        return;
                    }
                    String firstUpperKey = HumpLineUtils.firstToUpper(custom),
                            fieldTitle = fieldMap.get(custom);
                    add(new SelectDTO2("today" + firstUpperKey, fieldTitle + "_指定时段"));
                    add(new SelectDTO2(custom, fieldTitle + "_对比时段"));
                    if (listVO.getIsShowDiff()) {
                        add(new SelectDTO2("diff" + firstUpperKey, fieldTitle + "_差值"));
                        add(new SelectDTO2("diffRate" + firstUpperKey, fieldTitle + "_差值比例（%）"));
                    }
                });
            }
        }};
        String fileName = String.format("数据报表_%s-%s~%s-%s", listVO.getCurrentStartDate(), listVO.getCurrentEndDate(),
                listVO.getCompareStartDate(), listVO.getCompareEndDate());
        // 如果带小时段，去掉分秒
        log.info("file name : {},  headers : {}", fileName, JSONObject.toJSONString(headers));
        ExcelUtils.download(response, fileName, "报表数据", listData, headers, "_");
    }

    @Override
    public MultiIndexChartDTO getAssetReportChart(AssetChartListVO listVO) {
        AssetReportTypeEnum assetReportTypeEnum = ICommonEnum.get(listVO.getGroupBy(), AssetReportTypeEnum.class);
        if (null == assetReportTypeEnum) {
            throw new CustomException("比较维度不合法");
        }
        AssetReportListVO completeListVO = completeListVO(listVO);
        if (null == completeListVO) {
            return new MultiIndexChartDTO();
        }
        String groupBy = "";
        if (listVO.getIndicator().equals(listVO.getComparisonIndicator())) {
            groupBy += " dim_asset_id, " + assetReportTypeEnum.getField();
            listVO.setIndicators(List.of(listVO.getIndicator()));
            //获取top 10 素材
            List<Long> assetIds = this.assetHourMapper.getAssetReportList(this.getQueryWrapper(completeListVO, "dim_asset_id as asset_id,")
                    .groupBy("asset_id")
                    .orderByDesc(HumpLineUtils.humpToLine2(listVO.getIndicator()))
                    .orderByDesc("dim_asset_id")
                    .last("limit 10")
            ).stream().map(AssetReportListDTO::getAssetId).collect(Collectors.toList());
            if (assetIds.isEmpty()) {
                return new MultiIndexChartDTO();
            }
            listVO.setAssetIds(assetIds);
        } else {
            groupBy += assetReportTypeEnum.getField();
            listVO.setIndicators(List.of(listVO.getIndicator(), listVO.getComparisonIndicator()));
        }

        List<AssetReportListDTO> assetReportListDTOList = this.assetHourMapper.getAssetReportList(
                this.getQueryWrapper(completeListVO, "dim_asset_id AS asset_id, dim_day AS `day`, dim_hour AS `hour`,")
                        .groupBy(groupBy)
                        .orderByAsc(Arrays.asList(groupBy.split(",")))
        );
        if (assetReportListDTOList.isEmpty()) {
            return new MultiIndexChartDTO();
        }

        // 2.组装数据
        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        // 日期
        List<String> dates = new ArrayList<>();
        switch (assetReportTypeEnum) {
            case DAY:
                dates = DateUtils.getBetweenDate(listVO.getStartDate(), listVO.getEndDate(), DateUtils.DATE_PATTERN);
                multiIndexChartDTO.setXAxis(List.of(new LegendDTO(dates)));
                break;
            case HOUR:
                dates = IntStream.range(0, 24).mapToObj(Integer::toString).collect(Collectors.toList());
                multiIndexChartDTO.setXAxis(List.of(new LegendDTO(dates.stream().map(u -> DateUtils.hour2Name(Integer.parseInt(u))).collect(Collectors.toList()))));
                break;
            default:
        }
        // 3.组装返回数据
        // 指标的数据
        Map<String, List<Object>> valueMap = new HashMap<>();
        if (listVO.getIndicator().equals(listVO.getComparisonIndicator())) {
            Map<Long, List<AssetReportListDTO>> assetMap = new HashMap<>();
            assetReportListDTOList.forEach(u -> assetMap.computeIfAbsent(u.getAssetId(), k -> new ArrayList<>()).add(u));

            AssetGetVO getVO = new AssetGetVO();
            getVO.setAssetIds(new ArrayList<>(assetMap.keySet()));
            getVO.setMasterIds(!listVO.getMasterIds().isEmpty()
                    ? listVO.getMasterIds()
                    : (ObjectUtils.isNotNullOrZero(listVO.getUserId()) ? List.of(listVO.getUserId()) : List.of())
            );
            Map<Long, AssetBaseDTO> assetTypeMap = this.fgMarketService.getAssetInfoByIds(getVO).getData();
            List<String> finalDates = dates;
            List<String> legendList = new ArrayList<>();
            assetMap.forEach((key, val) -> {
                Map<String, List<Object>> oneAssetValueMap = new HashMap<>();
                this.multiIndexChart(assetReportTypeEnum, val, listVO, oneAssetValueMap, finalDates);
                String legend = String.format("%s_%s", key, assetTypeMap.get(key).getAssetName());
                valueMap.put(legend, oneAssetValueMap.get(listVO.getIndicator()));
                legendList.add(legend);
            });
            multiIndexChartDTO.setLegend(new LegendDTO(legendList));
            AtomicInteger index = new AtomicInteger(0);
            multiIndexChartDTO.setSeries(legendList.stream().map(u -> new SeriesDTO(legendList.get(index.getAndIncrement()), "line", valueMap.getOrDefault(u, List.of()))).collect(Collectors.toList()));
        } else {
            this.multiIndexChart(assetReportTypeEnum, assetReportListDTOList, listVO, valueMap, dates);
            // 填充y轴名称
            List<String> legendList = reportService.findReportFieldsTitle(listVO.getModule(), listVO.getIdentify(), listVO.getIndicators());
            multiIndexChartDTO.setLegend(new LegendDTO(legendList));
            AtomicInteger index = new AtomicInteger(0);
            multiIndexChartDTO.setSeries(listVO.getIndicators().stream().map(indicator ->
                    new SeriesDTO(legendList.get(index.getAndIncrement()), "line", valueMap.getOrDefault(indicator, List.of()))).collect(Collectors.toList()));
        }
        return multiIndexChartDTO;
    }


    /**
     * 拼接 chart 数据解构
     *
     * @param assetReportTypeEnum    类型
     * @param assetReportListDTOList 参数
     * @param listVO                 参数
     * @param valueMap               单数list
     * @param dates                  返回数据
     */
    private void multiIndexChart(AssetReportTypeEnum assetReportTypeEnum, List<AssetReportListDTO> assetReportListDTOList,
                                 AssetChartListVO listVO, Map<String, List<Object>> valueMap, List<String> dates) {
        switch (assetReportTypeEnum) {
            case DAY:
                Map<Long, AssetReportListDTO> dayMap = assetReportListDTOList.stream().collect(Collectors.toMap(ReportListDTO::getDay, Function.identity()));
                for (String day : dates) {
                    AssetReportListDTO dto = dayMap.getOrDefault(DateUtils.string2Long(day), new AssetReportListDTO());
                    for (String indicator : listVO.getIndicators()) {
                        Object value = this.getObject2BigDecimal(dto, indicator);
                        if (valueMap.get(indicator) == null) {
                            valueMap.put(indicator, new ArrayList<>(List.of(value)));
                        } else {
                            valueMap.get(indicator).add(value);
                        }
                    }
                }
                break;
            case HOUR:
                Map<String, AssetReportListDTO> hourMap = assetReportListDTOList.stream().collect(Collectors.toMap(u -> u.getHour().toString(), Function.identity()));
                dates.forEach(hour -> {
                    AssetReportListDTO dto = hourMap.getOrDefault(hour, new AssetReportListDTO());
                    for (String indicator : listVO.getIndicators()) {
                        Object value = this.getObject2BigDecimal(dto, indicator);
                        if (valueMap.get(indicator) == null) {
                            valueMap.put(indicator, new ArrayList<>(List.of(value)));
                        } else {
                            valueMap.get(indicator).add(value);
                        }
                    }
                });
                break;
            default:
        }
    }


    /**
     * 获取比对数据
     *
     * @param assetReportTypeEnum 类型
     * @param reportListVO        条件
     * @param groupBy             分组
     * @return 返回数据
     */
    private List<AssetReportListDTO> getCompareList(AssetReportTypeEnum assetReportTypeEnum,
                                                    AssetReportListVO reportListVO, String groupBy) {
        if (Objects.requireNonNull(assetReportTypeEnum) == AssetReportTypeEnum.HOUR) {
            return this.assetHourMapper.getAssetReportList(
                    this.getQueryWrapper(reportListVO, "dim_asset_id AS asset_id, dim_day AS `day`, dim_hour AS `hour`, ")
                            .groupBy(groupBy)
                            .orderBy(StringUtils.isNotBlank(reportListVO.getSortField()) && assetReportTypeEnum.getName().equals(reportListVO.getSortField()),
                                    "asc".equalsIgnoreCase(reportListVO.getSortType()), assetReportTypeEnum.getField())
                            .orderByDesc(groupBy)
            );
        }
        return this.baseMapper.listAssetReport(this.getQueryWrapper(reportListVO, "dim_asset_id AS asset_id, dim_day AS `day`, ")
                .groupBy(groupBy)
                .orderBy(StringUtils.isNotBlank(reportListVO.getSortField()) && assetReportTypeEnum.getName().equals(reportListVO.getSortField()),
                        "asc".equalsIgnoreCase(reportListVO.getSortType()), assetReportTypeEnum.getField())
                .orderByDesc(groupBy)
        );
    }


    /**
     * 总数
     *
     * @param assetReportTypeEnum 类型
     * @param reportListVO        条件
     * @return 返回总数
     */
    private AssetReportListDTO getTotalList(AssetReportTypeEnum assetReportTypeEnum, AssetReportListVO reportListVO) {
        if (Objects.requireNonNull(assetReportTypeEnum) == AssetReportTypeEnum.HOUR) {
            return this.assetHourMapper.getAssetReportTotal(
                    this.getQueryWrapper(reportListVO, "0 asset_id, 0 AS `day`, 0 AS `hour`, ")
            );
        }
        return this.baseMapper.getAssetReportTotal(
                this.getQueryWrapper(reportListVO, "0 AS `asset_id`, 0 AS `day`, ")
        );
    }


    /**
     * 筛选条件
     *
     * @param listVO 条件
     * @return 返回数据
     */
    private QueryWrapper<AssetDay> getQueryWrapper(AssetReportListVO listVO) {
        return this.getQueryWrapper(listVO, "dim_asset_id AS asset_id,");
    }

    /**
     * 筛选条件
     *
     * @param listVO    条件
     * @param selectStr 字段
     * @return 返回数据
     */
    private QueryWrapper<AssetDay> getQueryWrapper(AssetReportListVO listVO, String selectStr) {
        QueryWrapper<AssetDay> queryWrapper = new QueryWrapper<AssetDay>().select(selectStr +
                        reportService.findReportFields(listVO.getModule(), listVO.getIdentify(), listVO.getSortField()))
                .eq(ObjectUtils.isNotNullOrZero(listVO.getUserId()), "dim_user_id", listVO.getUserId())
                .in(!listVO.getMasterIds().isEmpty(), "dim_master_id", listVO.getMasterIds())
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "dim_campaign_id", listVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "dim_plan_id", listVO.getPlanIds())
                .in(CollectionUtils.isNotEmpty(listVO.getAssetIds()), "dim_asset_id", listVO.getAssetIds());
        if (listVO.getTimeZone().equals(TimeZoneEnum.UTC_8.getId())) {
            queryWrapper.between("dim_day", DateUtils.string2Long(listVO.getStartDate()), DateUtils.string2Long(listVO.getEndDate()));
        } else {
            //时区下的开始时间
            Long start = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getStartDate()), listVO.getTimeZone(), 0));
            //时区下的结束时间
            Long end = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getEndDate()), listVO.getTimeZone(), 23));
            queryWrapper.between("dim_report_hour", start, end)
                    .between("dim_day", DateUtils.string2Long(DateUtils.long2String(start)), DateUtils.string2Long(DateUtils.long2String(end)));
        }
        return queryWrapper;
    }

    @Override
    public void exportAssetList(AssetReportListVO listVO, User user, HttpServletResponse response) {
        List<AssetReportListDTO> assetReportList = this.getAssetReportPage(listVO, user).getData();
        if (assetReportList.isEmpty()) {
            throw new CustomException("暂无可导出数据");
        }
        List<SelectDTO2> headers = reportService.findReportHeader(listVO.getCustoms(), listVO.getModule(), listVO.getIdentify(), AssetReportListDTO.class);

        String fileName = "素材报表_" + listVO.getStartDate() + (listVO.getStartDate().equals(listVO.getEndDate()) ? "" : "_" + listVO.getEndDate());

        log.info("file name : {},  headers : {}", fileName, JSONObject.toJSONString(headers));
        headers.add(new SelectDTO2("assetPath", "素材地址"));
        try {
            ExcelUtils.download(response, fileName, "报表数据", assetReportList, headers);
        } catch (Exception e) {
            log.info(e.getMessage());
            throw new CustomException(e.getMessage());
        }
    }

    @Override
    public MultiIndexChartDTO getAssetChart(AssetChartGetVO getVO) {
        // 1.查询数据
        String field = getVO.getStartDate().equals(getVO.getEndDate()) ? "dim_hour" : "dim_day";
        List<AssetReportListDTO> assetReportListDTOList = this.assetHourMapper.getAssetReportList(new QueryWrapper<AssetHour>()
                .select("dim_day AS `day`,dim_hour AS `hour`," +
                        reportService.findReportFields(getVO.getModule(), getVO.getIdentify(), "")
                )
                .between("dim_day", DateUtils.string2Long(getVO.getStartDate()), DateUtils.string2Long(getVO.getEndDate()))
                .eq("dim_user_id", getVO.getUserId())
                .groupBy(field)
                .orderByAsc(field));
        if (assetReportListDTOList.isEmpty()) {
            return new MultiIndexChartDTO();
        }
        // 2.组装数据
        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        List<String> dates = new ArrayList<>(); // 日期
        Map<String, List<Object>> valueMap = new HashMap<>(); // 指标的数据
        for (AssetReportListDTO assetReportListDTO : assetReportListDTOList) {
            // 填充时间轴
            if (field.equals("dim_hour")) {
                dates.add(DateUtils.hour2Name(assetReportListDTO.getHour()));
            } else {
                dates.add(DateUtils.long2String(assetReportListDTO.getDay()));
            }
            for (String indicator : getVO.getIndicators()) {
                Object value = this.getObject2BigDecimal(assetReportListDTO, indicator);
                if (valueMap.get(indicator) == null) {
                    valueMap.put(indicator, new ArrayList<>(List.of(value)));
                } else {
                    valueMap.get(indicator).add(value);
                }
            }
        }
        // 3.组装返回数据
        multiIndexChartDTO.setXAxis(List.of(new LegendDTO(dates)));
        // 填充y轴名称
        List<String> legendList = reportService.findReportFieldsTitle(getVO.getModule(), getVO.getIdentify(), getVO.getIndicators());
        multiIndexChartDTO.setLegend(new LegendDTO(legendList));
        AtomicInteger index = new AtomicInteger(0);
        multiIndexChartDTO.setSeries(getVO.getIndicators().stream().map(indicator ->
                new SeriesDTO(legendList.get(index.getAndIncrement()), "line", valueMap.getOrDefault(indicator, List.of()))).collect(Collectors.toList()));
        return multiIndexChartDTO;
    }

    @Override
    public void updateAssetUserId(Long reportDate) {

        this.baseMapper.batchUpdateAssetDayUserId(reportDate);
        this.assetHourMapper.batchUpdateAssetHourUserId(reportDate);
    }

    @Override
    public List<Long> getAssetIds() {
        return this.baseMapper.listAssetReport(new QueryWrapper<AssetDay>()
                .select("dim_asset_id AS asset_id")
                .groupBy("dim_asset_id")).stream().map(AssetReportListDTO::getAssetId).collect(Collectors.toList());
    }

    @Override
    public PageUtils<?> assetBar(AssetBarVO assetBarVO) {
        String selectSql = fgSystemService.getUserCustomIndexReportSelect(CustomIndexGetVO.builder()
                        .identify(assetBarVO.getIdentify()).module(assetBarVO.getModule()).build())
                .getData();
        QueryWrapper<?> queryWrapper = this.queryWrapper(assetBarVO);
        Map<Integer, AssetBarDTO> result = new HashMap<>();
        AssetBarListDTO today = new AssetBarListDTO();
        if (null != queryWrapper) {
            List<AssetBarListDTO> todayAssetBar = this.baseMapper.listAssetBar(
                    queryWrapper.select("count(DISTINCT dim_asset_id) AS asset_num, 0 AS asset_type, " + selectSql)
            );
            if (!todayAssetBar.isEmpty()) {
                today = todayAssetBar.get(0);
            }
            result.put(0, AssetBarDTO.builder().today(today).last(new AssetBarListDTO()).build());
            this.baseMapper.listAssetBar(
                    queryWrapper.select("count(DISTINCT dim_asset_id) AS asset_num, asset_type, " + selectSql)
                            .groupBy("asset_type")
            ).forEach(assetBar -> result.put(assetBar.getAssetType(), AssetBarDTO.builder().today(assetBar).
                    last(new AssetBarListDTO()).build()));
        }
        //上一期数据
        this.lastCycle(assetBarVO);
        QueryWrapper<?> lastQueryWrapper = this.queryWrapper(assetBarVO);
        AssetBarListDTO last = new AssetBarListDTO();
        if (null != lastQueryWrapper) {
            List<AssetBarListDTO> todayAssetBar = this.baseMapper.listAssetBar(
                    lastQueryWrapper.select("count(DISTINCT dim_asset_id) as asset_num, " + selectSql)
            );
            if (!todayAssetBar.isEmpty()) {
                last = todayAssetBar.get(0);
            }
            result.computeIfAbsent(0, k -> AssetBarDTO.builder().today(new AssetBarListDTO()).build()).setLast(last);
            this.baseMapper.listAssetBar(
                    lastQueryWrapper.select("count(DISTINCT dim_asset_id) AS asset_num, asset_type, " + selectSql)
                            .groupBy("asset_type")
            ).forEach(assetBar -> result.computeIfAbsent(assetBar.getAssetType(), k -> AssetBarDTO.builder().today(new AssetBarListDTO()).build())
                    .setLast(assetBar));
        }
        return new PageUtils<>(result.values()
                .stream().peek(u -> {
                    if (u.getToday().getAssetType().equals(0)) {
                        u.getToday().setAssetTypeName("总计");
                    } else {
                        u.getToday().setAssetTypeName(ICommonEnum.getNameById(u.getToday().getAssetType(), AssetTypeEnum.class));
                    }
                })
                .collect(Collectors.toList()),
                Long.valueOf(result.size()));
    }

    @Override
    public PageUtils<?> assetTop(AssetTopVO assetTopVO) {
        QueryWrapper<?> queryWrapper = this.queryWrapper(assetTopVO);
        if (null == queryWrapper) {
            return new PageUtils<>(List.of(), 0L);
        }
        String selectSql = fgSystemService.getUserCustomIndexReportSelect(CustomIndexGetVO.builder()
                        .identify(assetTopVO.getIdentify()).module(assetTopVO.getModule()).build())
                .getData();
        List<AssetReportListDTO> assetList = this.baseMapper.listAssetReportWithAsset(
                queryWrapper.groupBy("dim_asset_id")
                        .eq(ObjectUtils.isNotNullOrZero(assetTopVO.getAssetType()), "asset_type", assetTopVO.getAssetType())
                        .select("dim_asset_id AS asset_id, " + selectSql)
                        .orderBy(StringUtils.isNotBlank(assetTopVO.getSortField()), "asc".equalsIgnoreCase(assetTopVO.getSortType()), HumpLineUtils.humpToLine2(assetTopVO.getSortField()))
                        .orderByDesc("dim_asset_id")
                        .last(" limit 5")
        );
        AssetGetVO assetGetVO = new AssetGetVO();
        assetGetVO.setAssetIds(assetList.stream().map(AssetReportListDTO::getAssetId).collect(Collectors.toList()));
        Map<Long, AssetBaseDTO> assetMap = fgMarketService.getAssetInfoByIds(assetGetVO).getData();

        assetList.forEach(assetReport -> {
            AssetBaseDTO base = assetMap.get(assetReport.getAssetId());
            if (null != base) {
                assetReport.setAssetName(base.getAssetName());
                assetReport.setAssetPath(UploadUtils.getNetworkUrl(base.getAssetPath(), base.getIsUpload()));
                assetReport.setAssetType(base.getAssetType());
            }
        });
        return new PageUtils<>(assetList, (long) assetList.size());
    }

    @Override
    public PageUtils<AssetCombineReportListDTO> combineList(AssetCombineListVO listVO, User user) {
        IPage<AssetCombineReportListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        listVO = this.completeListVO(listVO);
        if (null == listVO) {
            return new PageUtils<>(iPage);
        }
        List<Long> textAssetIds = listVO.getTextAssetIds();
        QueryWrapper<AssetDay> queryWrapper = this.getQueryWrapper(listVO,
                        "dim_title_id AS title_id, dim_desc_id AS desc_id,")
                .groupBy("dim_title_id")
                .groupBy("dim_desc_id")
                .orderBy(StringUtils.isNotBlank(listVO.getSortField()),
                        "asc".equalsIgnoreCase(listVO.getSortType()), HumpLineUtils.humpToLine2(listVO.getSortField()))
                .orderByDesc("dim_title_id")
                .orderByDesc("dim_desc_id")
                .and(CollectionUtils.isNotEmpty(textAssetIds), q -> q.in("dim_title_id", textAssetIds).or().in("dim_desc_id", textAssetIds))
                .inSql(CollectionUtils.isNotEmpty(listVO.getCombineInfos()), "(title_id, desc_id)",
                        listVO.getCombineInfos().stream().map(u -> String.format("(%s, %s)", u.getTitleId(), u.getDescId()))
                                .collect(Collectors.joining(","))
                );
        iPage = this.assetHourCkMapper.listAssetCombineReport(iPage, queryWrapper);
        return new PageUtils<>(iPage);
    }


    @Override
    public PageUtils<AssetCombineReportListDTO> fieldList(AssetFieldListVO listVO, User user) {
        IPage<AssetCombineReportListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        listVO = this.completeListVO(listVO);
        if (null == listVO) {
            return new PageUtils<>(iPage);
        }
        String fieldName = this.getFieldName(listVO);
        if (StringUtils.isBlank(fieldName)) {
            return new PageUtils<>(iPage);
        }
        List<Long> assetIds = listVO.getAssetIds();
        listVO.setAssetIds(null);
        QueryWrapper<AssetDay> queryWrapper = this.getQueryWrapper(listVO,
                        String.format("dim_%s AS %s ,", fieldName, fieldName))
                .in(CollectionUtils.isNotEmpty(assetIds), String.format("dim_%s", fieldName), assetIds)
                .groupBy(String.format("dim_%s", fieldName))
                .orderBy(StringUtils.isNotBlank(listVO.getSortField()),
                        "asc".equalsIgnoreCase(listVO.getSortType()), HumpLineUtils.humpToLine2(listVO.getSortField()))
                .orderByDesc(String.format("dim_%s", fieldName));
        iPage = this.assetHourCkMapper.listAssetCombineReport(iPage, queryWrapper);
        return new PageUtils<>(iPage);
    }

    /**
     * 获取字段名称
     *
     * @param listVO 条件
     * @return 返回数据
     */
    private String getFieldName(AssetFieldListVO listVO) {
        Map<Integer, String> fieldNameMap = new HashMap<>() {{
            put(3, "title_id");
            put(4, "desc_id");
        }};
        switch (listVO.getFieldType()) {
            case 1:
                return "";
            default:
                return fieldNameMap.get(listVO.getFieldType());
        }
    }

    /**
     * 获取 bar/top 筛选条件
     *
     * @param assetBarVO 条件
     * @return 返回数据
     */
    private QueryWrapper<?> queryWrapper(AssetBarVO assetBarVO) {
        QueryWrapper<?> queryWrapper = new QueryWrapper<>();
        if (1 == assetBarVO.getIsNewAsset()) {
            List<Long> newAssetIds = fgMarketService.newAssetIds(AssetNewIdsVO.builder()
                    .newStartDate(assetBarVO.getNewStartDate())
                    .newEndDate(assetBarVO.getNewEndDate()).build()
            ).getData();
            if (CollectionUtils.isEmpty(newAssetIds)) {
                return null;
            }
            queryWrapper.in("dim_asset_id", newAssetIds);
        }
        if (ObjectUtils.isNotNullOrZero(assetBarVO.getProjectId())) {
            ProjectResourceVO projectResourceVO = new ProjectResourceVO();
            projectResourceVO.setProjectId(assetBarVO.getProjectId());
            List<Long> masterIds = fgSystemService.projectResource(projectResourceVO).getData().getResourceIds();
            if (masterIds.isEmpty()) {
                return null;
            }
            queryWrapper.in("dim_master_id", masterIds);
        }
        // 未选择autolink 和shake 的计划
//        PlanListByTemplateGetVO getVO = new PlanListByTemplateGetVO();
//        getVO.setIsAuto(0);
//        getVO.setIsShake(0);
//        List<Long> noAutoLinkShakePlanIds = this.fgMarketService.listPlanByTemplate(getVO).getData();
        //过滤数据
        queryWrapper.eq(ObjectUtils.isNotNullOrZero(assetBarVO.getDesignerId()), "dim_user_id", assetBarVO.getDesignerId())
                .between("dim_day", DateUtils.string2Long(assetBarVO.getStartDate()),
                        DateUtils.string2Long(assetBarVO.getEndDate()));
//                .in("dim_plan_id", noAutoLinkShakePlanIds);
        return queryWrapper;
    }

    /**
     * 获取上一周期数据
     *
     * @param assetBarVO 数据
     */
    private void lastCycle(AssetBarVO assetBarVO) {
        //数据周期上一期
        DateUtils.Cycle lastDataDate = DateUtils.lastCycle(assetBarVO.getStartDate(), assetBarVO.getEndDate());
        assetBarVO.setStartDate(DateUtils.format(lastDataDate.getStart()));
        assetBarVO.setEndDate(DateUtils.format(lastDataDate.getEnd()));
//        //新素材周期上一期
//        if (1 == assetBarVO.getIsNewAsset()) {
//            DateUtils.Cycle lastNewDate = DateUtils.lastCycle(assetBarVO.getNewStartDate(), assetBarVO.getNewEndDate());
//            assetBarVO.setNewStartDate(DateUtils.format(lastNewDate.getStart()));
//            assetBarVO.setNewEndDate(DateUtils.format(lastNewDate.getEnd()));
//        }
    }

    /**
     * 将数据转换为 bigDecimal
     *
     * @param object    对象
     * @param indicator 字段
     * @return 返回数据
     */
    private BigDecimal getObject2BigDecimal(Object object, String indicator) {
        Object obj = ObjectUtils.getObjectValue(object, indicator);
        return ObjectUtils.isNullOrZero(obj) ? new BigDecimal(0) : new BigDecimal(obj.toString());
    }

    /**
     * 转换数据
     *
     * @param listVO 筛选条件
     * @return 返回数据
     */
    private <T extends AssetReportListVO> T completeListVO(T listVO) {
        // 如果查询了素材ID/名称、素材尺寸、创意标签
        if (CollectionUtils.isEmpty(listVO.getAssetIds()) && (StringUtils.isNotBlank(listVO.getSearch()) || CollectionUtils.isNotEmpty(listVO.getSizes()) ||
                ObjectUtils.isNotNullOrZero(listVO.getVideoType()) || ObjectUtils.isNotNullOrZero(listVO.getCreativeDirection()) ||
                ObjectUtils.isNotNullOrZero(listVO.getGoodsCategory()) || ObjectUtils.isNotNullOrZero(listVO.getAssetCategory()))) {
            AssetSearchGetVO getVO = new AssetSearchGetVO();
            getVO.setSearch(listVO.getSearch());
            getVO.setMasterIds(!listVO.getMasterIds().isEmpty() ? listVO.getMasterIds() : (ObjectUtils.isNotNullOrZero(listVO.getUserId()) ? List.of(listVO.getUserId()) : List.of()));
            getVO.setSizes(listVO.getSizes());
            getVO.setVideoType(listVO.getVideoType());
            getVO.setCreativeDirection(listVO.getCreativeDirection());
            getVO.setGoodsCategory(listVO.getGoodsCategory());
            getVO.setAssetCategory(listVO.getAssetCategory());
            listVO.setAssetIds(this.fgMarketService.getAssetIdsBySearch(getVO).getData());
            if (listVO.getAssetIds().isEmpty()) {
                return null;
            }
        }
        // 如果选择了是否auto
//        if (!listVO.getIsAuto().equals(-1) || !listVO.getIsShake().equals(-1)) {
//            PlanListByTemplateGetVO getVO = new PlanListByTemplateGetVO();
//            getVO.setIsAuto(listVO.getIsAuto());
//            getVO.setIsShake(listVO.getIsShake());
//            listVO.setPlanIds(this.fgMarketService.listPlanByTemplate(getVO).getData());
//        }
        return listVO;
    }

    /**
     * 完善比对数据
     *
     * @param todayData   今日数据
     * @param compareData 比较数据
     * @return 返回完善数据
     */
    private AssetReportListDTO fillCompleteDto(AssetReportListDTO todayData, AssetReportListDTO compareData) {
        AssetReportListDTO resultData = new AssetReportListDTO();
        if (todayData == null && compareData == null) {
            return resultData;
        }
        if (todayData == null) {
            BeanUtils.copyProperties(compareData, resultData);
        } else {
            // 先将其他查询结果赋值
            BeanUtils.copyProperties(todayData, resultData);
        }
        // 再遍历field依次赋值
        for (Field field : BaseReportDTO.class.getDeclaredFields()) {
            try {
                String firstUpperKey = HumpLineUtils.firstToUpper(field.getName());

                Object fieldData = this.getObjectByField(ObjectUtils.getObjectValue(compareData, field.getName()), field.getType()),
                        todayFieldData = this.getObjectByField(ObjectUtils.getObjectValue(todayData, field.getName()), field.getType());
                ObjectUtils.setObjectValue(resultData, field.getName(), fieldData);
                ObjectUtils.setObjectValue(resultData, "today" + firstUpperKey, todayFieldData);
                // 计算差值
                ObjectUtils.setObjectValue(resultData, "diff" + firstUpperKey,
                        ObjectUtils.getObjectCalculatedResult(todayFieldData, fieldData, "-"));
                // 计算差值比例
                ObjectUtils.setObjectValue(resultData, "diffRate" + firstUpperKey,
                        ObjectUtils.getObjectCalculatedResult(
                                ObjectUtils.getObjectCalculatedResult(ObjectUtils.getObjectValue(resultData, "diff" + firstUpperKey),
                                        100L, "*"), fieldData, "/")
                );
            } catch (Exception exception) {
                log.error(exception.getMessage(), exception);
            }
        }
        return resultData;
    }

    /**
     * 赋值对象
     *
     * @param object    对象
     * @param classType 类型
     * @return 返回数据
     */
    private Object getObjectByField(Object object, Class<?> classType) {
        if (object != null) {
            return object;
        }
        if (classType.equals(Long.class)) {
            return 0L;
        }
        if (classType.equals(Double.class)) {
            return 0D;
        }
        return 0;
    }
}
