package com.overseas.service.report.controller;

import com.overseas.common.dto.R;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterCommonListDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterDiagnosisDTO;
import com.overseas.common.dto.report.flow.center.distribute.FlowDistributeDTO;
import com.overseas.common.dto.report.flow.center.overview.FlowOverviewChartDTO;
import com.overseas.common.vo.report.flow.center.compare.FlowCenterCompareListDownloadVO;
import com.overseas.common.vo.report.flow.center.compare.FlowCenterCompareListVO;
import com.overseas.common.vo.report.flow.center.detail.FlowCenterDetailChartVO;
import com.overseas.common.vo.report.flow.center.detail.FlowCenterDetailListDownloadVO;
import com.overseas.common.vo.report.flow.center.detail.FlowCenterDetailListVO;
import com.overseas.common.vo.report.flow.center.distribute.FlowCenterDistributeListDownloadVO;
import com.overseas.common.vo.report.flow.center.distribute.FlowCenterDistributeVO;
import com.overseas.common.vo.report.flow.center.overview.FlowCenterOverviewVO;
import com.overseas.service.report.service.FlowCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 **/
@RestController
@Slf4j
@RequiredArgsConstructor
@Api(tags = "流量中心")
@RequestMapping("/report/flow/center")
public class FlowCenterReportController {


    private final FlowCenterService flowCenterService;

    @ApiOperation(value = "流量概览趋势", response = FlowOverviewChartDTO.class)
    @PostMapping("/overview/chart")
    public R overviewChart(@RequestBody @Validated FlowCenterOverviewVO overviewVO) {
        return R.data(flowCenterService.overviewChart(overviewVO));
    }

    @ApiOperation(value = "流量概览诊断", response = FlowCenterDiagnosisDTO.class)
    @PostMapping("/overview/diagnosis")
    public R overviewDiagnosis(@RequestBody @Validated FlowCenterOverviewVO overviewVO) {
        return R.data(flowCenterService.overviewDiagnosis(overviewVO));
    }

    @ApiOperation(value = "流量分布图表", response = FlowDistributeDTO.class)
    @PostMapping("/distribute/chart")
    public R distributeChart(@RequestBody @Validated FlowCenterDistributeVO distributeVO) {
        return R.data(flowCenterService.distributeChart(distributeVO));
    }

    @ApiOperation(value = "流量分布图表(趋势图、其他图的数据结构)", response = FlowDistributeDTO.class)
    @PostMapping("/distribute/chart2")
    public R distributeChart2(@RequestBody @Validated FlowCenterDistributeVO distributeVO) {
        return R.data(flowCenterService.distributeChart2(distributeVO));
    }

    @ApiOperation(value = "流量分布列表", response = FlowCenterCommonListDTO.class)
    @PostMapping("/distribute/list")
    public R distributeList(@RequestBody @Validated FlowCenterDistributeVO distributeVO) {
        return R.data(flowCenterService.distributeList(distributeVO));
    }

    @ApiOperation(value = "流量分布列表下载")
    @PostMapping("/distribute/list/export")
    public void distributeListDownload(@RequestBody @Validated FlowCenterDistributeListDownloadVO downloadVO,
                                   HttpServletResponse response) throws IOException {
        flowCenterService.distributeListDownload(downloadVO, response);
    }

    @ApiOperation(value = "流量详情趋势", response = MultiIndexChartDTO.class)
    @PostMapping("/detail/chart")
    public R detailChart(@RequestBody @Validated FlowCenterDetailChartVO detailVO) {
        return R.data(flowCenterService.detailChart(detailVO));
    }

    @ApiOperation(value = "流量详情列表", response = FlowCenterCommonListDTO.class)
    @PostMapping("/detail/list")
    public R detailList(@RequestBody @Validated FlowCenterDetailListVO listVO) {
        return R.data(flowCenterService.detailList(listVO));
    }

    @ApiOperation(value = "流量详情列表下载")
    @PostMapping("/detail/list/export")
    public void detailListDownload(@RequestBody @Validated FlowCenterDetailListDownloadVO downloadVO,
                                   HttpServletResponse response) throws IOException {
        flowCenterService.detailListDownload(downloadVO, response);
    }

    @ApiOperation(value = "流量对比列表", response = FlowCenterCommonListDTO.class)
    @PostMapping("/compare/list")
    public R compareList(@RequestBody @Validated FlowCenterCompareListVO listVO) {
        return R.data(flowCenterService.compareList(listVO));
    }

    @ApiOperation(value = "流量对比列表下载")
    @PostMapping("/compare/list/export")
    public void compareListDownload(@RequestBody @Validated FlowCenterCompareListDownloadVO downloadVO,
                                   HttpServletResponse response) throws IOException {
        flowCenterService.compareListDownload(downloadVO, response);
    }

}
