package com.overseas.service.report.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.report.FlowReportListDTO;
import com.overseas.common.enums.ResultStatusEnum;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.report.flow.FlowReportListVO;
import com.overseas.common.vo.report.flow.FlowReportSizeSelectVO;
import com.overseas.common.vo.report.flow.FlowReportTopListVO;
import com.overseas.service.report.entity.FlowHour;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface FlowReportService extends IService<FlowHour> {

    /**
     * 获取流量查询报表数据
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    PageUtils<FlowReportListDTO> listFlowReport(FlowReportListVO listVO);

    /**
     * 下载流量查询报表
     *
     * @param listVO 查询参数
     */
    void exportFlowReport(FlowReportListVO listVO, HttpServletResponse response) throws IOException;

    ResultStatusEnum exportFlowReportToPath(FlowReportListVO listVO);

    /**
     * 获取指定时间段内的视频和图片尺寸下拉数据
     *
     * @param selectVO 查询参数
     * @return 结果集
     */
    Map<String, List<SelectDTO3>> selectSizeByDate(FlowReportSizeSelectVO selectVO);

    /**
     * 获取流量查询趋势图数据
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    MultiIndexChartDTO getFlowSearchReportChart(FlowReportListVO listVO);

    /**
     * 获取流量查询Top级别数据
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    PageUtils<FlowReportListDTO> getFlowSearchTopList(FlowReportTopListVO listVO);

    List<SelectDTO3> selectCountryByReq(FlowReportSizeSelectVO selectVO);
}
