package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.revenue.RevenueReportDTO;
import com.overseas.common.dto.report.revenue.RevenueReportListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.RevenueDay;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RevenueDayMapper extends BaseMapper<RevenueDay> {

    @Select("SELECT ${ew.sqlSelect} " +
            "FROM (SELECT ${ewDsp.sqlSelect} FROM `m_dsp_ae_revenue_day` AS dsp ${ewDsp.customSqlSegment} ) AS dsp " +
            "LEFT JOIN m_dsp_revenue_day AS customer ON dsp.dim_day = customer.dim_day " +
            "AND dsp.dim_project_id = customer.dim_project_id " +
            "AND dsp.dim_master_id = customer.dim_master_id " +
            "AND dsp.dim_country_id = customer.dim_country_id " +
            "AND dsp.dim_rta_id = customer.dim_rta_id " +
            "${ew.customSqlSegment}")
    IPage<RevenueReportListDTO> listRevenueReport(IPage<RevenueReportListDTO> page,
                                                  @Param(ConstantUtils.WRAPPER) Wrapper<RevenueDay> wrapper,
                                                  @Param("ewDsp") Wrapper<?> dspWrapper);

    @Select("SELECT ${ew.sqlSelect} " +
            "FROM ( SELECT ${ewDsp.sqlSelect} FROM `m_dsp_ae_revenue_day` AS dsp ${ewDsp.customSqlSegment} ) AS dsp " +
            "LEFT JOIN m_dsp_revenue_day AS customer ON dsp.dim_day = customer.dim_day " +
            "AND dsp.dim_project_id = customer.dim_project_id " +
            "AND dsp.dim_master_id = customer.dim_master_id " +
            "AND dsp.dim_country_id = customer.dim_country_id " +
            "AND dsp.dim_rta_id = customer.dim_rta_id " +
            "${ew.customSqlSegment}")
    RevenueReportListDTO getRevenueReportTotal(@Param(ConstantUtils.WRAPPER) Wrapper<RevenueDay> wrapper,
                                               @Param("ewDsp") Wrapper<?> dspWrapper);

    @Insert("<script> " +
            "INSERT INTO m_dsp_revenue_day (`dim_day`,`dim_project_id`,`dim_master_id`,`dim_country_id`," +
            "`dim_rta_id` ${insertFields}) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.day},#{item.projectId},#{item.masterId},#{item.countryId},#{item.rtaId} ${insertParams}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "idx_view = VALUES(idx_view), " +
            "idx_click = VALUES(idx_click), " +
            "idx_income = VALUES(idx_income), " +
            "idx_action1 = VALUES(idx_action1), " +
            "idx_action2 = VALUES(idx_action2), " +
            "idx_action3 = VALUES(idx_action3), " +
            "idx_action4 = VALUES(idx_action4), " +
            "idx_action5 = VALUES(idx_action5), " +
            "idx_action6 = VALUES(idx_action6), " +
            "idx_action7 = VALUES(idx_action7), " +
            "idx_action8 = VALUES(idx_action8), " +
            "idx_action9 = VALUES(idx_action9), " +
            "idx_action10 = VALUES(idx_action10), " +
            "idx_action11 = VALUES(idx_action11), " +
            "idx_action12 = VALUES(idx_action12), " +
            "idx_action13 = VALUES(idx_action13), " +
            "idx_action14 = VALUES(idx_action14), " +
            "idx_action15 = VALUES(idx_action15), " +
            "idx_action16 = VALUES(idx_action16), " +
            "idx_action17 = VALUES(idx_action17), " +
            "idx_action18 = VALUES(idx_action18), " +
            "idx_action19 = VALUES(idx_action19), " +
            "idx_action20 = VALUES(idx_action20), " +
            "idx_action21 = VALUES(idx_action21), " +
            "idx_action22 = VALUES(idx_action22), " +
            "idx_action23 = VALUES(idx_action23), " +
            "idx_action24 = VALUES(idx_action24), " +
            "idx_action25 = VALUES(idx_action25), " +
            "idx_action26 = VALUES(idx_action26), " +
            "idx_action27 = VALUES(idx_action27), " +
            "idx_action28 = VALUES(idx_action28), " +
            "idx_action29 = VALUES(idx_action29), " +
            "idx_action30 = VALUES(idx_action30), " +
            "idx_action31 = VALUES(idx_action31), " +
            "idx_action32 = VALUES(idx_action32), " +
            "idx_action33 = VALUES(idx_action33), " +
            "idx_action34 = VALUES(idx_action34), " +
            "idx_action35 = VALUES(idx_action35), " +
            "idx_action36 = VALUES(idx_action36), " +
            "idx_action37 = VALUES(idx_action37), " +
            "idx_action38 = VALUES(idx_action38), " +
            "idx_action39 = VALUES(idx_action39), " +
            "idx_action40 = VALUES(idx_action40) " +
            "</script>")
    void batchSaveRevenueDayData(@Param("list") List<RevenueReportDTO> list,
                                 @Param("insertFields") String insertFields,
                                 @Param("insertParams") String insertParams);

    @Insert("<script> " +
            "INSERT INTO m_dsp_revenue_day (`dim_day`,`dim_project_id`,`dim_master_id`,`dim_country_id`," +
            "`dim_rta_id` ${insertFields}) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            "(#{item.day},#{item.projectId},#{item.masterId},#{item.countryId},#{item.rtaId} ${insertParams}) " +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE ${updateFields}" +
            "</script>")
    void batchUpdateRevenueDayData(@Param("list") List<RevenueReportDTO> list,
                                   @Param("insertFields") String insertFields,
                                   @Param("insertParams") String insertParams,
                                   @Param("updateFields") String updateFields);

    /**
     * 插入 更换数据
     *
     * @param revenueDays 数据
     *                    数据入库
     */
    @Insert("<script>" +
            "INSERT INTO `m_dsp_ae_revenue_day` " +
            " ( `dim_day`, `dim_node_id`, `dim_project_id`, `dim_master_id`, `dim_rta_id`, `dim_adx_id`," +
            " `dim_country_id`, `idx_bid`, `idx_inner_bid`, `idx_win`, `idx_view`, `idx_user_view`," +
            " `idx_click`, `idx_user_click`," +
            " `idx_media_cost`, `idx_platform_cost`, `idx_report_cost`, `idx_consumer_cost`," +
            " `idx_reach`, `idx_action`, `idx_num_action`, `idx_action1`, `idx_action2`," +
            " `idx_action3`, `idx_action4`, `idx_action5`, `idx_action6`, `idx_action7`," +
            " `idx_action8`, `idx_action9`, `idx_action10`, `idx_action11`, `idx_action12`," +
            " `idx_action13`, `idx_action14`, `idx_action15`, `idx_action16`, `idx_action17`," +
            " `idx_action18`, `idx_action19`, `idx_action20`, `idx_action21`, `idx_action22`," +
            " `idx_action23`, `idx_action24`, `idx_action25`, `idx_action26`, `idx_action27`," +
            " `idx_action28`, `idx_action29`, `idx_action30`, `idx_action31`,`idx_action32`," +
            " `idx_action33`, `idx_action34`, `idx_action35`, `idx_action36`,`idx_action37`," +
            " `idx_action38`, `idx_action39`, `idx_action40`, `idx_settlement`, `idx_click_uniq_session`)" +
            " VALUES  " +
            "<foreach collection='list' item='item' index='index' separator=','> " +
            " (#{item.day}, #{item.nodeId}, #{item.projectId}, #{item.masterId}, #{item.rtaId}, #{item.adxId}," +
            " #{item.countryId}, #{item.bid}, #{item.innerBid}, #{item.win}, #{item.view}, #{item.userView}," +
            " #{item.click}, #{item.userClick}," +
            " #{item.mediaCost}, #{item.platformCost}, #{item.reportCost}, #{item.consumerCost}," +
            " #{item.reach}, #{item.action}, #{item.numAction}, #{item.action1}, #{item.action2}," +
            " #{item.action3}, #{item.action4}, #{item.action5}, #{item.action6}, #{item.action7}," +
            " #{item.action8}, #{item.action9}, #{item.action10}, #{item.action11}, #{item.action12}," +
            " #{item.action13}, #{item.action14}, #{item.action15}, #{item.action16},#{item.action17}," +
            " #{item.action18}, #{item.action19}, #{item.action20}, #{item.action21}, #{item.action22}," +
            " #{item.action23}, #{item.action24}, #{item.action25}, #{item.action26}, #{item.action27}," +
            " #{item.action28}, #{item.action29}, #{item.action30}, #{item.action31}, #{item.action32}," +
            " #{item.action33}, #{item.action34}, #{item.action35}, #{item.action36}, #{item.action37}," +
            " #{item.action38}, #{item.action39}, #{item.action40}, #{item.settlement}, #{item.clickUniqSession}) " +
            "</foreach>" +
            "ON DUPLICATE KEY UPDATE " +
            " idx_bid = VALUES(idx_bid), " +
            " idx_inner_bid = VALUES(idx_inner_bid), " +
            " idx_win = VALUES(idx_win), " +
            " idx_view = VALUES(idx_view), " +
            " idx_user_view = VALUES(idx_user_view), " +
            " idx_click = VALUES(idx_click), " +
            " idx_user_click = VALUES(idx_user_click), " +
            " idx_media_cost = VALUES(idx_media_cost), " +
            " idx_platform_cost = VALUES(idx_platform_cost), " +
            " idx_report_cost = VALUES(idx_report_cost), " +
            " idx_consumer_cost = VALUES(idx_consumer_cost), " +
            " idx_reach = VALUES(idx_reach), " +
            " idx_action = VALUES(idx_action), " +
            " idx_num_action = VALUES(idx_num_action), " +
            " idx_action1 = VALUES(idx_action1), " +
            " idx_action2 = VALUES(idx_action2), " +
            " idx_action3 = VALUES(idx_action3), " +
            " idx_action4 = VALUES(idx_action4), " +
            " idx_action5 = VALUES(idx_action5), " +
            " idx_action6 = VALUES(idx_action6), " +
            " idx_action7 = VALUES(idx_action7), " +
            " idx_action8 = VALUES(idx_action8), " +
            " idx_action9 = VALUES(idx_action9), " +
            " idx_action10 = VALUES(idx_action10), " +
            " idx_action11 = VALUES(idx_action11), " +
            " idx_action12 = VALUES(idx_action12), " +
            " idx_action13 = VALUES(idx_action13), " +
            " idx_action14 = VALUES(idx_action14), " +
            " idx_action15 = VALUES(idx_action15), " +
            " idx_action16 = VALUES(idx_action16), " +
            " idx_action17 = VALUES(idx_action17), " +
            " idx_action18 = VALUES(idx_action18), " +
            " idx_action19 = VALUES(idx_action19), " +
            " idx_action20 = VALUES(idx_action20), " +
            " idx_action21 = VALUES(idx_action21), " +
            " idx_action22 = VALUES(idx_action22), " +
            " idx_action23 = VALUES(idx_action23), " +
            " idx_action24 = VALUES(idx_action24), " +
            " idx_action25 = VALUES(idx_action25), " +
            " idx_action26 = VALUES(idx_action26), " +
            " idx_action27 = VALUES(idx_action27), " +
            " idx_action28 = VALUES(idx_action28), " +
            " idx_action29 = VALUES(idx_action29), " +
            " idx_action30 = VALUES(idx_action30), " +
            " idx_action31 = VALUES(idx_action31), " +
            " idx_action32 = VALUES(idx_action32), " +
            " idx_action33 = VALUES(idx_action33), " +
            " idx_action34 = VALUES(idx_action34), " +
            " idx_action35 = VALUES(idx_action35), " +
            " idx_action36 = VALUES(idx_action36), " +
            " idx_action37 = VALUES(idx_action37), " +
            " idx_action38 = VALUES(idx_action38), " +
            " idx_action39 = VALUES(idx_action39), " +
            " idx_action40 = VALUES(idx_action40), " +
            " idx_settlement = VALUES(idx_settlement)," +
            " idx_click_uniq_session = VALUES(idx_click_uniq_session) " +
            "</script>")
    void batchInsertReplaceData(@Param("list") List<RevenueDay> revenueDays);

    @Select("SELECT count(1) from m_dsp_ae_revenue_day ${ew.customSqlSegment}")
    long getRevenueDayByWraper(@Param(ConstantUtils.WRAPPER)Wrapper<?> wrapper);
}
