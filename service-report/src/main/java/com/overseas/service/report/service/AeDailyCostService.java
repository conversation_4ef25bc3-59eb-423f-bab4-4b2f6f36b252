package com.overseas.service.report.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.report.openApi.ae.AeDailyCostListDTO;
import com.overseas.common.vo.report.openApi.DailyCostListVO;
import com.overseas.service.report.entity.AeDailyCost;

import java.util.List;

public interface AeDailyCostService extends IService<AeDailyCost> {

    List<AeDailyCostListDTO> listAeCampaignDailyCost(DailyCostListVO getVO);
}
