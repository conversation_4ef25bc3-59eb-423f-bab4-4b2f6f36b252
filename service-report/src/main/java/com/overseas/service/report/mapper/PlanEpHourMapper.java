package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.AnalysisReportListDTO;
import com.overseas.common.dto.report.CreativeHourReportListDTO;
import com.overseas.common.dto.report.DimensionTotalDTO;
import com.overseas.common.dto.report.adx.AdxDailyCostListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.PlanEpHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface PlanEpHourMapper extends BaseMapper<PlanEpHour> {

    @Select("SELECT ${finalSelectSql} FROM " +
            "(SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_ep_rta_hour AS m_report " +
            "INNER JOIN iflytek_overseas_dsp.m_plan AS m_plan ON m_plan.id = m_report.dim_plan_id " +
            "${ew.customSqlSegment}) AS report" +
            "${filterSql}")
    IPage<AnalysisReportListDTO> listAdxRtaReport(IPage<AnalysisReportListDTO> page,
                                                  @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                  @Param("finalSelectSql") String finalSelectSql,
                                                  @Param("filterSql") String filterSql);

    @Select("SELECT ${finalSelectSql} FROM " +
            "(SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_ep_rta_hour AS m_report " +
            "INNER JOIN iflytek_overseas_dsp.m_plan AS m_plan ON m_plan.id = m_report.dim_plan_id " +
            "${ew.customSqlSegment}) AS report" +
            "${filterSql}")
    List<AnalysisReportListDTO> getAdxReportListAnalysis(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                         @Param("finalSelectSql") String finalSelectSql,
                                                         @Param("filterSql") String filterSql);


    @Select("SELECT ${finalSelectSql} FROM " +
            "(SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_ep_rta_hour AS m_report " +
            "INNER JOIN iflytek_overseas_dsp.m_plan AS m_plan ON m_plan.id = m_report.dim_plan_id " +
            "${ew.customSqlSegment}) AS report")
    AnalysisReportListDTO getAdxRtaSummaryReportData(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                     @Param("finalSelectSql") String finalSelectSql);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_ep_rta_hour AS report " +
            "LEFT JOIN iflytek_overseas_dsp.d_ep AS ep ON ep.id = report.dim_ep_id " +
            "${ew.customSqlSegment}")
    List<AdxDailyCostListDTO> getAdxDailyCostByEpGroup(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_ep_rta_hour ${ew.customSqlSegment}")
    IPage<CreativeHourReportListDTO> getReportPageList(IPage<CreativeHourReportListDTO> page,
                                                       @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_ep_rta_hour ${ew.customSqlSegment}")
    CreativeHourReportListDTO getTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_ep_rta_hour ${ew.customSqlSegment}")
    List<CreativeHourReportListDTO> getReportList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_ep_rta_hour AS m_report " +
            "INNER JOIN iflytek_overseas_dsp.m_plan AS m_plan ON m_plan.id = m_report.dim_plan_id " +
            "${ew.customSqlSegment}")
    List<CreativeHourReportListDTO> getReportListJoinPlan(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 维度数据查询
     *
     * @param wrapper 筛选条件
     * @return 返回数据
     */
    @Select("SELECT  * from " +
            " (SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_ep_rta_hour" +
            " LEFT JOIN iflytek_overseas_dsp.m_plan " +
            " ON t_ads_dsp_flow_plan_ep_rta_hour.dim_plan_id = iflytek_overseas_dsp.m_plan.id " +
            " ${ew.customSqlSegment}) " +
            "t ${where.customSqlSegment}")
    List<DimensionTotalDTO> dimensionTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                           @Param("where") Wrapper<?> whereWrapper);
}
