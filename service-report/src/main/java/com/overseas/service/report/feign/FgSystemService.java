package com.overseas.service.report.feign;

import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO3;
import com.overseas.common.dto.sys.UserLoginDTO;
import com.overseas.common.dto.sys.customIndex.*;
import com.overseas.common.dto.sys.project.ProjectGetDTO;
import com.overseas.common.dto.sys.project.ProjectByMasterDTO;
import com.overseas.common.dto.sys.project.ProjectResourceDTO;
import com.overseas.common.entity.User;
import com.overseas.common.utils.FeignR;
import com.overseas.common.vo.sys.UserLoginVO;
import com.overseas.common.vo.sys.project.*;
import com.overseas.common.vo.sys.area.AreaCountrySelectVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.common.vo.sys.user.UserMasterGetVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient("ai-overseas-service-system")
public interface FgSystemService {

    @PostMapping("/sys/auth/getUserId")
    FeignR<Integer> getUserId();

    @PostMapping("/sys/auth/getUser")
    FeignR<User> getUser();

    @PostMapping("/sys/auth/listMasterId")
    FeignR<List<Integer>> listMasterId();

    @PostMapping("/sys/auth/login")
    FeignR<UserLoginDTO> authLogin(UserLoginVO userLoginVO);

    @PostMapping("/sys/customIndex/get")
    FeignR<CustomIndexDTO> getUserCustomIndex(CustomIndexGetVO getVO);

    @PostMapping("/sys/customIndex/get/report/select")
    FeignR<String> getUserCustomIndexReportSelect(CustomIndexGetVO getVO);

    @PostMapping("/sys/customIndex/fieldMap/get")
    FeignR<List<CustomIndexFieldDTO>> getCustomIndexFieldMap(CustomIndexGetVO getVO);

    @PostMapping("/sys/customIndex/fieldMap/get/v2")
    FeignR<List<CustomIndexFieldDTO>> getCustomIndexFieldMapV2(CustomIndexGetVO getVO);

    @PostMapping("/sys/customIndex/get/report")
    FeignR<List<CustomIndexChildColumnDTO>> getUserCustomIndexReport(CustomIndexGetVO getVO);

    @PostMapping("/sys/customIndex/get/report/download")
    FeignR<List<CustomIndexParentColumnDTO>> getUserCustomIndexReportDownload(CustomIndexGetVO getVO);

    @PostMapping("/sys/customIndex/byModule/get")
    FeignR<List<CustomIndexContentDTO>> getCustomIndexByModule(CustomIndexGetVO getVO);

    @PostMapping("/sys/resource/area/countries/select")
    FeignR<List<SelectDTO3>> listCountry(AreaCountrySelectVO selectVO);

    @PostMapping("/sys/auth/listMasterId")
    FeignR<List<Integer>> getMasterIds();

    @PostMapping("/sys/projects/by/master")
    FeignR<ProjectByMasterDTO> getProjectByMaster(ProjectByMasterVO projectByMasterVO);

    @PostMapping("/sys/projects/get")
    FeignR<ProjectGetDTO> getProject(ProjectGetVO getVO);

    @PostMapping("/sys/resource/countries/select")
    FeignR<List<SelectDTO>> selectCountry(AreaCountrySelectVO selectVO);

    @PostMapping("/sys/auth/masterIds/get")
    FeignR<List<Long>> getMasterIdsByProjectId(UserMasterGetVO getVO);

    @PostMapping("/sys/auth/masterIds/get/all")
    FeignR<List<Long>> getAllMasterIdsByProjectId(UserMasterGetVO getVO);

    @PostMapping("/sys/projects/master/project")
    FeignR<Map<Long, Long>> getMasterProject(ProjectForMasterVO masterVO);

    @PostMapping("/sys/projects/resource")
    FeignR<ProjectResourceDTO> projectResource(ProjectResourceVO projectResourceVO);

    @PostMapping("/sys/projects/select/by/info")
    FeignR<List<SelectDTO>> selectProjectByInfo(ProjectSelectByInfoVO idsVO);
}
