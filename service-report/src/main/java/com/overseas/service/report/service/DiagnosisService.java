package com.overseas.service.report.service;

import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.report.diagnosis.DiagnosisDetailInfoDTO;
import com.overseas.common.dto.report.diagnosis.DiagnosisPutInfoDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterCommonListDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterDiagnosisDTO;
import com.overseas.common.vo.report.diagnosis.DiagnosisDetailInfoChartVO;
import com.overseas.common.vo.report.diagnosis.DiagnosisDetailInfoVO;
import com.overseas.common.vo.report.diagnosis.DiagnosisDetailListVO;
import com.overseas.common.vo.report.diagnosis.DiagnosisPutInfoVO;
import com.overseas.common.vo.report.flow.center.overview.FlowCenterOverviewVO;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface DiagnosisService {

    /**
     * 投放详情
     *
     * @param putInfoVO 投放详情
     * @return 返回数据
     */
    DiagnosisPutInfoDTO putInfo(DiagnosisPutInfoVO putInfoVO);

    /**
     * 投放细节
     *
     * @param overviewVO 条件
     * @return 返回数据
     */
    FlowCenterDiagnosisDTO putDetail(FlowCenterOverviewVO overviewVO);

    /**
     * 数据
     *
     * @param chartVO 条件
     * @return 返回数据
     */
    MultiIndexChartDTO putDetailChart(DiagnosisDetailInfoChartVO chartVO);

    /**
     * 投放详情数据
     *
     * @param listVO 计划ID
     * @return 返回数据
     */
    List<FlowCenterCommonListDTO> detailList(DiagnosisDetailListVO listVO);

    /**
     * 投放内容
     *
     * @param infoVO 投放内容
     * @return 返回数据
     */
    DiagnosisDetailInfoDTO detailInfo(DiagnosisDetailInfoVO infoVO);


}
