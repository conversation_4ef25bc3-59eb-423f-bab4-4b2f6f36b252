package com.overseas.service.report.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterCommonListDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterCompareListDTO;
import com.overseas.common.dto.report.flow.center.FlowCenterDiagnosisDTO;
import com.overseas.common.dto.report.flow.center.distribute.FlowDistributeDTO;
import com.overseas.common.dto.report.flow.center.overview.FlowOverviewChartDTO;
import com.overseas.common.vo.report.flow.center.FlowCenterReportListVO;
import com.overseas.common.vo.report.flow.center.compare.FlowCenterCompareListDownloadVO;
import com.overseas.common.vo.report.flow.center.compare.FlowCenterCompareListVO;
import com.overseas.common.vo.report.flow.center.detail.FlowCenterDetailChartVO;
import com.overseas.common.vo.report.flow.center.detail.FlowCenterDetailListDownloadVO;
import com.overseas.common.vo.report.flow.center.detail.FlowCenterDetailListVO;
import com.overseas.common.vo.report.flow.center.distribute.FlowCenterDistributeListDownloadVO;
import com.overseas.common.vo.report.flow.center.distribute.FlowCenterDistributeVO;
import com.overseas.common.vo.report.flow.center.overview.FlowCenterOverviewVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 **/
public interface FlowCenterService {

    /**
     * 概览趋势图
     *
     * @param overviewVO 参数
     * @return 返回数据
     */
    List<FlowOverviewChartDTO> overviewChart(FlowCenterOverviewVO overviewVO);

    /**
     * 概览错误数据
     *
     * @param overviewVO 参数
     * @return 返回数据
     */
    FlowCenterDiagnosisDTO overviewDiagnosis(FlowCenterOverviewVO overviewVO);

    /**
     * 分布情况数据
     *
     * @param distributeVO 参数
     * @return 返会数据
     */
    List<FlowDistributeDTO> distributeChart(FlowCenterDistributeVO distributeVO);

    /**
     * 分布情况数据
     *
     * @param distributeVO 参数
     * @return 返会数据
     */
    MultiIndexChartDTO distributeChart2(FlowCenterDistributeVO distributeVO);

    /**
     * 分布情况列表
     *
     * @param distributeVO 地域请求
     * @return 返回数据
     */
    List<FlowCenterCommonListDTO> distributeList(FlowCenterDistributeVO distributeVO);

    /**
     * 分布情况列表下载
     *
     * @param listVO   条件
     * @param response 返回数据
     * @return 返回数据
     * @throws IOException 异常
     */
    void distributeListDownload(FlowCenterDistributeListDownloadVO listVO, HttpServletResponse response) throws IOException;

    /**
     * 详情趋势图
     *
     * @param detailVO 条件
     * @return 返回数据
     */
    MultiIndexChartDTO detailChart(FlowCenterDetailChartVO detailVO);

    /**
     * 详情列表数据
     *
     * @param listVO 条件
     * @return 返回数据
     */
    List<FlowCenterCommonListDTO> detailList(FlowCenterDetailListVO listVO);

    /**
     * 详情列表数据
     *
     * @param listVO   条件
     * @param response 返回数据
     * @return 返回数据
     * @throws IOException 异常
     */
    void detailListDownload(FlowCenterDetailListDownloadVO listVO, HttpServletResponse response) throws IOException;

    /**
     * 对比数据
     *
     * @param listVO 条件
     * @return 返回数据
     */
    List<FlowCenterCompareListDTO> compareList(FlowCenterCompareListVO listVO);

    /**
     * 详情列表数据
     *
     * @param listVO   条件
     * @param response 返回数据
     * @return 返回数据
     * @throws IOException 异常
     */
    void compareListDownload(FlowCenterCompareListDownloadVO listVO, HttpServletResponse response) throws IOException;


    /**
     * 获取list 数据
     *
     * @param listVO         条件
     * @param tTypeReference 类型
     * @param <T>            类型
     * @return 返回数据
     */
    <T> T getFlowData(FlowCenterReportListVO listVO, TypeReference<T> tTypeReference);

    /**
     * 获取诊断详情数据
     *
     * @param listVO 筛选条件
     * @return 返回数据
     */
    FlowCenterDiagnosisDTO getDiagnosisData(FlowCenterReportListVO listVO);


    /**
     * 获取数据
     *
     * @param current   当前数据
     * @param last      上期数据
     * @param range     当前range
     * @param lastRange 上期range
     * @param dayOf     时间类型
     * @param fields    字段类型
     * @return 返回数据
     */
    List<FlowOverviewChartDTO> completeChart(List<JSONObject> current, List<JSONObject> last,
                                             List<String> range, List<String> lastRange,
                                             String dayOf, Map<String, String> fields);

}
