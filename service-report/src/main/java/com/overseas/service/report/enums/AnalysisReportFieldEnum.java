package com.overseas.service.report.enums;

import com.overseas.common.exception.CustomException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum AnalysisReportFieldEnum {

    //
    REPORT_DATE("reportDate", "日期", "`day`", "reportDate"),
    REPORT_HOUR("reportHour", "小时", "`day_hour`", "hour"),
    MASTER("masterName", "账号", "dim_master_id", "masterId"),
    CAMPAIGN("campaignName", "活动", "dim_campaign_id", "campaignId"),
    PLAN("planName", "计划", "dim_plan_id", "planId"),
    PLAN_GROUP("planGroupName", "计划组", "dim_plan_group_id", "planGroupId"),
    ADX("adxName", "ADX", "dim_adx_id", "adxId"),
    EP("epName", "EP", "dim_ep_id", "epId"),
    RTA("rtaName", "RTA", "dim_rta_id", "rtaId"),
    SLOT_TYPE("slotTypeName", "广告形式", "dim_ad_type", "slotType"),
    FLOW_TYPE("flowTypeName", "广告形式", "dim_flow_type", "flowType"),
    PACKAGE("pkg", "包名", "dim_pkg", "pkg"),
    SSP("ssp", "SSP", "dim_ssp", "ssp"),
    SUPPLIER_CHAIN("supplierChain", "SSP层级", "dim_supplier_chain", "supplierChain"),
    DEAL_ID("dealId", "dealId", "dim_deal_id", "dealId"),
    SLOT("slotId", "slotId", "dim_slot_id", "slotId");

    private final String key;

    private final String name;

    private final String field;

    private final String fieldKey;

    public static AnalysisReportFieldEnum get(String key) {

        for (AnalysisReportFieldEnum enumValue : values()) {
            if (enumValue.getKey().equals(key)) {
                return enumValue;
            }
        }
        throw new CustomException("枚举值超出预设范围");
    }

    public static List<String> getKeys() {

        return new ArrayList<>() {{
            for (AnalysisReportFieldEnum enumValue : values()) {
                add(enumValue.getKey());
            }
        }};
    }

    public static List<String> getFieldsByKeys(List<String> keys) {

        return new ArrayList<>() {{
            for (AnalysisReportFieldEnum enumValue : values()) {
                keys.forEach(key -> {
                    if (key.equals(enumValue.getKey())) {
                        add(enumValue.getField());
                    }
                });
            }
        }};
    }

    public static String getFieldByKey(String key) {

        if (StringUtils.isBlank(key)) {
            return "";
        }
        for (AnalysisReportFieldEnum enumValue : values()) {
            if (List.of(REPORT_DATE.getKey(), REPORT_HOUR.getKey()).contains(key)) {
                return "dim_report_hour";
            }
            if (enumValue.getKey().equals(key)) {
                return enumValue.getField();
            }
        }
        return "";
    }
}
