package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.rta.RtaReportListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.RtaReport;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface RtaReportMapper extends BaseMapper<RtaReport> {

    /**
     * 列表数据
     *
     * @param iPage   分页
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("select * from " +
            "( " +
            "SELECT ${ew.sqlSelect} " +
            "from iflytek_overseas_service.m_dsp_rta_report " +
            "left join iflytek_overseas_dsp.d_rta_strategy ON d_rta_strategy.id = m_dsp_rta_report.dim_rta_strategy_id " +
            "${ew.customSqlSegment} " +
            ") " +
            "ts ${order.customSqlSegment}")
    IPage<RtaReportListDTO> list(IPage<?> iPage, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                 @Param("order") Wrapper<?> order);


    /**
     * 列表数据
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT ${ew.sqlSelect} " +
            "from iflytek_overseas_service.m_dsp_rta_report " +
            "left join iflytek_overseas_dsp.d_rta_strategy ON d_rta_strategy.id = m_dsp_rta_report.dim_rta_strategy_id " +
            "${ew.customSqlSegment}")
    RtaReportListDTO total(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 列表数据
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT ${ew.sqlSelect} from iflytek_overseas_service.m_dsp_rta_report ${ew.customSqlSegment}")
    List<RtaReportListDTO> list2(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
