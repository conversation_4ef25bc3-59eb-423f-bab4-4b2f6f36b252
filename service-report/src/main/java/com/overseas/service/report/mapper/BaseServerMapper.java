package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.entity.BaseServer;
import com.overseas.common.utils.ConstantUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface BaseServerMapper extends BaseMapper<BaseServer> {

    @Select("SELECT * FROM iflytek_overseas_dsp.d_server ${ew.customSqlSegment}")
    List<BaseServer> listBaseServer(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
