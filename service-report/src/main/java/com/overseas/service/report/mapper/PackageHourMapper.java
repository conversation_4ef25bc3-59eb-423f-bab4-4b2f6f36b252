package com.overseas.service.report.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.AnalysisReportListDTO;
import com.overseas.common.dto.report.CreativeHourReportListDTO;
import com.overseas.common.dto.report.DimensionTotalDTO;
import com.overseas.common.dto.report.adx.RevenuePkgListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.PackageHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DS("ck")
public interface PackageHourMapper extends BaseMapper<PackageHour> {

    /**
     * 列表
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_ep_pkg_hour ${ew.customSqlSegment}")
    List<CreativeHourReportListDTO> listReportJoinPlan(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 分页数据
     *
     * @param page           分页
     * @param wrapper        条件
     * @param finalSelectSql 最终数据
     * @param filterSql      过滤数据
     * @return 返回数据
     */
    @Select("SELECT ${finalSelectSql} FROM " +
            "( SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_ep_pkg_hour ${ew.customSqlSegment} ) AS report " +
            "${filterSql}")
    IPage<AnalysisReportListDTO> listPackageReport(IPage<AnalysisReportListDTO> page,
                                                   @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                   @Param("finalSelectSql") String finalSelectSql,
                                                   @Param("filterSql") String filterSql);

    /**
     * 获取数据
     *
     * @param wrapper        条件
     * @param finalSelectSql 最终数据
     * @param filterSql      过滤数据
     * @return 返回数据
     */
    @Select("SELECT ${finalSelectSql} FROM " +
            "(SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_ep_pkg_hour ${ew.customSqlSegment}) AS report " +
            "${filterSql}")
    List<AnalysisReportListDTO> listPackageReportByTemp(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                        @Param("finalSelectSql") String finalSelectSql,
                                                        @Param("filterSql") String filterSql);

    /**
     * 总数
     *
     * @param wrapper        条件
     * @param finalSelectSql 最终数据
     * @param filterSql      过滤数据
     * @return 返回数据
     */
    @Select("SELECT ${finalSelectSql} FROM " +
            "(SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_ep_pkg_hour ${ew.customSqlSegment}) AS report " +
            "${filterSql}")
    AnalysisReportListDTO getPackageSummaryReportDataByTemp(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                            @Param("finalSelectSql") String finalSelectSql,
                                                            @Param("filterSql") String filterSql);

//    @Insert("INSERT INTO m_dsp_flow_plan_pkg_hour (${pkgFields}) " +
//            "SELECT ${pkgFields} " +
//            "FROM t_ads_dsp_flow_plan_rta_pkg_hour " +
//            "WHERE dim_day = ${reportDay} " +
//            "AND dim_report_hour = ${reportDate} " +
//            "AND dim_master_id = ${masterId} " +
//            "AND idx_report_cost > ${masterCost} " +
//            "ON DUPLICATE KEY UPDATE ${pkgUpdateKeys}")
//    void savePkgReportIntoTemp(@Param("pkgFields") String pkgFields, @Param("pkgUpdateKeys") String pkgUpdateKeys,
//                               @Param("reportDay") Long reportDay, @Param("reportDate") Long reportDate,
//                               @Param("masterId") Long masterId, @Param("bid") Long bid,
//                               @Param("masterCost") Double masterCost);
//
//    @Insert("INSERT INTO m_dsp_flow_plan_pkg_hour (${pkgFields}) " +
//            "SELECT ${pkgFields} " +
//            "FROM t_ads_dsp_flow_plan_rta_ep_pkg_hour " +
//            "${ew.customSqlSegment} " +
//            "ON DUPLICATE KEY UPDATE ${pkgUpdateKeys}")
//    void savePkgReport2Temp(@Param("pkgFields") String pkgFields, @Param("pkgUpdateKeys") String pkgUpdateKeys,
//                            @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
//
//    @Delete("DELETE FROM m_dsp_flow_plan_pkg_hour WHERE dim_day <= ${reportDate}")
//    void deletePkgTemp(@Param("reportDate") Long reportDate);

    /**
     * 分包数据
     *
     * @param wrapper 条件
     * @return 返回数据
     */
    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_ep_pkg_hour ${ew.customSqlSegment}")
    List<CreativeHourReportListDTO> listPkgReport(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    /**
     * 总数
     *
     * @param wrapper 条件
     * @return 返回数据
     **/
    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_ep_pkg_hour ${ew.customSqlSegment}")
    CreativeHourReportListDTO getTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 维度数据查询
     *
     * @param wrapper      筛选条件
     * @param whereWrapper 筛选外部条件
     * @return 返回数据
     */
    @Select("SELECT * from (" +
            "SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_ep_pkg_hour ${ew.customSqlSegment}) AS report " +
            "${where.customSqlSegment}")
    List<DimensionTotalDTO> dimensionTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                           @Param("where") Wrapper<?> whereWrapper);


    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_ep_pkg_hour ${ew.customSqlSegment} AS report")
    List<RevenuePkgListDTO> revenueList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);
}
