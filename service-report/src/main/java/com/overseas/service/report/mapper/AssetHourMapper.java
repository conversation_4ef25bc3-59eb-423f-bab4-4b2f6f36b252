package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.AssetReportListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.AssetHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AssetHourMapper extends BaseMapper<AssetHour> {

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_asset_hour ${ew.customSqlSegment}")
    List<AssetReportListDTO> getAssetReportList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_asset_hour ${ew.customSqlSegment}")
    IPage<AssetReportListDTO> listAssetReport(IPage<?> iPage, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_asset_hour ${ew.customSqlSegment}")
    AssetReportListDTO getAssetReportTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Update("UPDATE t_ads_dsp_flow_asset_hour AS report " +
            "INNER JOIN iflytek_overseas_dsp.m_asset AS ma ON report.dim_asset_id = ma.id " +
            "SET report.dim_user_id = ma.create_uid " +
            "WHERE report.dim_user_id = 0 AND report.dim_day = #{reportDate}")
    void batchUpdateAssetHourUserId(@Param("reportDate") Long reportDate);

    @Update("UPDATE t_ads_dsp_flow_asset_hour " +
            "SET dim_report_hour = dim_day + dim_hour * 3600 " +
            "WHERE dim_day > #{reportDate} AND dim_report_hour = 0 ")
    void updateAssetReportHour(@Param("reportDate") Long reportDate);
}
