package com.overseas.service.report.service;

import com.overseas.common.dto.report.revenue.RevenueForUpdateDTO;
import com.overseas.common.dto.report.revenue.RevenueReportListDTO;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.report.revenue.RevenueReportListVO;
import com.overseas.common.vo.report.revenue.RevenueReportSaveVO;
import com.overseas.common.vo.report.revenue.RevenueUpdateVO;
import com.overseas.service.report.entity.RevenueDay;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface RevenueDayService {

    PageUtils<RevenueReportListDTO> listRevenueReport(RevenueReportListVO listVO);

    void exportRevenueReport(RevenueReportListVO listVO, HttpServletResponse response) throws IOException;

    void saveRevenueData(RevenueReportSaveVO saveVO);

    /**
     * 手动修改营收数据
     */
    void updateRevenueData(RevenueUpdateVO updateVO);

    /**
     * 更新需要更新的账户，时间，时区数据
     *
     * @param day 日期
     */
    void revenueForUpdate(String day);

    /**
     * 获取需要更新的账户，时间，时区数据
     *
     * @param day 日期
     * @return 返回数据
     */
    List<RevenueForUpdateDTO> revenueInfoForUpdate(String day);

    /**
     * 跟新某一批账号，某个时区，某天数据
     *
     * @param masterId 账户ID
     * @param timezone 时区
     * @param day      天数据
     */
    void saveDspDataByMaster(List<Long> masterId, Integer timezone, String day);

    /**
     * 一段时间内的DSP数据
     *
     * @param start 开始时间
     * @param end   结束时间
     */
    void saveDspDateIn(String start, String end);

    /**
     * 保存DSP数据
     *
     * @param day 当前小时时间
     */
    void saveDspData(String day);

    /**
     * 获取DSP数据
     *
     * @param day 当前小时时间
     * @return 返回数据
     */
    List<RevenueDay> listDspData(String day);

    /**
     * 查询DSP账户，时区下的数据
     *
     * @param masterIds    账户IDs
     * @param timeZoneEnum 时区
     * @param day          时间（需要录入的时间，即账户时区的天数据）
     * @return 返回数据
     */
    List<RevenueDay> listDspData(List<Long> masterIds, TimeZoneEnum timeZoneEnum, String day);
}
