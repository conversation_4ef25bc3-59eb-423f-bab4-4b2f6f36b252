package com.overseas.service.report.enums;

import com.overseas.common.exception.CustomException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum RevenueDimensionFieldEnum {

    REPORT_DATE("reportDate", "日期", "dim_day", "dsp.dim_day AS `day`", "customer.dim_day = dsp.dim_day", "`day`"),
    MASTER("masterName", "账号", "dim_master_id", "dsp.dim_master_id AS master_id", "customer.dim_master_id = dsp.dim_master_id", "master_id"),
    COUNTRY("countryName", "国家", "dim_country_id", "dsp.dim_country_id AS country_id", "customer.dim_country_id = dsp.dim_country_id", "country_id"),
    RTA("rtaName", "RTA", "dim_rta_id", "dsp.dim_rta_id AS rta_id", "customer.dim_rta_id = dsp.dim_rta_id", "rta_id");

    private final String key;

    private final String title;

    private final String field;

    private final String selectSql;

    private final String joinSql;

    private final String orderSql;

    public static RevenueDimensionFieldEnum get(String key) {
        for (RevenueDimensionFieldEnum fieldEnum : values()) {
            if (fieldEnum.getKey().equals(key)) {
                return fieldEnum;
            }
        }
        throw new CustomException("维度异常");
    }

    public static String getFieldsByKeys(List<String> keys, String prefix) {
        return String.join(",", new ArrayList<>() {{
            for (RevenueDimensionFieldEnum fieldEnum : values()) {
                if (keys.contains(fieldEnum.getKey())) {
                    add(prefix + fieldEnum.getField());
                }
            }
        }});
    }

    public static String getFieldsByKeys(List<String> keys) {
        return String.join(",", new ArrayList<>() {{
            for (RevenueDimensionFieldEnum fieldEnum : values()) {
                if (keys.contains(fieldEnum.getKey())) {
                    add(fieldEnum.getField());
                }
            }
        }});
    }

    public static String getSelectByKeys(List<String> keys) {
        return String.join(",", new ArrayList<>() {{
            for (RevenueDimensionFieldEnum fieldEnum : values()) {
                if (keys.contains(fieldEnum.getKey())) {
                    add(fieldEnum.getSelectSql());
                }
            }
        }});
    }

    public static String getJoinSqlByKeys(List<String> keys) {
        return String.join(" AND ", new ArrayList<>() {{
            for (RevenueDimensionFieldEnum fieldEnum : values()) {
                if (keys.contains(fieldEnum.getKey())) {
                    add(fieldEnum.getJoinSql());
                }
            }
        }});
    }
}
