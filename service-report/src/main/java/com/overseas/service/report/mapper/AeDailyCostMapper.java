package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.overseas.common.dto.report.openApi.ae.AeDailyCostListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.AeDailyCost;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AeDailyCostMapper extends BaseMapper<AeDailyCost> {

    @Select("SELECT * FROM m_ae_daily_cost ${ew.customSqlSegment}")
    List<AeDailyCostListDTO> listDailyCost(@Param(ConstantUtils.WRAPPER) Wrapper<AeDailyCost> wrapper);
}