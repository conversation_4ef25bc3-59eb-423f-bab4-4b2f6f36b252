package com.overseas.service.report.service;

import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.report.rta.RtaReportListDTO;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.report.rta.RtaReportListExportVO;
import com.overseas.common.vo.report.rta.RtaReportListVO;
import com.overseas.common.vo.report.rta.RtaReportTrendyVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 **/
public interface RtaReportService {

    /**
     * 列表数据
     *
     * @param listVO 条件
     * @return 返回数据
     */

    PageUtils<?> passList(RtaReportListVO listVO);

    /**
     * 趋势图
     *
     * @param trendyVO 条件
     * @return 返回数据
     */
    MultiIndexChartDTO passTrendy(RtaReportTrendyVO trendyVO);


    /**
     * 下载列表
     *
     * @param listVO   条件
     * @param response response
     * @throws IOException 异常
     */
    void exportPassList(RtaReportListExportVO listVO, HttpServletResponse response) throws IOException;

    /**
     * 开始时间结束时间内的数据
     *
     * @param start 开始
     * @param end   结束
     * @return 数据
     */
    List<RtaReportListDTO> passData(Long start, Long end);
}
