package com.overseas.service.report.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.overseas.common.dto.market.reportTask.ReportExportTaskDTO;
import com.overseas.common.dto.report.CustomReportFieldRuleDTO;
import com.overseas.common.enums.ICommonEnum;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.reportTask.ReportTaskStatusEnum;
import com.overseas.common.enums.market.reportTask.ReportTaskTypeEnum;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.utils.FeignR;
import com.overseas.common.utils.ObjectUtils;
import com.overseas.common.utils.UploadUtils;
import com.overseas.common.vo.market.reportTask.ReportExportTaskGetVO;
import com.overseas.common.vo.market.reportTask.ReportExportTaskUpdateVO;
import com.overseas.common.vo.report.AnalysisReportListVO;
import com.overseas.common.vo.report.ReportListVO;
import com.overseas.common.vo.report.flow.FlowReportListVO;
import com.overseas.service.report.enums.AnalysisReportFieldEnum;
import com.overseas.service.report.feign.FgMarketService;
import com.overseas.service.report.service.AnalysisReportService;
import com.overseas.service.report.service.FlowReportService;
import com.overseas.service.report.service.ReportScheduleService;
import com.overseas.service.report.service.ReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReportScheduleServiceImpl implements ReportScheduleService {

    private final FgMarketService fgMarketService;

    private final ReportService reportService;

    private final FlowReportService flowReportService;

    private final AnalysisReportService analysisReportService;

    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    /**
     * 离线任务定时器
     */
    @Override
    public void exportOfflineTask() {

        // 1.查询是否存在待执行的任务
        ReportExportTaskDTO reportExportTask = this.getReportExportTaskDTO(new ReportExportTaskGetVO());
        // 如果没有待执行的任务，则直接返回
        if (reportExportTask == null) {
            log.info("当前无待执行导出的任务。");
            return;
        }

        // 如果存在则执行后续导出流程
        log.info("存在待导出任务；当前执行任务ID：{}", reportExportTask.getId());
        this.executeExportOfflineReport(reportExportTask);
    }

    @Override
    public void exportOfflineIncludePkgTask() {

        // 1.查询是否存在待执行的任务
        ReportExportTaskGetVO getVO = new ReportExportTaskGetVO();
        getVO.setIncludePkg(1);
        ReportExportTaskDTO reportExportTask = this.getReportExportTaskDTO(getVO);
        // 如果没有待执行的任务，则直接返回
        if (reportExportTask == null) {
            log.info("当前无待执行导出的任务。");
            return;
        }

        // 2.将任务状态调整至更新SQL
        ReportExportTaskUpdateVO updateVO = new ReportExportTaskUpdateVO();
        updateVO.setId(reportExportTask.getId());
        updateVO.setTaskStatus(ReportTaskStatusEnum.EXECUTING_SQL_SCRIPT.getId());
        this.fgMarketService.updateReportTask(updateVO);

        // 3.获取账号获取时区
//        MasterTimeZoneGetVO masterTimeZoneGetVO = new MasterTimeZoneGetVO();
//        masterTimeZoneGetVO.setMasterId(reportExportTask.getMasterId());
//        masterTimeZoneGetVO.setTimeZone(999);
//        Integer timeZone = this.fgMarketService.getMasterTimeZone(masterTimeZoneGetVO).getData().get(0).getTimeZone();
        // 4.获取需要更新的日期时间
//        List<String> dates = new ArrayList<>() {{
//            if (ReportTypeEnum.TIME_COMPARE.getId().equals(reportExportTask.getTaskType())) {
//                addAll(List.of(reportExportTask.getStartDate(), reportExportTask.getEndDate()));
//            } else {
//                addAll(DateUtils.getBetweenDate(reportExportTask.getStartDate(), reportExportTask.getEndDate(), "yyyy-MM-dd"));
//            }
//        }};
//        Map<String, Object> map = JSONObject.parseObject(reportExportTask.getSearchData());
//        String startHourStr = this.getHourStr(map.get("startHour"), "00:00:00"),
//                endHourStr = this.getHourStr(map.get("endHour"), "23:00:00");

//        List<String> reportHourStrList = new ArrayList<>() {{
//            dates.forEach(date -> addAll(DateUtils.getBetweenDateHourList(
//                    TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(date + " " + startHourStr), timeZone, 0),
//                    TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(date + " " + endHourStr), timeZone, 0))
//            ));
//        }};
//        log.info("date: {}", reportHourStrList);
        // 5.执行线程
//        long startTime = System.currentTimeMillis();
//        List<CompletableFuture<Void>> futures = new ArrayList<>();
//        reportHourStrList.forEach(reportHourStr -> futures.add(CompletableFuture.runAsync(() -> {
//            Long reportHour = DateUtils.string2Long(reportHourStr);
//            try {
//                log.info("start insert data, date is : " + reportHourStr);
//                // 执行SQL
//                this.analysisReportService.savePkgReportIntoTemp(reportHour, reportExportTask.getMasterId());
//            } catch (Exception exception) {
//                // 如果失败了，重新执行SQL
//                log.error(exception.getMessage(), exception);
//                this.analysisReportService.savePkgReportIntoTemp(reportHour, reportExportTask.getMasterId());
//            }
//            log.info("finish insert data, date is : " + reportHourStr);
//        }, executorService)));
//        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

//        log.info("execute all insert sql, cost {}", (System.currentTimeMillis() - startTime) / 1000);
        // 6.执行后续导出流程
        log.info("存在待导出任务；当前执行任务ID：{}", reportExportTask.getId());
        this.executeExportOfflineReport(reportExportTask);
    }

    private String getHourStr(Object object, String defaultValue) {
        if (object == null) {
            return defaultValue;
        }
        int hour = Integer.parseInt(object.toString());
        return (hour < 10 ? "0" : "") + hour + ":00:00";
    }

    private ReportExportTaskDTO getReportExportTaskDTO(ReportExportTaskGetVO getVO) {

        // 1.查询是否存在待执行的任务
        FeignR<ReportExportTaskDTO> feignR = this.fgMarketService.getWaitToCreateTask(getVO);
        if (!feignR.getCode().equals(0)) {
            log.error("获取离线导出任务异常：{}", feignR.getMsg());
        }
        return feignR.getData();
    }

    /**
     * 导出任务执行流程
     *
     * @param reportExportTask 任务
     */
    private void executeExportOfflineReport(ReportExportTaskDTO reportExportTask) {

        // 1.开始执行导出流程，先将任务状态置为执行中
        long startTime = System.currentTimeMillis();
        log.info("离线任务开始导出，任务ID：{}", reportExportTask.getId());
        ReportExportTaskUpdateVO updateVO = new ReportExportTaskUpdateVO();
        updateVO.setId(reportExportTask.getId());
        updateVO.setTaskStatus(ReportTaskStatusEnum.CREATING.getId());
        this.fgMarketService.updateReportTask(updateVO);

        // 2.执行导出流程
        try {
            this.executeTaskFunction(reportExportTask);
            log.info("导出报表离线任务成功：任务ID：{}", reportExportTask.getId());
            updateVO.setTaskStatus(ReportTaskStatusEnum.SUCCESS.getId());
            updateVO.setFileName(reportExportTask.getFileName());
            updateVO.setFilePath(reportExportTask.getFilePath());
        } catch (Exception exception) {
            updateVO.setTaskStatus(ReportTaskStatusEnum.FAIL.getId());
            updateVO.setTaskRemark(exception.getMessage());
            // 失败了清空任务名称和路径
            updateVO.setFileName("");
            updateVO.setFilePath("");
            log.error(exception.getMessage(), exception);
            log.info("导出报表离线任务失败：任务ID：{}；失败原因：{}", reportExportTask.getId(), reportExportTask.getTaskRemark());
        } finally {
            log.info("任务执行结束，耗时：{}", System.currentTimeMillis() - startTime);
            this.fgMarketService.updateReportTask(updateVO);
        }
    }

    /**
     * 任务执行方法
     *
     * @param reportExportTask 任务
     */
    private void executeTaskFunction(ReportExportTaskDTO reportExportTask) {

        // 设置文件名称与文件地址
        String fileName = reportExportTask.getTaskName() + "_" + (reportExportTask.getStartDate().equals(reportExportTask.getEndDate()) ?
                reportExportTask.getStartDate() : reportExportTask.getStartDate() + "_" + reportExportTask.getEndDate()) + ".xlsx";
        String filePath = "/overseasReportTask/" + ICommonEnum.get(reportExportTask.getTaskType(), ReportTaskTypeEnum.class).getFilePath() +
                "/" + DateUtils.format(new Date(), "yyyy/MM/dd");

        File path = new File(UploadUtils.getUploadPath(filePath));
        if (!path.exists()) {
            path.mkdirs();
        }
        filePath += "/" + DigestUtils.md5DigestAsHex(((new Date()).getTime() + Math.random() * 10000 + "").getBytes()) + ".xlsx";
        // 设置任务导出文件存储信息
        reportExportTask.setFileName(fileName);
        reportExportTask.setFilePath(filePath);
        // 执行导出任务
        ReportTaskTypeEnum reportTaskTypeEnum = ICommonEnum.get(reportExportTask.getTaskType(), ReportTaskTypeEnum.class);
        log.info("导出报表离线任务开始执行：任务ID：{}；报表类型：{}报表", reportExportTask.getId(), reportTaskTypeEnum.getName());

        switch (reportTaskTypeEnum) {
            case TIME_COMPARE:
                this.executeExportReport(reportExportTask, filePath);
                break;
            case PACKAGE:
            case FLOW:
                // 执行自定义报表导出
                this.executeExportCustomReport(reportExportTask, filePath);
                break;
            case FLOW_SEARCH:
                // 执行流量查询导出
                this.executeExportFlowSearch(reportExportTask, filePath);
                break;
        }
    }

    /**
     * 执行报表导出
     *
     * @param reportExportTask 离线任务
     * @param filePath         导出路径
     */
    private void executeExportReport(ReportExportTaskDTO reportExportTask, String filePath) {

        ReportListVO listVO = new ReportListVO();
        listVO.setPage(1L);
        listVO.setPageNum(200000L);
        listVO.setMasterId(reportExportTask.getMasterId());
        listVO.setReportType(reportExportTask.getTaskType());
        listVO.setStartDate(reportExportTask.getStartDate());
        listVO.setEndDate(reportExportTask.getEndDate());
        listVO.setModule(reportExportTask.getTaskModule());
        listVO.setIdentify(reportExportTask.getTaskIdentify());
        listVO.setCustoms(JSONObject.parseArray(reportExportTask.getCustoms(), String.class));
        if (StringUtils.isNotBlank(reportExportTask.getSearchData())) {
            Map<String, Object> searchData = JSONObject.parseObject(reportExportTask.getSearchData());
            listVO.setCampaignIds(this.objectToLongList(searchData.get("campaignIds")));
            listVO.setPlanIds(this.objectToLongList(searchData.get("planIds")));
            listVO.setSearch(searchData.get("search") == null ? "" : searchData.get("search").toString());
            listVO.setSortField(searchData.get("sortField") == null ? "" : searchData.get("sortField").toString());
            listVO.setSortType(searchData.get("sortType") == null ? "" : searchData.get("sortType").toString());
            listVO.setTimeZone(searchData.get("timeZone") == null ? TimeZoneEnum.UTC_8.getId() : Integer.parseInt(searchData.get("timeZone").toString()));
            listVO.setStartHour(searchData.get("startHour") == null ? 0 : Integer.parseInt(searchData.get("startHour").toString()));
            listVO.setEndHour(searchData.get("endHour") == null ? 0 : Integer.parseInt(searchData.get("endHour").toString()));
            listVO.setCompareField(searchData.get("sortType") == null ? "" : searchData.get("compareField").toString());
        }

        this.reportService.download(listVO, filePath, null);
    }

    /**
     * 执行自定义报表导出
     *
     * @param reportExportTask 离线任务
     * @param filePath         导出路径
     */
    private void executeExportCustomReport(ReportExportTaskDTO reportExportTask, String filePath) {

        AnalysisReportListVO listVO = new AnalysisReportListVO();
        listVO.setPage(1L);
        listVO.setPageNum(200000L);
        listVO.setMasterId(reportExportTask.getMasterId());
        listVO.setMasterIds(List.of(reportExportTask.getMasterId()));
        listVO.setReportType(reportExportTask.getTaskType());
        listVO.setStartDate(reportExportTask.getStartDate());
        listVO.setEndDate(reportExportTask.getEndDate());
        listVO.setModule(reportExportTask.getTaskModule());
        listVO.setIdentify(reportExportTask.getTaskIdentify());
        listVO.setIsDownload(true);
        listVO.setFilePath(filePath);
        List<String> customs = JSONObject.parseArray(reportExportTask.getCustoms(), String.class);
        List<String> dimensions = AnalysisReportFieldEnum.getKeys().stream().filter(customs::contains).collect(Collectors.toList());
        listVO.setDimensions(dimensions);
        customs.removeAll(dimensions);
        listVO.setCustoms(customs);
        // 如果存在筛选条件
        if (StringUtils.isNotBlank(reportExportTask.getSearchData())) {
            Map<String, Object> searchData = JSONObject.parseObject(reportExportTask.getSearchData());
            listVO.setCampaignIds(this.objectToLongList(searchData.get("campaignIds")));
            listVO.setPlanIds(this.objectToLongList(searchData.get("planIds")));
            listVO.setAdxIds(this.objectToLongList(searchData.get("adxIds")));
            listVO.setEpIds(this.objectToLongList(searchData.get("epIds")));
            listVO.setSlotTypes(this.objectToLongList(searchData.get("slotTypes")));
            listVO.setSearch(searchData.get("search") == null ? "" : searchData.get("search").toString());
            listVO.setSortField(searchData.get("sortField") == null ? "" : searchData.get("sortField").toString());
            listVO.setSortType(searchData.get("sortType") == null ? "" : searchData.get("sortType").toString());
            listVO.setFieldRules(searchData.get("fieldRules") == null ? List.of()
                    : JSONObject.parseArray(JSONObject.toJSONString(searchData.get("fieldRules")), CustomReportFieldRuleDTO.class));
            listVO.setTimeZone(searchData.get("timeZone") == null ? TimeZoneEnum.UTC_8.getId() : (Integer) searchData.get("timeZone"));
            listVO.setTemplateIds(this.objectToLongList(searchData.get("templateIds")));
            listVO.setRtaStrategyIds(this.objectToLongList(searchData.get("rtaStrategyIds")));
            listVO.setOptimizeTargetId(searchData.get("optimizeTargetId") == null ? 0L : Long.parseLong(searchData.get("optimizeTargetId").toString()));
            listVO.setBidType(searchData.get("bidType") == null ? 0 : (Integer) searchData.get("bidType"));
        }

        this.analysisReportService.exportReportToPath(listVO);
    }


    /**
     * 执行流量查询报表导出
     *
     * @param reportExportTask 离线任务
     * @param filePath         导出路径
     */
    private void executeExportFlowSearch(ReportExportTaskDTO reportExportTask, String filePath) {
        FlowReportListVO listVO = new FlowReportListVO();
        listVO.setPage(1L);
        listVO.setPageNum(200000L);
        listVO.setStartDate(reportExportTask.getStartDate());
        listVO.setEndDate(reportExportTask.getEndDate());
        listVO.setDimensions(JSONObject.parseArray(reportExportTask.getCustoms(), String.class));
        listVO.setSortField("req");
        listVO.setSortType("desc");
        listVO.setFilePath(filePath);
        // 如果存在筛选条件
        if (StringUtils.isNotBlank(reportExportTask.getSearchData())) {
            Map<String, Object> searchData = JSONObject.parseObject(reportExportTask.getSearchData());
            listVO.setAdxId(searchData.get("adxId") == null ? 0 : (Integer) searchData.get("adxId"));
            listVO.setEpId(searchData.get("epId") == null ? 0 : (Integer) searchData.get("epId"));
            listVO.setSlotType(searchData.get("slotType") == null ? 0 : (Integer) searchData.get("slotType"));
            listVO.setAreaId(searchData.get("areaId") == null ? 0L : Long.parseLong(searchData.get("areaId").toString()));
            listVO.setImageSize(searchData.get("imageSize") == null ? "" : searchData.get("imageSize").toString());
            listVO.setVideoSize(searchData.get("videoSize") == null ? "" : searchData.get("videoSize").toString());
            listVO.setSearch(searchData.get("search") == null ? "" : searchData.get("search").toString());
            listVO.setSortField(searchData.get("sortField") == null ? "" : searchData.get("sortField").toString());
            listVO.setSortType(searchData.get("sortType") == null ? "" : searchData.get("sortType").toString());
        }
        this.flowReportService.exportFlowReportToPath(listVO);
    }

    /**
     * Object对象转为List<Long>
     *
     * @param object 对象
     * @return 返回数据
     */
    private List<Long> objectToLongList(Object object) {
        return object == null ? List.of() : ObjectUtils.toList(object, Integer.class).stream().map(Long::valueOf).collect(Collectors.toList());
    }

}
