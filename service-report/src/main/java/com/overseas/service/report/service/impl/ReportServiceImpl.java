package com.overseas.service.report.service.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.MapUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.configuration.SheinConfiguration;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.chart.LegendDTO;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.chart.RemarkDTO;
import com.overseas.common.dto.chart.SeriesDTO;
import com.overseas.common.dto.market.creativeUnit.CreativeUnitAllDataListDTO;
import com.overseas.common.dto.market.master.MasterTimeZoneDTO;
import com.overseas.common.dto.market.plan.PlanUpdateRecordListDTO;
import com.overseas.common.dto.report.*;
import com.overseas.common.dto.report.adx.AdxDailyCostListDTO;
import com.overseas.common.dto.report.revenue.dsp.RevenueDspReportDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexChildColumnDTO;
import com.overseas.common.dto.sys.customIndex.CustomIndexFieldDTO;
import com.overseas.common.dto.sys.project.ProjectByMasterDTO;
import com.overseas.common.entity.User;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.enums.market.PutEnum;
import com.overseas.common.enums.report.ReportFieldEnum;
import com.overseas.common.enums.report.ReportTypeEnum;
import com.overseas.common.enums.user.UserTypeEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.campaign.CampaignSelectGetVO;
import com.overseas.common.vo.market.cps.recycle.CpsRecycleListVO;
import com.overseas.common.vo.market.master.MasterTimeZoneGetVO;
import com.overseas.common.vo.market.monitor.monitorEvent.MonitorEventIdsGetVO;
import com.overseas.common.vo.market.plan.PlanSelectGetVO;
import com.overseas.common.vo.market.plan.PlanUpdateRecordListVO;
import com.overseas.common.vo.market.plan.direct.EpSelectGetVO;
import com.overseas.common.vo.market.plan.direct.RtaSelectGetVO;
import com.overseas.common.vo.report.*;
import com.overseas.common.vo.report.plan.PlanHourReportListVO;
import com.overseas.common.vo.report.revenue.RevenueRtaReportListVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.common.vo.sys.project.ProjectByMasterVO;
import com.overseas.service.report.entity.*;
import com.overseas.service.report.enums.AnalysisReportFieldEnum;
import com.overseas.service.report.enums.DateTypeEnum;
import com.overseas.service.report.feign.FgMarketService;
import com.overseas.service.report.feign.FgSystemService;
import com.overseas.service.report.mapper.*;
import com.overseas.service.report.service.ReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReportServiceImpl extends ServiceImpl<CreativeUnitDayMapper, CreativeUnitDay> implements ReportService {

    private final FgMarketService fgMarketService;

    private final FgSystemService fgSystemService;

    private final PlanHourMapper planHourMapper;

    private final PlanGroupHourMapper planGroupHourMapper;

    private final CreativeUnitHourMapper creativeUnitHourMapper;

    private final PlanEpHourMapper planEpHourMapper;

    private final PlanDayMapper planDayMapper;

    private final AssetHourMapper assetHourMapper;

    private final AdxCostDayMapper adxCostDayMapper;

    private final PlanRtaHourMapper planRtaHourMapper;

    private final PlanAreaHourMapper planAreaHourMapper;

    private final PackageHourMapper packageHourMapper;

    private final SheinCreativeUnitHourMapper sheinCreativeUnitHourMapper;

    private final SheinConfiguration sheinConfiguration;

    private final PlanSspHourMapper planSspHourMapper;

    private final DealHourMapper dealHourMapper;

    @Override
    public MultiIndexChartDTO getTrendChart(ReportListVO listVO, User user) {
        this.sheinDeal(listVO, user);

        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        // 校验是否使用searchIds进行后续查询；为true则使用，为false则不使用
        Boolean isValidSearchIds = this.validSearchIds(listVO);
        // 筛选
        List<Long> searchIds = isValidSearchIds
                ? this.getSearchIds(this.getCreativeDataList(listVO), ReportTypeEnum.getById(listVO.getReportType()))
                : List.of();
        if (searchIds.isEmpty() && isValidSearchIds) {
            return multiIndexChartDTO;
        }
        //如果查询平台类型，则根据搜索searchIds查询数据
        if (ObjectUtils.isNotNullOrZero(listVO.getReportMode())) {
            CampaignSelectGetVO selectGetVO = new CampaignSelectGetVO();
            selectGetVO.setCampaignMode(List.of(listVO.getReportMode()));
            if (ObjectUtils.isNotNullOrZero(listVO.getMasterId())) {
                selectGetVO.setMasterId(listVO.getMasterId());
            }
            selectGetVO.setIsPut(listVO.getIsPut());
            List<Long> campIds = fgMarketService.selectCampaign(selectGetVO).getData()
                    .stream().map(SelectDTO::getId).collect(Collectors.toList());
            if (campIds.isEmpty()) {
                return multiIndexChartDTO;
            }
            //取活动合集
            if (CollectionUtils.isNotEmpty(listVO.getCampaignIds())) {
                listVO.getCampaignIds().retainAll(campIds);
            } else {
                listVO.setCampaignIds(campIds);
            }
        }

        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getById(listVO.getReportType());
        String groupField;
        switch (DateTypeEnum.get(listVO.getDateType())) {
            case DAY:
                groupField = "`day`";
                break;
            case HOUR:
                groupField = "`hour`";
                break;
            default:
                groupField = ReportTypeEnum.TIME_HOUR.equals(reportTypeEnum) ? "dim_report_hour" :
                        ((listVO.getStartDate().equals(listVO.getEndDate())
                                || ReportTypeEnum.TIME_COMPARE.equals(reportTypeEnum))
                                ? "`hour`" : "`day`");
                break;
        }

        // 获取趋势图查询Wrapper
        QueryWrapper<?> queryWrapper = this.getChartWrapper(listVO, isValidSearchIds, searchIds, groupField);
        // 获取趋势图需要填充的List数据
        List<ReportListDTO> listData = this.getDataList(listVO, reportTypeEnum, queryWrapper)
                .stream().map(u -> {
                    ReportListDTO reportListDTO = new ReportListDTO();
                    BeanUtils.copyProperties(u, reportListDTO);
                    return reportListDTO;
                }).collect(Collectors.toList());

        if (listData.isEmpty()) {
            return multiIndexChartDTO;
        }
        // 填充数据至DTO中
        return this.fillChartData(listData, Stream.of(listVO.getIndicator(),
                        listVO.getComparisonIndicator()).filter(StringUtils::isNotBlank).collect(Collectors.toList()),
                groupField, listVO.getModule(), listVO.getIdentify(),
                null, null, null, null, listVO);
    }

    @Override
    public PageUtils<CreativeHourReportListDTO> getReportPageList(ReportListVO listVO, User user) {
        this.sheinDeal(listVO, user);

        // 获取筛选后的查询数据ID集合（时间、小时、时间对比报表不需要查询）
        List<CreativeUnitAllDataListDTO> dataListDTOList =
                List.of(ReportTypeEnum.TIME, ReportTypeEnum.TIME_HOUR, ReportTypeEnum.TIME_COMPARE, ReportTypeEnum.SSP)
                        .contains(ReportTypeEnum.getById(listVO.getReportType()))
                        ? List.of() : this.getCreativeDataList(listVO);
        List<Long> searchIds = this.getSearchIds(dataListDTOList, ReportTypeEnum.getById(listVO.getReportType()));

        // 校验是否使用searchIds进行后续查询；为true则使用，为false则不使用
        Boolean isValidSearchIds = this.validSearchIds(listVO);
        // 如果使用searchIds且searchIds为空，则返回空
        if (isValidSearchIds && searchIds.isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }

        //如果查询平台类型，则根据搜索searchIds查询数据
        if (ObjectUtils.isNotNullOrZero(listVO.getReportMode())) {
            CampaignSelectGetVO selectGetVO = new CampaignSelectGetVO();
            selectGetVO.setCampaignMode(List.of(listVO.getReportMode()));
            if (ObjectUtils.isNotNullOrZero(listVO.getMasterId())) {
                selectGetVO.setMasterId(listVO.getMasterId());
            }
            selectGetVO.setIsPut(listVO.getIsPut());
            List<Long> campIds = fgMarketService.selectCampaign(selectGetVO).getData()
                    .stream().map(SelectDTO::getId).collect(Collectors.toList());
            if (campIds.isEmpty()) {
                return new PageUtils<>(List.of(), 0L);
            }
            //取活动合集
            if (CollectionUtils.isNotEmpty(listVO.getCampaignIds())) {
                listVO.getCampaignIds().retainAll(campIds);
            } else {
                listVO.setCampaignIds(campIds);
            }
        }

        // 获取列表填充的分页数据
        IPage<CreativeHourReportListDTO> pageData = this.getReportList(listVO, isValidSearchIds, searchIds, user);
        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }
        // 填充报表中的投放相关字段
        this.fillData(pageData.getRecords(), dataListDTOList, ReportTypeEnum.getById(listVO.getReportType()), listVO);
        // 添加汇总数据
        return new PageUtils<>(pageData, this.getSummaryData(listVO, isValidSearchIds, searchIds, user));
    }

    @Override
    public ProjectByMasterDTO getReportPageListIdentify(ReportListVO listVO, User user) {
        this.sheinDeal(listVO, user);

        List<Long> masterIds = null;
        if (CollectionUtils.isNotEmpty(listVO.getMasterIds())) {
            masterIds = listVO.getMasterIds().stream()
                    .map(u -> Long.parseLong(u.toString())).collect(Collectors.toList());
        } else if (ObjectUtils.isNotNullOrZero(listVO.getMasterId())) {
            masterIds = List.of(listVO.getMasterId());
        }
        if (CollectionUtils.isEmpty(masterIds)) {
            Boolean isValidSearchIds = this.validSearchIds(listVO);
            // 如果使用需要搜索使用
            if (isValidSearchIds) {
                List<CreativeUnitAllDataListDTO> dataListDTOList =
                        List.of(ReportTypeEnum.TIME, ReportTypeEnum.TIME_HOUR, ReportTypeEnum.TIME_COMPARE)
                                .contains(ReportTypeEnum.getById(listVO.getReportType()))
                                ? List.of() : this.getCreativeDataList(listVO);
                masterIds = dataListDTOList.stream().map(CreativeUnitAllDataListDTO::getMasterId)
                        .distinct().collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(masterIds)) {
            return ProjectByMasterDTO.builder().identify("").projectId(List.of()).build();
        }
        return fgSystemService.getProjectByMaster(ProjectByMasterVO.builder().masterIds(masterIds).build()).getData();
    }

    @Override
    public IPage<CreativeHourReportListDTO> getReportList(ReportListVO listVO, Boolean isValidSearchIds,
                                                          List<Long> searchIds, User user) {
        this.sheinDeal(listVO, user);

        // 如果使用searchIds且searchIds为空，则返回空
        if (isValidSearchIds && searchIds.isEmpty()) {
            return new Page<>(listVO.getPage(), listVO.getPageNum());
        }
        // 如果是时间对比报表，则分开调用直接返回
        if (listVO.getReportType().equals(ReportTypeEnum.TIME_COMPARE.getId())) {
            return this.getTimeCompareList(listVO, searchIds);
        }
        // 获取查询Wrapper
        QueryWrapper<?> queryWrapper = this.getWrapper(listVO, isValidSearchIds, searchIds);
        if (null == queryWrapper) {
            return new Page<>(listVO.getPage(), listVO.getPageNum());
        }
        // 获取列表填充的分页数据
        return this.getDataPageList(listVO, ReportTypeEnum.getById(listVO.getReportType()), queryWrapper);
    }

    private IPage<CreativeHourReportListDTO> getTimeCompareList(ReportListVO listVO, List<Long> searchIds) {

        int hourRatio = Objects.requireNonNull(TimeZoneEnum.get(listVO.getTimeZone())).getFormatHour();
        AnalysisReportFieldEnum analysisReportFieldEnum = AnalysisReportFieldEnum.get(listVO.getCompareField());
        // 获取今日数据
        ReportListVO todayListVO = new ReportListVO();
        BeanUtils.copyProperties(listVO, todayListVO);
        todayListVO.setEndDate(listVO.getStartDate());
        // 获取对比日数据
        ReportListVO compareListVO = new ReportListVO();
        BeanUtils.copyProperties(listVO, compareListVO);
        compareListVO.setStartDate(listVO.getEndDate());
        // 时间对比报表不需要查询其下searchIds
        QueryWrapper<?> todayWrapper = this.getWrapper(todayListVO, false, searchIds),
                compareWrapper = this.getWrapper(compareListVO, false, searchIds);
        Map<String, CreativeHourReportListDTO> todayMap = this.getCompareMap(
                this.getDataPageList(listVO, ReportTypeEnum.getById(listVO.getReportType()), todayWrapper).getRecords(),
                analysisReportFieldEnum, hourRatio);

        Map<String, CreativeHourReportListDTO> compareMap = this.getCompareMap(
                this.getDataPageList(listVO, ReportTypeEnum.getById(listVO.getReportType()), compareWrapper).getRecords(),
                analysisReportFieldEnum, hourRatio);
        List<CreativeHourReportListDTO> timeCompareReportListDTOList = this.fillCompareListByMap(listVO,
                analysisReportFieldEnum, compareMap, todayMap);
        IPage<CreativeHourReportListDTO> page = new Page<>();
        page.setRecords(timeCompareReportListDTOList);
        page.setCurrent(listVO.getPage());
        page.setTotal(timeCompareReportListDTOList.size());
        return page;
    }

    private Map<String, CreativeHourReportListDTO> getCompareMap(List<CreativeHourReportListDTO> listData,
                                                                 AnalysisReportFieldEnum analysisReportFieldEnum,
                                                                 Integer hourRatio) {

        return listData.stream().peek(u -> {
            if (analysisReportFieldEnum.equals(AnalysisReportFieldEnum.REPORT_HOUR)) {
                u.setHour(DateUtils.formatHourRange(u.getHour() + hourRatio));
            }
        }).collect(Collectors.toMap(u -> Objects.requireNonNull(
                ObjectUtils.getObjectValue(u, analysisReportFieldEnum.getFieldKey())).toString(), Function.identity()));
    }

    private List<CreativeHourReportListDTO> fillCompareListByMap(ReportListVO listVO,
                                                                 AnalysisReportFieldEnum analysisReportFieldEnum,
                                                                 Map<String, CreativeHourReportListDTO> compareMap,
                                                                 Map<String, CreativeHourReportListDTO> todayMap) {

        List<CreativeHourReportListDTO> timeCompareReportListDTOList = new ArrayList<>();
        if (analysisReportFieldEnum.equals(AnalysisReportFieldEnum.REPORT_HOUR)) {
            if (listVO.getSortType().equals("asc")) {
                // 时间升序
                for (int index = 0; index <= 23; index++) {
                    CreativeHourReportListDTO reportDTO = this.fillCompareData(todayMap.get(String.valueOf(index)),
                            compareMap.get(String.valueOf(index)));
                    if (reportDTO == null) {
                        continue;
                    }
                    timeCompareReportListDTOList.add(reportDTO);
                }
            } else {
                // 时间降序
                for (int index = 23; index >= 0; index--) {
                    CreativeHourReportListDTO reportDTO = this.fillCompareData(todayMap.get(String.valueOf(index)),
                            compareMap.get(String.valueOf(index)));
                    if (reportDTO == null) {
                        continue;
                    }
                    timeCompareReportListDTOList.add(reportDTO);
                }
            }
        } else {
            List<String> keys = new ArrayList<>() {{
                addAll(compareMap.keySet());
                addAll(todayMap.keySet());
            }}.stream().distinct().map(String::valueOf).collect(Collectors.toList());
            keys = listVO.getSortType().equals("asc")
                    ? keys.stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList())
                    : keys.stream().sorted(Comparator.comparing(String::valueOf, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            for (String key : keys) {
                CreativeHourReportListDTO reportDTO = this.fillCompareData(todayMap.get(key), compareMap.get(key));
                if (reportDTO == null) {
                    continue;
                }
                timeCompareReportListDTOList.add(reportDTO);
            }
        }
        return timeCompareReportListDTOList;
    }

    @Override
    public void download(ReportListVO listVO, User user, HttpServletResponse response) {
        this.executeDownload(listVO, user, response, "");
    }

    @Override
    public void download(ReportListVO listVO, String path, User user) {
        this.executeDownload(listVO, user, null, path);
    }

    private void executeDownload(ReportListVO listVO, User user, HttpServletResponse response, String path) {
        List<CreativeHourReportListDTO> listData = this.getReportPageList(listVO, user).getData();
        if (listData.isEmpty()) {
            throw new CustomException("无可下载数据");
        }
        Map<String, String> fieldMap = fgSystemService.getUserCustomIndexReportDownload(CustomIndexGetVO.builder()
                        .module(listVO.getModule()).identify(listVO.getIdentify()).build()).getData()
                .stream().flatMap(u -> u.getColumns().stream())
                .collect(Collectors.toMap(CustomIndexChildColumnDTO::getKey, CustomIndexChildColumnDTO::getTitle, (o, n) -> n));
        fieldMap.putAll(ObjectUtils.getFields(CreativeHourReportListDTO.class)
                .stream().filter(field -> field.getAnnotation(ExcelProperty.class) != null)
                .collect(Collectors.toMap(Field::getName, field -> field.getAnnotation(ExcelProperty.class).value()[0])));
        //如果获取不到自定义列
        List<SelectDTO2> headers;
        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getById(listVO.getReportType());
        headers = new ArrayList<>() {{
            if (ReportTypeEnum.TIME_COMPARE.equals(reportTypeEnum) && StringUtils.isNotBlank(listVO.getCompareField())) {
                add(new SelectDTO2(listVO.getCompareField(), fieldMap.get(listVO.getCompareField())));
            } else {
                addAll(Stream.of(
                                (
                                        ReportTypeEnum.TIME_HOUR.equals(reportTypeEnum) && listVO.getIsMerge() == 1
                                                ? "reportHour" : reportTypeEnum.getReportFields()
                                ).split(","))
                        .map(field -> {
                            if (null == fieldMap.get(field)) {
                                return null;
                            }
                            return new SelectDTO2(field, fieldMap.get(field));
                        }).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            // 特殊处理报表支持日期组合
            if (CollectionUtils.isNotEmpty(listVO.getDimensions()) && !ReportTypeEnum.CUSTOM.equals(reportTypeEnum)) {
                if (listVO.getDimensions().size() == 1 && listVO.getDimensions().contains("reportDate")) {
                    add(new SelectDTO2("reportDate", "日期"));
                }
            }

            if (CollectionUtils.isNotEmpty(listVO.getCustoms())) {
                listVO.getCustoms().forEach(custom -> {
                    if (null == fieldMap.get(custom)) {
                        return;
                    }
                    String firstUpperKey = HumpLineUtils.firstToUpper(custom),
                            fieldTitle = fieldMap.get(custom);
                    switch (reportTypeEnum) {
                        case TIME_COMPARE:
                            add(new SelectDTO2("today" + firstUpperKey, fieldTitle + "_指定天"));
                            add(new SelectDTO2(custom, fieldTitle + "_对比天"));
                            add(new SelectDTO2("diff" + firstUpperKey, fieldTitle + "_差值"));
                            add(new SelectDTO2("diffRate" + firstUpperKey, fieldTitle + "_差值比例（%）"));
                            break;
                        case TIME:
                        case TIME_HOUR:
                            // 如果展示差值
                            if (listVO.getIsShowDiff()) {
                                add(new SelectDTO2(custom, fieldTitle + "_原值"));
                                add(new SelectDTO2("diff" + firstUpperKey, fieldTitle + "_差值"));
                                add(new SelectDTO2("diffRate" + firstUpperKey, fieldTitle + "_差值比例（%）"));
                            } else {
                                add(new SelectDTO2(custom, fieldTitle));
                            }
                            break;
                        default:
                            add(new SelectDTO2(custom, fieldTitle));
                            break;
                    }
                });
            }
        }};

        String fileName = ReportTypeEnum.getById(listVO.getReportType()).getName() + "报表_";
        // 如果带小时段，去掉分秒
        listVO.setStartDate(listVO.getStartDate().split(":")[0]);
        listVO.setEndDate(listVO.getEndDate().split(":")[0]);
        fileName += listVO.getStartDate().equals(listVO.getEndDate())
                ? listVO.getStartDate() : listVO.getStartDate() + "_" + listVO.getEndDate();

        log.info("file name : {},  headers : {}", fileName,
                JSONObject.toJSONString(headers));
        try {
            if (StringUtils.isNotBlank(path)) {
                ExcelUtils.download(UploadUtils.getUploadPath(path), "报表数据", listData, headers, "_");
            } else {
                ExcelUtils.download(response, fileName, "报表数据", listData, headers, "_");
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
    }

    /**
     * 校验是否使用searchIds进行后续查询；为true则使用，为false则不使用
     *
     * @param listVO 筛选参数
     * @return 返回结果
     */
    private Boolean validSearchIds(ReportListVO listVO) {
        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getById(listVO.getReportType());
        switch (reportTypeEnum) {
            case MASTER:
            case CREATIVE:
                // 如果为账户、创意投放模块调用，或者报表查询了search查询，则返回true
                if (listVO.getIsMarket() || StringUtils.isNotBlank(listVO.getSearch()) || CollectionUtils.isNotEmpty(listVO.getTemplateIds())) {
                    return true;
                }
                break;
            case CAMPAIGN:
                // 如果为活动投放模块调用，或者活动报表查询了营销目的、推广目标、search查询，则返回true
                if (listVO.getIsMarket() || ObjectUtils.isNotNullOrZero(listVO.getMarketTarget()) ||
                        ObjectUtils.isNotNullOrZero(listVO.getPutOnTarget()) || StringUtils.isNotBlank(listVO.getSearch())) {
                    return true;
                }
                break;
            case PLAN:
            case PLAN_GROUP:
                // 如果为计划投放模块调用，或者计划报表查询了营销目的、推广目标、广告形式、操作系统、优化目标、模版、出价类型、RTA、search查询，则返回true
                if (listVO.getIsMarket() || ObjectUtils.isNotNullOrZero(listVO.getMarketTarget()) ||
                        ObjectUtils.isNotNullOrZero(listVO.getPutOnTarget()) || ObjectUtils.isNotNullOrZero(listVO.getSlotType()) ||
                        ObjectUtils.isNotNullOrZero(listVO.getBidType()) ||
                        ObjectUtils.isNotNullOrZero(listVO.getOptimizeTargetId()) || CollectionUtils.isNotEmpty(listVO.getRtaStrategyIds()) ||
                        ObjectUtils.isNotNullOrZero(listVO.getOsType()) || StringUtils.isNotBlank(listVO.getSearch())) {
                    return true;
                }
                break;
            case RTA:
            case REGION:
                if (StringUtils.isNotBlank(listVO.getSearch())) {
                    return true;
                }
                break;
            default:
        }
        return false;
    }

    @Override
    public List<Long> getHasCostIds(ReportHasCostGetVO getVO) {
        String field = "dim_" + HumpLineUtils.humpToLine2(getVO.getType()) + "_id";
        return this.planDayMapper.getIds(new QueryWrapper<PlanDay>().select(field)
                .eq("dim_master_id", getVO.getMasterId())
                .ne(field, 0)
                .groupBy(field));
    }

    @Override
    public List<ReportListDTO> getPlanHourReportList(PlanHourReportListVO listVO) {
        return this.planHourMapper.getPlanHourReportList(new QueryWrapper<PlanHour>()
                .select("dim_plan_id AS `plan_id`," + ReportFieldEnum.getBaseIndicatorRules())
                .eq("dim_day", DateUtils.string2Long(listVO.getDate()))
                .eq(listVO.getHour() != null, "dim_hour", listVO.getHour())
                .eq("dim_master_id", listVO.getMasterId())
                .eq("dim_plan_id", listVO.getPlanId()));
    }

    @Override
    public PageUtils<AdxDailyCostListDTO> listAdxDailyCost(AdxDailyCostListVO listVO) {

        IPage<AdxDailyCostListDTO> pageData = this.adxCostDayMapper.listAdxDailyCost(
                new Page<>(listVO.getPage(), listVO.getPageNum()),
                new QueryWrapper<AdxCostDay>().lambda()
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getAdxId()), AdxCostDay::getAdxId, listVO.getAdxId())
                        .between(AdxCostDay::getDay, DateUtils.string2Long(listVO.getStartDate()),
                                DateUtils.string2Long(listVO.getEndDate()))
                        .orderByDesc(AdxCostDay::getDay)
                        .orderByDesc(AdxCostDay::getAdxId));
        if (pageData.getRecords().isEmpty()) {
            return new PageUtils<>(List.of(), 0L);
        }

        AdxDailyCostListDTO totalData = this.adxCostDayMapper.getAdxDailyCostTotal(
                new QueryWrapper<AdxCostDay>().lambda()
                        .eq(ObjectUtils.isNotNullOrZero(listVO.getAdxId()), AdxCostDay::getAdxId, listVO.getAdxId())
                        .between(AdxCostDay::getDay, DateUtils.string2Long(listVO.getStartDate()),
                                DateUtils.string2Long(listVO.getEndDate())));

        Map<Long, String> adxMap = this.fgMarketService.getDockingAdxSelect().getData().stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
        pageData.getRecords().forEach(record -> {
            record.setReportDate(DateUtils.long2String(record.getDay()));
            record.setAdxName(adxMap.get(record.getAdxId()));
        });
        return new PageUtils<>(pageData, totalData);
    }

    @Override
    public List<RevenueDspReportDTO> listDspReportData(RevenueRtaReportListVO listVO) {

        if (ObjectUtils.isNotNullOrZero(listVO.getMasterId())) {
            // 获取当前账号时区
            QueryWrapper<PlanRtaHour> queryWrapper = this.getDspReportBaseWrapper(listVO.getRtaIds());
            MasterTimeZoneGetVO masterGetVO = new MasterTimeZoneGetVO();
            masterGetVO.setMasterId(listVO.getMasterId());
            masterGetVO.setTimeZone(100);
            Integer timeZone = this.fgMarketService.getMasterTimeZone(masterGetVO).getData().get(0).getTimeZone();
            // 根据时区重新转化日期
            String dspFields = listVO.getSqlSelect() + ",UNIX_TIMESTAMP(FROM_UNIXTIME(dim_report_hour + "
                    + 3600 * Objects.requireNonNull(TimeZoneEnum.get(timeZone)).getFormatHour()
                    + ",'%Y-%m-%d')) AS `day`";
            queryWrapper.eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "dim_master_id", listVO.getMasterId());
            // 根据多天查询日期
            queryWrapper.and(q -> {
                for (Long day : listVO.getDates()) {
                    q.or().between("dim_report_hour", DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.long2Date(day), timeZone, 0)),
                            DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.long2Date(day), timeZone, 23)));
                }
            });
            return this.planRtaHourMapper.listPlanReport(queryWrapper.select(dspFields));
        } else {
            // 获取当前账号时区
            MasterTimeZoneGetVO masterGetVO = new MasterTimeZoneGetVO();
            masterGetVO.setMasterIds(listVO.getMasterIds());
            masterGetVO.setTimeZone(100);
            Map<Integer, List<MasterTimeZoneDTO>> timeZoneMap = this.fgMarketService.getMasterTimeZone(masterGetVO).getData()
                    .stream().collect(Collectors.groupingBy(MasterTimeZoneDTO::getTimeZone));
            List<RevenueDspReportDTO> dspReports = new ArrayList<>();
            for (Map.Entry<Integer, List<MasterTimeZoneDTO>> entry : timeZoneMap.entrySet()) {
                QueryWrapper<PlanRtaHour> queryWrapper = this.getDspReportBaseWrapper(listVO.getRtaIds());
                // 根据时区重新转化日期
                Integer timeZone = entry.getKey();
                String dspFields = listVO.getSqlSelect() + ",UNIX_TIMESTAMP(FROM_UNIXTIME(dim_report_hour + " + 3600 * Objects.requireNonNull(TimeZoneEnum.get(timeZone)).getFormatHour() + ",'%Y-%m-%d')) AS `day`";
                // 根据多天查询日期
                queryWrapper.and(q -> {
                    for (Long day : listVO.getDates()) {
                        q.or(q1 -> q1.in("dim_master_id", entry.getValue().stream().map(MasterTimeZoneDTO::getMasterId).collect(Collectors.toList()))
                                .between("dim_report_hour", DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.long2Date(day), timeZone, 0)),
                                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.long2Date(day), timeZone, 23))));
                    }
                });
                dspReports.addAll(this.planRtaHourMapper.listPlanReport(queryWrapper.select(dspFields)));
            }
            return dspReports;
        }
    }

    @Override
    public List<DimensionTotalDTO> dimensionTotal(DimensionTotalVO dimensionTotalVO) {
        String fields = dimensionTotalVO.getDimensionFields();
        if (List.of(5, 12).contains(dimensionTotalVO.getMonitorDimension())) {
            dimensionTotalVO.setGroupBys(dimensionTotalVO.getGroupBys().stream().map(u -> "slot_type".equals(u) ? "dim_ad_type" : u).collect(Collectors.toList()));
            fields = dimensionTotalVO.getDimensionFields().replace("`slot_type`", "`dim_ad_type`");
        }
        log.info("监测统计数据当前开始小时 :{}，结束小时:{}", dimensionTotalVO.getStartHour(), dimensionTotalVO.getEndHour());
        QueryWrapper<?> queryWrapper = new QueryWrapper<>()
                .select(fields)
                .groupBy(CollectionUtils.isNotEmpty(dimensionTotalVO.getGroupBys()), dimensionTotalVO.getGroupBys())
                .in("dim_master_id", dimensionTotalVO.getMasterId())
                .eq(ObjectUtils.isNotNullOrZero(dimensionTotalVO.getAgentId()),
                        "dim_agent_id", dimensionTotalVO.getAgentId())
                .in(CollectionUtils.isNotEmpty(dimensionTotalVO.getCampaignIds()),
                        "dim_campaign_id", dimensionTotalVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(dimensionTotalVO.getPlanIds()),
                        "dim_plan_id", dimensionTotalVO.getPlanIds())
                .in(CollectionUtils.isNotEmpty(dimensionTotalVO.getEpIds()),
                        "dim_ep_id", dimensionTotalVO.getEpIds());
        //如果开始时间为-101 则认为是累计时间，不限定时间
        if (dimensionTotalVO.getStartHour() != -101) {
            Date date = DateUtils.string2Date(dimensionTotalVO.getDay());
            Long startHour = DateUtils.date2Long(
                    TimeZoneEnum.getUTC_8Date(date, dimensionTotalVO.getTimezone(), dimensionTotalVO.getStartHour()));
            Long endHour = DateUtils.date2Long(
                    TimeZoneEnum.getUTC_8Date(date, dimensionTotalVO.getTimezone(), dimensionTotalVO.getEndHour())
            );
            log.info("监测统计开始时间：{} ,结束时间:{}", startHour, endHour);
            queryWrapper.between("dim_report_hour", startHour, endHour)
                    //加入前后1天，防止出现时区问题，导致无法查询数据，（为什么加这个字段，因为期望用到索引）
                    .in("dim_day", DateUtils.date2Long(DateUtils.afterDay(date, -1)),
                            DateUtils.date2Long(date), DateUtils.date2Long(DateUtils.afterDay(date, 1)));
        } else {
            Date date = DateUtils.string2Date(dimensionTotalVO.getDay());
            Long endHour = DateUtils.date2Long(
                    TimeZoneEnum.getUTC_8Date(date, dimensionTotalVO.getTimezone(), dimensionTotalVO.getEndHour())
            );
            log.info("监测统计结束时间:{}", endHour);
            queryWrapper.le("dim_report_hour", endHour);
        }
        if (MapUtils.isNotEmpty(dimensionTotalVO.getOrderBys())) {
            dimensionTotalVO.getOrderBys().forEach((k, v) ->
                    queryWrapper.orderBy(true, "asc".equalsIgnoreCase(v), k)
            );
        }
        QueryWrapper<?> whereQuery = new QueryWrapper<>();
        if (MapUtils.isNotEmpty(dimensionTotalVO.getWheres())) {
            dimensionTotalVO.getWheres().forEach((k, v) -> {
                switch (k) {
                    case "gt":
                        v.forEach(whereQuery::gtSql);
                        break;
                    case "eq":
                    case "in":
                        v.forEach(whereQuery::inSql);
                        break;
                    case "lt":
                        v.forEach(whereQuery::ltSql);
                        break;
                    default:
                }
            });
        }
        if (ObjectUtils.isNotNullOrZero(dimensionTotalVO.getTop())) {
            queryWrapper.last("LIMIT " + dimensionTotalVO.getTop());
        }
        switch (dimensionTotalVO.getMonitorDimension()) {
            case 6:
                return planAreaHourMapper.dimensionTotal(queryWrapper, whereQuery);
            case 7:
            case 8:
                return planEpHourMapper.dimensionTotal(queryWrapper, whereQuery);
            case 5:
            case 12:
                String dimensionInfo = queryWrapper.getSqlSelect();
                dimensionInfo = dimensionInfo.substring(dimensionInfo.indexOf("CONCAT") + 6, dimensionInfo.indexOf(" AS `dimension_info`"));
                dimensionInfo = dimensionInfo.replace("(", "").replace(")", "");
                String[] dimensionInfoArr = dimensionInfo.split("' -- '");
                queryWrapper.select(queryWrapper.getSqlSelect().replaceAll(dimensionInfo, Arrays.stream(dimensionInfoArr)
                        .map(u -> String.format("CAST (%s AS String)", u.replace(",", ""))).collect(Collectors.joining(", ' -- ' ,"))));
                //限制消耗大于0.1
                queryWrapper.gt("idx_media_cost", 0.00001);
                return packageHourMapper.dimensionTotal(queryWrapper, whereQuery);
            case 13:
                return creativeUnitHourMapper.dimensionTotal(queryWrapper, whereQuery);
            default:
                return planHourMapper.dimensionTotal(queryWrapper, whereQuery);
        }
    }

    @Override
    public BigDecimal dimensionReset(DimensionResetVO resetVO) {
        return this.planHourMapper.selectRestVal(new QueryWrapper<>()
                .between("dim_report_hour", resetVO.getStart(), resetVO.getEnd())
                .eq("dim_plan_id", resetVO.getPlanId())
                .select(resetVO.getRule())
        );
    }

    private QueryWrapper<PlanRtaHour> getDspReportBaseWrapper(List<Long> rtaIds) {

        return new QueryWrapper<PlanRtaHour>()
                .in(CollectionUtils.isNotEmpty(rtaIds), "dim_rta_id", rtaIds)
                .groupBy("`day`,dim_master_id,dim_rta_id");
    }

    @Override
    public void updateSheinT1Settlement() {
        Long startDate = DateUtils.string2Long(DateUtils.format(DateUtils.format(new Date(), -10)));
        Long endDate = DateUtils.string2Long(DateUtils.format(DateUtils.format(new Date(), -1)));
        UpdateWrapper<CreativeUnitHour> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().between(CreativeUnitHour::getDay, startDate, endDate);
        this.sheinCreativeUnitHourMapper.updateT1Settlement(updateWrapper);

        updateWrapper.gt("idx_action12", 0);
        this.sheinCreativeUnitHourMapper.updateT1Purchase(updateWrapper);
    }

    @Override
    public void updateSheinRealTimeSettlement() {
        Long date = DateUtils.string2Long(DateUtils.format(DateUtils.format(new Date(), -1)));
        UpdateWrapper<CreativeUnitHour> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().gt(CreativeUnitHour::getDay, date);
        this.sheinCreativeUnitHourMapper.updateRealTimeSettlement(updateWrapper);
    }

    @Override
    public Map<String, BigDecimal> recycleCost(CpsRecycleListVO listVO) {
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.get(listVO.getTimeZone());
        if (null == timeZoneEnum) {
            throw new CustomException("时区不合法");
        }

        List<PlanHour> planHours = this.planHourMapper.selectList(new QueryWrapper<PlanHour>()
                .select("UNIX_TIMESTAMP(FROM_UNIXTIME(dim_report_hour + " + 3600 * timeZoneEnum.getFormatHour() + ",'%Y-%m-%d')) AS `day`," +
                        "FROM_UNIXTIME(dim_report_hour + " + 3600 * timeZoneEnum.getFormatHour() + ", '%H') AS `hour`," +
                        ("all".equals(listVO.getGroupBy()) ?
                                " SUM(`idx_report_cost`) AS report_cost" :
                                String.format(" `dim_%s` AS id, SUM(`idx_report_cost`) AS report_cost",
                                        HumpLineUtils.humpToLine2(listVO.getGroupBy()))
                        )
                ).between("dim_report_hour",
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getStartDate()), listVO.getTimeZone(), 0)),
                        DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(listVO.getEndDate()), listVO.getTimeZone(), 23))
                )
                .in("dim_master_id", listVO.getMasterIds())
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "dim_campaign_id", listVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "dim_plan_id", listVO.getPlanIds())
                .groupBy(HumpLineUtils.humpToLine2(listVO.getRecycleType()))
                .groupBy(!"all".equals(listVO.getGroupBy()), String.format("dim_%s", HumpLineUtils.humpToLine2(listVO.getGroupBy())))
                .orderByDesc("report_cost")
                .last(!"all".equals(listVO.getGroupBy()) && CollectionUtils.isEmpty(listVO.getPlanIds()) && CollectionUtils.isEmpty(listVO.getCampaignIds()), "limit 10")
        );
        return planHours.stream().filter(u -> {
            if (CollectionUtils.isEmpty(listVO.getHours())) {
                return true;
            }
            return listVO.getHours().contains(u.getHour().intValue());
        }).collect(Collectors.toMap(k -> {
            if ("all".equals(listVO.getGroupBy())) {
                if ("hour".equals(listVO.getRecycleType())) {
                    return k.getHour().toString();
                } else {
                    return k.getDay().toString();
                }
            } else {
                if ("hour".equals(listVO.getRecycleType())) {
                    return String.format("%s_%s", k.getHour(), k.getId());
                } else {
                    return String.format("%s_%s", k.getDay(), k.getId());
                }
            }
        }, v -> BigDecimal.valueOf(v.getReportCost()).divide(BigDecimal.valueOf(1000000), 3, RoundingMode.HALF_UP)));
    }

    @Override
    public List<SelectDTO> selectSortByCost(SelectSortByCostVO sortByCostVO) {
        //如果为空，返回空数据
        if (CollectionUtils.isEmpty(sortByCostVO.getSelects()) || (StringUtils.isBlank(sortByCostVO.getStartDate()) || StringUtils.isBlank(sortByCostVO.getEndDate()))) {
            return sortByCostVO.getSelects();
        }
        String sortField;
        switch (sortByCostVO.getType()) {
            case 1:
                sortField = "dim_campaign_id";
                break;
            case 2:
                sortField = "dim_plan_id";
                break;
            case 3:
                sortField = "dim_master_id";
                break;
            default:
                return sortByCostVO.getSelects();
        }
        Map<Long, SelectDTO> maps = sortByCostVO.getSelects().stream()
                .collect(Collectors.toMap(SelectDTO::getId, Function.identity()));
        List<SelectDTO> result = this.planHourMapper.selectList(new QueryWrapper<PlanHour>()
                        .select(String.format("%s AS id, sum(idx_report_cost) AS report_cost", sortField))
                        .in(sortField, maps.keySet())
                        .between("dim_report_hour",
                                DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(sortByCostVO.getStartDate()), sortByCostVO.getTimeZone(), 0)),
                                DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(sortByCostVO.getEndDate()), sortByCostVO.getTimeZone(), 23)))
                        .groupBy(sortField)
                        .orderByDesc("report_cost"))
                .stream().map(u -> {
                    SelectDTO select = maps.get(u.getId());
                    maps.remove(u.getId());
                    return select;
                }).collect(Collectors.toList());
        result.addAll(maps.values().stream().sorted(Comparator.comparingLong(SelectDTO::getId).reversed()).collect(Collectors.toList()));
        return result;
    }

    @Override
    public UserCostDTO getUserCost(UserCostGetVO getVO) {
        List<UserCostDTO> userCostDTOS = this.planDayMapper.getUserCost(new QueryWrapper<PlanDay>()
                .between("dim_day", DateUtils.string2Long(getVO.getStartDate()), DateUtils.string2Long(getVO.getEndDate()))
                .eq(ObjectUtils.isNotNullOrZero(getVO.getMasterId()), "dim_master_id", getVO.getMasterId())
                .groupBy("dim_master_id"));
        return userCostDTOS.isEmpty() ? null : userCostDTOS.get(0);
    }

    @Override
    public Map<Long, UserCostDTO> listUserCost(UserCostListVO listVO) {

        MasterTimeZoneGetVO masterGetVO = new MasterTimeZoneGetVO();
        masterGetVO.setMasterIds(ObjectUtils.isNotNullOrZero(listVO.getMasterId()) ? List.of(listVO.getMasterId().intValue())
                : this.fgSystemService.getMasterIds().getData());
        masterGetVO.setTimeZone(100);
        Map<Integer, List<MasterTimeZoneDTO>> timeZoneMap = this.fgMarketService.getMasterTimeZone(masterGetVO).getData()
                .stream().collect(Collectors.groupingBy(MasterTimeZoneDTO::getTimeZone));

        QueryWrapper<PlanHour> queryWrapper = new QueryWrapper<PlanHour>()
                .eq(ObjectUtils.isNotNullOrZero(listVO.getAgentId()), "dim_agent_id", listVO.getAgentId())
                .groupBy(UserTypeEnum.AGENT.getId().equals(listVO.getUserType()), "dim_agent_id")
                .groupBy(UserTypeEnum.MASTER.getId().equals(listVO.getUserType()), "dim_master_id");
        queryWrapper.and(q -> timeZoneMap.forEach((timeZone, value) -> {
            Long startDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(listVO.getStartDate(), timeZone, 0)),
                    endDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(listVO.getEndDate(), timeZone, 23));
            q.or(q1 -> q1.in("dim_master_id", value.stream().map(MasterTimeZoneDTO::getMasterId).collect(Collectors.toList()))
                    .between("dim_report_hour", startDate, endDate));
        }));
        List<UserCostDTO> userCostDTOS = this.planHourMapper.getUserCost(queryWrapper);
        if (UserTypeEnum.AGENT.getId().equals(listVO.getUserType())) {
            return userCostDTOS.stream().collect(Collectors.toMap(UserCostDTO::getAgentId, Function.identity()));
        } else {
            return userCostDTOS.stream().collect(Collectors.toMap(UserCostDTO::getMasterId, Function.identity()));
        }
    }

    @Override
    public OverviewReportDTO getOverviewReport(OverviewReportGetVO getVO, User user) {

        // 1.定义周期开始、结束时间；获取今日时间
        long lastStartDate, lastEndDate, startDate, endDate, endDateVO = DateUtils.string2Long(getVO.getEndDate());
        Long todayDate = DateUtils.date2Long(DateUtils.getTodayDate());

        // 根据时区获取当前周期开始时间
        startDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(getVO.getStartDate()), getVO.getTimeZone(), 0));
        // 如果结束时间是当天，则获取当前小时
        endDate = (todayDate.equals(endDateVO) || todayDate < endDateVO)
                ? DateUtils.date2Long(new Date())
                : DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(DateUtils.string2Date(getVO.getEndDate()), getVO.getTimeZone(), 23));
        // 如果开始日期与结束日期是同一天，则上一周期为该日的昨天
        if (getVO.getStartDate().equals(getVO.getEndDate())) {
            lastStartDate = startDate - 86400L;
            lastEndDate = endDate - 86400L;
        } else {
            // 如果不是同一天，则是该时间段的上一时间段
            if (todayDate < endDateVO) {
                lastStartDate = TimeZoneEnum.getTimeZoneLongDateByUTC_8(2 * startDate - endDateVO - 86400L, getVO.getTimeZone(), 0);
                lastEndDate = endDate - (startDate - lastStartDate);
            } else {
                lastStartDate = startDate - (endDate - startDate + 3600L);
                lastEndDate = startDate - 3600L;
            }
        }

        // 2.获取查询Wrapper
        QueryWrapper<PlanHour> queryWrapper = this.getPlanHourWrapper(getVO.getMasterId(), startDate,
                endDate, getVO.getModule(), getVO.getIdentify(), getVO.getTimeZone());
        QueryWrapper<PlanHour> lastQueryWrapper = this.getPlanHourWrapper(getVO.getMasterId(), lastStartDate, lastEndDate,
                getVO.getModule(), getVO.getIdentify(), getVO.getTimeZone());

        // 3.查询上一周期与当前周期数据
        List<ReportListDTO> lastReport;
        List<ReportListDTO> currentReport;
        if (sheinConfiguration.isRole(user.getRoleId())) {
            lastReport = this.sheinCreativeUnitHourMapper.getPlanHourReportList(lastQueryWrapper);
            currentReport = this.sheinCreativeUnitHourMapper.getPlanHourReportList(queryWrapper);
        } else {
            lastReport = this.planHourMapper.getPlanHourReportList(lastQueryWrapper);
            currentReport = this.planHourMapper.getPlanHourReportList(queryWrapper);
        }

        // 4.组装数据
        OverviewReportDTO resReport = new OverviewReportDTO();
        // 插入当前周期数据
        if (CollectionUtils.isNotEmpty(currentReport)) {
            resReport.setCurrentData(currentReport.get(0));
        }
        // 插入上一周期数据
        if (CollectionUtils.isNotEmpty(lastReport)) {
            resReport.setLastData(lastReport.get(0));
        }
        return resReport;
    }

    @Override
    public MultiIndexChartDTO getOverviewTrendChart(OverviewChartGetVO getVO, User user) {

        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();

        if (getVO.getIndicators().isEmpty()) {
            throw new CustomException("指标不能为空");
        }

        // 如果多天指标，则以小时和天聚合
        // 如果开始时间与结束时间相同且均不为空，则以小时聚合
        // 否则则以天聚合
        String groupField = (CollectionUtils.isNotEmpty(getVO.getDates())) ? "`day`,`hour`" :
                (StringUtils.isNotBlank(getVO.getStartDate()) && StringUtils.isNotBlank(getVO.getEndDate()) &&
                        getVO.getStartDate().equals(getVO.getEndDate()) ? "`hour`" : "`day`");

        ReportListVO reportListVO = new ReportListVO();
        reportListVO.setMasterId(getVO.getMasterId());
        reportListVO.setStartDate(getVO.getStartDate());
        reportListVO.setEndDate(getVO.getEndDate());
        reportListVO.setReportType(ReportTypeEnum.PLAN.getId());
        reportListVO.setModule(getVO.getModule());
        reportListVO.setIdentify(getVO.getIdentify());
        reportListVO.setTimeZone(getVO.getTimeZone());
        QueryWrapper<?> queryWrapper = this.getChartWrapper(reportListVO, true, List.of(), groupField);

        // 如果选择了指定日期，则查询指定日期
        if (CollectionUtils.isNotEmpty(getVO.getDates())) {
            queryWrapper.and(q -> getVO.getDates().forEach(date -> {
                Date dayDate = DateUtils.string2Date(date);
                Long startDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(dayDate, getVO.getTimeZone(), 0)),
                        endDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(dayDate, getVO.getTimeZone(), 23));
                q.or().between("dim_report_hour", startDate, endDate);
            }));
        }
        List<ReportListDTO> reportList;
        if (sheinConfiguration.isRole(user.getRoleId())) {
            reportList = this.sheinCreativeUnitHourMapper.getPlanHourReportList(queryWrapper);
        } else {
            reportList = this.planHourMapper.getPlanHourReportList(queryWrapper);
        }
        if (reportList.isEmpty()) {
            return multiIndexChartDTO;
        }

        return this.fillChartData(reportList, getVO.getIndicators(), groupField,
                getVO.getModule(), getVO.getIdentify(), null, null, null, null, new ReportListVO());
    }

    @Override
    public MultiIndexChartDTO getTrendChart(TrendChartGetVO getVO) {

        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();

        if (getVO.getIndicators().isEmpty()) {
            throw new CustomException("指标不能为空");
        }

        ReportListVO reportListVO = new ReportListVO();
        reportListVO.setMasterId(getVO.getMasterId());
        reportListVO.setStartDate(getVO.getStartDate());
        reportListVO.setEndDate(getVO.getEndDate());
        reportListVO.setReportType(ReportTypeEnum.PLAN.getId());
        reportListVO.setModule(getVO.getModule());
        reportListVO.setIdentify(getVO.getIdentify());
        reportListVO.setPlanId(getVO.getPlanId());
        QueryWrapper<?> queryWrapper = this.getChartWrapper(reportListVO, false, List.of(), "dim_report_hour");

        List<ReportListDTO> reportList = this.planHourMapper.getPlanHourReportList(queryWrapper);
        if (reportList.isEmpty()) {
            return multiIndexChartDTO;
        }

        return this.fillChartData(reportList, getVO.getIndicators(), "dim_report_hour", getVO.getModule(),
                getVO.getIdentify(), getVO.getIsIncludeUpdateRecord(), getVO.getPlanId(), getVO.getStartDate(), getVO.getEndDate(), new ReportListVO());
    }

    @Override
    public CreativeHourReportListDTO getSummaryData(ReportListVO listVO, Boolean isValidSearchIds, List<Long> searchIds, User user) {
        this.sheinDeal(listVO, user);

        // 如果使用searchIds且searchIds为空，则返回空
        if (isValidSearchIds && searchIds.isEmpty()) {
            return null;
        }
        QueryWrapper<?> summaryWrapper = this.getSummaryWrapper(listVO, isValidSearchIds, searchIds);
        CreativeHourReportListDTO summaryReportData = new CreativeHourReportListDTO();

        if (null != summaryWrapper) {
            ReportTypeEnum reportTypeEnum = ReportTypeEnum.getById(listVO.getReportType());
            switch (reportTypeEnum) {
                case CREATIVE:
                    if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                        summaryReportData = this.sheinCreativeUnitHourMapper.getTotal(summaryWrapper);
                    } else {
                        String customFieldsSql = this.totalCustomFieldSql(listVO);
                        if (StringUtils.isBlank(customFieldsSql)) {
                            summaryReportData = this.creativeUnitHourMapper.getTotal(summaryWrapper);
                        } else {
                            String finalSelectSql = summaryWrapper.getSqlSelect()
                                    .replace("dim_report_hour", "0")
                                    .replace("dim_creative_unit_id AS creative_unit_id,", "");
                            summaryReportData = this.creativeUnitHourMapper.getTotal2(summaryWrapper.select(this.innerSelectSql(finalSelectSql))
                                    .groupBy("dim_creative_unit_id"), finalSelectSql, customFieldsSql);
                        }
                    }
                    break;
                case AGENT:
                case MASTER:
                case CAMPAIGN:
                case PLAN:
                case TIME:
                case TIME_HOUR:
                    if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                        summaryReportData = this.sheinCreativeUnitHourMapper.getTotal(summaryWrapper);
                    } else {
                        String customFieldsSql = this.totalCustomFieldSql(listVO);
                        if (StringUtils.isBlank(customFieldsSql) || !List.of(ReportTypeEnum.PLAN, ReportTypeEnum.CAMPAIGN).contains(reportTypeEnum)) {
                            summaryReportData = this.planHourMapper.getTotal(summaryWrapper);
                        } else {
                            String finalSelectSql = summaryWrapper.getSqlSelect()
                                    .replace("dim_report_hour", "0")
                                    .replace("dim_plan_id AS plan_id,", "")
                                    .replace("dim_campaign_id AS campaign_id,", "");
                            Map<ReportTypeEnum, String> groupMap = new HashMap<>() {{
                                put(ReportTypeEnum.PLAN, "dim_plan_id");
                                put(ReportTypeEnum.CAMPAIGN, "dim_campaign_id");
                            }};

                            summaryReportData = this.planHourMapper.getTotal2(summaryWrapper.select(this.innerSelectSql(finalSelectSql))
                                    .groupBy(groupMap.getOrDefault(reportTypeEnum, "")), finalSelectSql, customFieldsSql);
                        }
                    }
                    break;
                case PLAN_GROUP:
                    summaryReportData = this.planGroupHourMapper.getTotal(summaryWrapper);
                    break;
                case TIME_COMPARE:
                    ReportListVO todayListVO = new ReportListVO();
                    BeanUtils.copyProperties(listVO, todayListVO);
                    todayListVO.setEndDate(listVO.getStartDate());
                    // 时间对比报表不需要查询其下searchIds
                    CreativeHourReportListDTO todayData = AnalysisReportFieldEnum.PACKAGE.getKey().equals(listVO.getCompareField())
                            ? this.packageHourMapper.getTotal(this.getSummaryWrapper(todayListVO, false, searchIds))
                            : this.planEpHourMapper.getTotal(this.getSummaryWrapper(todayListVO, false, searchIds));
                    ReportListVO compareListVO = new ReportListVO();
                    BeanUtils.copyProperties(listVO, compareListVO);
                    compareListVO.setStartDate(listVO.getEndDate());
                    // 时间对比报表不需要查询其下searchIds
                    CreativeHourReportListDTO compareData = AnalysisReportFieldEnum.PACKAGE.getKey().equals(listVO.getCompareField())
                            ? this.packageHourMapper.getTotal(this.getSummaryWrapper(compareListVO, false, searchIds))
                            : this.planEpHourMapper.getTotal(this.getSummaryWrapper(compareListVO, false, searchIds));
                    if (todayData != null || compareData != null) {
                        summaryReportData = this.fillTodayData(todayData, compareData);
                    }
                    break;
                case RTA:
                    if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                        summaryReportData = this.sheinCreativeUnitHourMapper.getTotal(summaryWrapper);
                    } else {
                        summaryReportData = this.planRtaHourMapper.getRtaReportTotal(summaryWrapper);
                    }
                    break;
                case REGION:
                    if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                        summaryReportData = this.sheinCreativeUnitHourMapper.getTotal(summaryWrapper);
                    } else {
                        summaryReportData = this.planAreaHourMapper.getAreaReportTotal(summaryWrapper);
                    }
                    break;
                case SSP:
                    summaryReportData = this.planSspHourMapper.getSspReportTotal(summaryWrapper);
                    break;
                case DEAL:
                    summaryReportData = this.dealHourMapper.getDealReportTotal(summaryWrapper);
                    break;
                default:
            }
        }
        if (summaryReportData == null) {
            summaryReportData = new CreativeHourReportListDTO();
        }
        summaryReportData.setDay(0L);
        summaryReportData.setHour(-1);
        summaryReportData.setAgentId(0L);
        summaryReportData.setMasterId(0L);
        summaryReportData.setCampaignId(0L);
        summaryReportData.setPlanId(0L);
        summaryReportData.setRtaId(0L);
        summaryReportData.setCountryId(0L);
        summaryReportData.setCreativeId(0L);
        summaryReportData.setCreativeUnitId(0L);
        summaryReportData.setReportDate(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setReportHour(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setPlanName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setPlanGroupName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setMasterName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setRtaName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setAdxName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setEpName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setCountryName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setCampaignName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setAgentName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setSlotTypeName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setCreativeTypeName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setMarketTargetName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setOsTypeName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setAuditMasterName(ConstantUtils.PLACEHOLDER_2);
        summaryReportData.setPutOnTargetName(ConstantUtils.PLACEHOLDER_2);
        return summaryReportData;
    }

    @Override
    public List<Long> listHasCostRecord(ReportHasCostListVO hasCostListVO) {
        QueryWrapper<CreativeUnitHour> queryWrapper = new QueryWrapper<>();
        String field = "DISTINCT dim_" + HumpLineUtils.humpToLine2(hasCostListVO.getListType()) + "_id";
        queryWrapper.select(field).lambda().gt(CreativeUnitHour::getView, 0);
        switch (hasCostListVO.getListType()) {
            case "campaign":
                queryWrapper.lambda().in(CreativeUnitHour::getCampaignId, hasCostListVO.getIds());
                break;
            case "plan":
                queryWrapper.lambda().in(CreativeUnitHour::getPlanId, hasCostListVO.getIds());
                break;
            case "creativeUnit":
                queryWrapper.lambda().in(CreativeUnitHour::getCreativeUnitId, hasCostListVO.getIds());
                break;
            default:
        }
        return this.baseMapper.getIds(queryWrapper);
    }

    /**
     * 获取sqlSelect
     *
     * @param origin 需要处理的select字符串
     * @return 处理后的select字符串
     */
    private String translateField(String origin) {
        return Arrays.stream(origin.split(",")).map(key ->
                key + " AS " + new ArrayList<>(List.of(key.split("dim_"))).get(1)).collect(Collectors.joining(","));
    }

    /**
     * 获取筛选的投放数据
     *
     * @param listVO 筛选数据
     * @return 投放数据
     */
    private List<CreativeUnitAllDataListDTO> getCreativeDataList(ReportListVO listVO) {
        FeignR<List<CreativeUnitAllDataListDTO>> result = fgMarketService.getCreativeAllDataList(listVO);

        if (!result.getCode().equals(0)) {
            log.info("get creative all data {}", JSONObject.toJSONString(result));
            throw new CustomException(result.getMsg());
        }
        return result.getData();
    }

    /**
     * 获取筛选出来的投放数据指定维度ID
     *
     * @param dataList   数据
     * @param reportType 报表类型
     * @return ID集合
     */
    private List<Long> getSearchIds(List<CreativeUnitAllDataListDTO> dataList, ReportTypeEnum reportType) {

        if (dataList.isEmpty()) {
            return List.of();
        }
        List<Long> searchIds = new ArrayList<>();
        switch (reportType) {
            case MASTER:
                searchIds = dataList.stream().map(CreativeUnitAllDataListDTO::getMasterId).collect(Collectors.toList());
                break;
            case CAMPAIGN:
                searchIds = dataList.stream().map(CreativeUnitAllDataListDTO::getCampaignId).collect(Collectors.toList());
                break;
            case PLAN:
            case TIME:
            case TIME_HOUR:
            case TIME_COMPARE:
                searchIds = dataList.stream().map(CreativeUnitAllDataListDTO::getPlanId).collect(Collectors.toList());
                break;
            case PLAN_GROUP:
                searchIds = dataList.stream().map(CreativeUnitAllDataListDTO::getPlanGroupId).collect(Collectors.toList());
                break;
            case CREATIVE:
                searchIds = dataList.stream().map(CreativeUnitAllDataListDTO::getCreativeUnitId).collect(Collectors.toList());
                break;
            case RTA:
                searchIds = dataList.stream().map(CreativeUnitAllDataListDTO::getRtaId).collect(Collectors.toList());
                break;
            case REGION:
                searchIds = dataList.stream().map(CreativeUnitAllDataListDTO::getCountryId).collect(Collectors.toList());
                break;
            default:
        }
        return searchIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 根据报表类型获取筛选字段类型
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    private String getSearchField(ReportListVO listVO) {
        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getById(listVO.getReportType());
        switch (reportTypeEnum) {
            // 如果天、小时查询计划
            case TIME:
            case TIME_HOUR:
                return ReportTypeEnum.PLAN.getGroupField();
            // 如果是天对比，且对比维度是小时，查询计划，否则查询对比维度
            case TIME_COMPARE:
                return listVO.getCompareField().equals(AnalysisReportFieldEnum.REPORT_HOUR.getKey())
                        ? ReportTypeEnum.PLAN.getGroupField() : AnalysisReportFieldEnum.getFieldByKey(listVO.getCompareField());
            // 其他维度查询具体维度
            default:
                return reportTypeEnum.getGroupField();
        }
    }

    /**
     * 获取趋势图Wrapper
     *
     * @param listVO           查询参数
     * @param isValidSearchIds 是否使用searchIds
     * @param searchIds        查询ID集合
     * @param groupField       聚合维度
     * @return 返回数据
     */
    private QueryWrapper<?> getChartWrapper(ReportListVO listVO, Boolean isValidSearchIds, List<Long> searchIds,
                                            String groupField) {

        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getById(listVO.getReportType());
        // 如果是小时报表，则查询小时，其他转化为天
        Date start = DateUtils.string2Date(listVO.getStartDate()),
                end = DateUtils.string2Date(listVO.getEndDate());
        // 分小时报表和其他报表做处理
        // 小时报表前端传 yyyy-MM-dd HH，含带小时时间，因此不需要额外做小时的加减处理
        // 其他报表前端传 yyyy-MM-dd，不含带小时时间，因此需要对结束时间加23小时来查询结束时间的数据
        Long startDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(start, listVO.getTimeZone(), 0)),
                endDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(end, listVO.getTimeZone(), 23)
                );
        Long startDay = DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(startDate))),
                endDay = DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(endDate)));

        String commonFieldSql = findReportFields(listVO.getModule(), listVO.getIdentify(), "");
        AnalysisReportFieldEnum analysisReportFieldEnum = StringUtils.isNotBlank(listVO.getCompareField())
                ? AnalysisReportFieldEnum.get(listVO.getCompareField()) : null;
        List<Long> ids = analysisReportFieldEnum != null && reportTypeEnum.equals(ReportTypeEnum.TIME_COMPARE)
                && !AnalysisReportFieldEnum.REPORT_HOUR.equals(analysisReportFieldEnum)
                ? this.getCompareTopIds(listVO, analysisReportFieldEnum, startDate, commonFieldSql) : List.of();

        // 获取时间对比报表的对比维度
        String compareField = reportTypeEnum.equals(ReportTypeEnum.TIME_COMPARE)
                ? (AnalysisReportFieldEnum.REPORT_HOUR.getKey().equals(listVO.getCompareField()) ? "`day`" : AnalysisReportFieldEnum.getFieldByKey(listVO.getCompareField()))
                : "";
        int hourRatio = Objects.requireNonNull(TimeZoneEnum.get(listVO.getTimeZone())).getFormatHour();
        QueryWrapper<?> queryWrapper = new QueryWrapper<>()
                .select("UNIX_TIMESTAMP(FROM_UNIXTIME(dim_report_hour + " + 3600 * hourRatio + ",'%Y-%m-%d')) AS `day`," +
                        "dim_report_hour + " + 3600 * hourRatio + " AS `day_hour`," +
                        "FROM_UNIXTIME(dim_report_hour + " + 3600 * hourRatio + ", '%H') AS `hour`," +
                        this.getCompareFieldSql(compareField) + commonFieldSql)
                .between(List.of(ReportTypeEnum.TIME_HOUR, ReportTypeEnum.TIME_COMPARE).contains(reportTypeEnum),
                        "FROM_UNIXTIME(dim_report_hour + " + 3600 * hourRatio + ", '%H')", listVO.getStartHour(), listVO.getEndHour())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "dim_master_id", listVO.getMasterId())
                .in(CollectionUtils.isNotEmpty(searchIds), this.getSearchField(listVO), searchIds)
                .groupBy(groupField)
                // 如果是时间对比报表，则额外补充聚合维度
                .groupBy(StringUtils.isNotBlank(compareField), compareField)
                // 如果是时间对比，且数据趋势为小时
                .groupBy(StringUtils.isNotBlank(compareField) && DateTypeEnum.HOUR.getId().equals(listVO.getDateType()), "dim_report_hour")
                .orderByAsc(groupField);

        // 如果是选择了对比维度，则查询TOP
        if (CollectionUtils.isNotEmpty(ids) && analysisReportFieldEnum != null) {
            queryWrapper.in(analysisReportFieldEnum.getField(), ids);
        }

        // 如果不使用searchIds进行查询，则使用报表相关筛选字段进行过滤
        this.setWrapperWithoutSearchIds(listVO, queryWrapper, isValidSearchIds);
        if (ObjectUtils.isNotNullOrZero(start) && ObjectUtils.isNotNullOrZero(end)) {
            // 如果是时间对比报表，则日期圈选为指定两天日期
            if (reportTypeEnum.equals(ReportTypeEnum.TIME_COMPARE)) {
                queryWrapper.and(q -> q.between("dim_report_hour", startDate, startDate + 3600 * 23)
                        .or().between("dim_report_hour", endDate - 3600 * 23, endDate));
            } else {
                // 如果是其他报表，则日期圈选为指定两天日期范围
                queryWrapper.between("dim_day", startDay, endDay).between("dim_report_hour", startDate, endDate);
            }
        }
        return queryWrapper;
    }

    /**
     * 获取时间对比报表TOP数据
     *
     * @param listVO                  查询参数
     * @param analysisReportFieldEnum 对比维度
     * @param date                    指定天时间
     * @param commonFieldSql          查询的指标
     * @return 返回数据
     */
    private List<Long> getCompareTopIds(ReportListVO listVO, AnalysisReportFieldEnum analysisReportFieldEnum, Long date, String commonFieldSql) {

        String field = analysisReportFieldEnum.equals(AnalysisReportFieldEnum.REPORT_HOUR) ? "dim_report_hour" : analysisReportFieldEnum.getField();
        QueryWrapper<?> queryWrapper = new QueryWrapper<>()
                .select(field + " AS `id`," + commonFieldSql)
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "dim_master_id", listVO.getMasterId())
                .between("dim_report_hour", date, date + 3600 * 23)
                .groupBy(field)
                .orderByDesc(HumpLineUtils.humpToLine2(listVO.getIndicator()))
                .last("LIMIT 5");
        return this.planEpHourMapper.getReportList(queryWrapper).stream().map(CreativeHourReportListDTO::getId).collect(Collectors.toList());
    }

    private String getCompareFieldSql(String compareField) {
        if (StringUtils.isBlank(compareField) || compareField.equals("`day`")) {
            return "";
        }
        return ReportTypeEnum.translateField(List.of(compareField)).get(0) + ",";
    }

    /**
     * 获取报表列表汇总数据的Wrapper
     *
     * @param listVO    筛选数据
     * @param searchIds 查询ID集合
     * @return 返回数据
     */
    private QueryWrapper<?> getSummaryWrapper(ReportListVO listVO, Boolean isValidSearchIds, List<Long> searchIds) {

        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getById(listVO.getReportType());
        // 如果是小时报表，则查询小时，其他转化为天
        Date start = DateUtils.string2Date(listVO.getStartDate()),
                end = DateUtils.string2Date(listVO.getEndDate());
        // 分小时报表和其他报表做处理
        // 小时报表，时间对比报表前端传 yyyy-MM-dd HH，含带小时时间，因此不需要额外做小时的加减处理
        // 其他报表前端传 yyyy-MM-dd，不含带小时时间，因此需要对结束时间加23小时来查询结束时间的数据
        Long startDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(start, listVO.getTimeZone(), 0)),
                endDate = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(end, listVO.getTimeZone(), 23));
        Long startDay = DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(startDate))),
                endDay = DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(endDate)));

        QueryWrapper<?> queryWrapper = new QueryWrapper<>();

        String searchField = this.getSearchField(listVO);
        int hourRatio = Objects.requireNonNull(TimeZoneEnum.get(listVO.getTimeZone())).getFormatHour();
        // 如果是时间报表，或者活动报表包含了日期维度，则根据时区去重新定义日期
        String selectFields = "UNIX_TIMESTAMP(FROM_UNIXTIME(dim_report_hour + " + 3600 * hourRatio + ",'%Y-%m-%d')) AS `day`,";
        switch (reportTypeEnum) {
            case TIME_HOUR:
                if (listVO.getIsMerge() == 1) {
                    selectFields += "FROM_UNIXTIME(dim_report_hour +" + 3600 * hourRatio + ",'%H') as `hour`, ";
                } else {
                    selectFields += "dim_hour AS `hour`, dim_report_hour + " + 3600 * hourRatio + " AS `day_hour`,";
                }
                break;
            case TIME_COMPARE:
                selectFields += "dim_hour AS `hour`, dim_report_hour + " + 3600 * hourRatio + " AS `day_hour`,";
                break;
            default:
        }
        selectFields += this.translateField(searchField) + "," +
                findReportFields(listVO.getModule(), listVO.getIdentify(), listVO.getSortField());
        // 时间筛选
        queryWrapper.between("dim_report_hour", startDate, endDate).between("dim_day", startDay, endDay);
        // 如果是小时报表、时间对比报表，则筛选小时
        if (List.of(ReportTypeEnum.TIME_HOUR, ReportTypeEnum.TIME_COMPARE).contains(reportTypeEnum)) {
            queryWrapper.between("FROM_UNIXTIME(dim_report_hour + " + 3600 * hourRatio + ", '%H')",
                    listVO.getStartHour(), listVO.getEndHour());
        }
        // 如果不使用searchIds进行查询，则使用报表相关筛选字段进行过滤
        this.setWrapperWithoutSearchIds(listVO, queryWrapper, isValidSearchIds);
        return queryWrapper.select(selectFields)
                .eq(ObjectUtils.isNotNullOrZero(listVO.getMasterId()), "dim_master_id", listVO.getMasterId())
                .in(isValidSearchIds && CollectionUtils.isNotEmpty(searchIds), searchField, searchIds);
    }

    /**
     * 获取报表列表数据的Wrapper
     *
     * @param listVO    筛选数据
     * @param searchIds 查询ID集合
     * @return 返回数据
     */
    private QueryWrapper<?> getWrapper(ReportListVO listVO, Boolean isValidSearchIds, List<Long> searchIds) {

        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getById(listVO.getReportType());
        // 设置排序
        if (listVO.getSortField().equals("reportDate")) {
            listVO.setSortField("day");
        }
        if (listVO.getSortField().equals("reportHour")) {
            if (listVO.getIsMerge() == 1) {
                listVO.setSortField("hour");
            } else {
                listVO.setSortField("dim_report_hour");
            }
        }
        // 如果前端没有排序则设置默认排序
        if (List.of("", "normal").contains(listVO.getSortType()) || StringUtils.isBlank(listVO.getSortField())) {
            listVO.setSortField(List.of(ReportTypeEnum.TIME, ReportTypeEnum.TIME_HOUR, ReportTypeEnum.TIME_COMPARE).contains(reportTypeEnum) ?
                    reportTypeEnum.getGroupField() : "masterCost");
            listVO.setSortType("desc");
        }
        QueryWrapper<?> queryWrapper = this.getSummaryWrapper(listVO, isValidSearchIds, searchIds);
        if (null == queryWrapper) {
            return null;
        }
        return queryWrapper.groupBy(this.groupBy(reportTypeEnum, listVO))
                .orderBy(ReportTypeEnum.CAMPAIGN.equals(reportTypeEnum) && !listVO.getDimensions().isEmpty(),
                        "asc".equalsIgnoreCase(listVO.getSortType()), "`day`")
                .orderBy(true, "asc".equalsIgnoreCase(listVO.getSortType()),
                        reportTypeEnum.equals(ReportTypeEnum.TIME_COMPARE)
                                ? AnalysisReportFieldEnum.getFieldByKey(listVO.getCompareField())
                                : HumpLineUtils.humpToLine2(listVO.getSortField())
                );
    }

    /**
     * 获取 group 数据
     *
     * @param reportTypeEnum 类型
     * @param listVO         条件
     * @return 返回group
     */
    private String groupBy(ReportTypeEnum reportTypeEnum, ReportListVO listVO) {
        if (ReportTypeEnum.TIME_HOUR.equals(reportTypeEnum) && listVO.getIsMerge() == 1) {
            return "hour";
        }
        // 特殊处理报表支持日期组合
        if (CollectionUtils.isNotEmpty(listVO.getDimensions()) && !ReportTypeEnum.CUSTOM.equals(reportTypeEnum)) {
            if (listVO.getDimensions().size() == 1 && listVO.getDimensions().contains("reportDate")) {
                return "`day`," + reportTypeEnum.getGroupField();
            }
        }
        return reportTypeEnum.equals(ReportTypeEnum.TIME_COMPARE)
                ? AnalysisReportFieldEnum.getFieldByKey(listVO.getCompareField())
                : reportTypeEnum.getGroupField();
    }

    /**
     * 如果不使用searchIds，设置根据报表相关字段筛选过滤
     *
     * @param listVO       传入参数
     * @param queryWrapper wrapper查询
     */
    private void setWrapperWithoutSearchIds(ReportListVO listVO, QueryWrapper<?> queryWrapper, Boolean isValidSearchIds) {

        if (isValidSearchIds) {
            // 如果是RTA报表类型，添加活动、计划过滤
            if (listVO.getReportType().equals(ReportTypeEnum.RTA.getId())) {
                queryWrapper.in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "dim_campaign_id", listVO.getCampaignIds())
                        .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "dim_plan_id", listVO.getPlanIds());
            }
            return;
        }
        List<Integer> masterIds = new ArrayList<>(ObjectUtils.isNullOrZero(listVO.getMasterId()) ? this.fgSystemService.getMasterIds().getData() : List.of());
        // 如果查询参数中包含多个广告主ID，取交集
        if (CollectionUtils.isNotEmpty(listVO.getMasterIds())) {
            masterIds.retainAll(listVO.getMasterIds());
        }
        queryWrapper.in(ObjectUtils.isNotNullOrZero(listVO.getAgentId()), "dim_agent_id", listVO.getAgentId())
                .in(CollectionUtils.isNotEmpty(masterIds), "dim_master_id", masterIds)
                .in(CollectionUtils.isNotEmpty(listVO.getCampaignIds()), "dim_campaign_id", listVO.getCampaignIds())
                .in(CollectionUtils.isNotEmpty(listVO.getPlanIds()), "dim_plan_id", listVO.getPlanIds())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getPlanId()), "dim_plan_id", listVO.getPlanId())
                .in(CollectionUtils.isNotEmpty(listVO.getCreativeUnitIds()), "dim_creative_unit_id", listVO.getCreativeUnitIds())
                .eq(ObjectUtils.isNotNullOrZero(listVO.getCreativeUnitId()), "dim_creative_unit_id", listVO.getCreativeUnitId())
                // 如果是时间对比、RTA报表，才查询ADX
                .in(CollectionUtils.isNotEmpty(listVO.getAdxIds())
                                && List.of(ReportTypeEnum.TIME_COMPARE.getId(), ReportTypeEnum.RTA.getId(), ReportTypeEnum.SSP.getId()).contains(listVO.getReportType()),
                        "dim_adx_id", listVO.getAdxIds())
                // 如果是时间对比，才查询EP
                .in(CollectionUtils.isNotEmpty(listVO.getEpIds())
                                && List.of(ReportTypeEnum.TIME_COMPARE.getId(), ReportTypeEnum.SSP.getId()).contains(listVO.getReportType()),
                        "dim_ep_id", listVO.getEpIds())
                // 如果是时间对比、RTA报表，才查询RTA
                .in(CollectionUtils.isNotEmpty(listVO.getRtaStrategyIds())
                                && List.of(ReportTypeEnum.TIME_COMPARE.getId(), ReportTypeEnum.RTA.getId()).contains(listVO.getReportType()),
                        "dim_rta_id", listVO.getRtaStrategyIds())
                .eq(StringUtils.isNotBlank(listVO.getSearch()) && Objects.equals(ReportTypeEnum.SSP.getId(), listVO.getReportType()),
                        "dim_ssp", listVO.getSearch())
                .and(CollectionUtils.isNotEmpty(listVO.getSupplierChain()) && Objects.equals(ReportTypeEnum.SSP.getId(), listVO.getReportType()),
                        q -> {
                            if (listVO.getSupplierChain().contains(100)) {
                                q.in("dim_supplier_chain", listVO.getSupplierChain())
                                        .or().gt("dim_supplier_chain", "5");
                            } else {
                                q.in("dim_supplier_chain", listVO.getSupplierChain());
                            }
                        }).eq(ObjectUtils.isNotNullOrZero(listVO.getDealId()) && StringUtils.isNotBlank(listVO.getDealId()), "dim_deal_id", listVO.getDealId());

        // 如果是自定义报表，则根据所选维度进行筛选
        if (ReportTypeEnum.CUSTOM.getId().equals(listVO.getReportType())) {

            List<Long> optimizeTargets = new ArrayList<>() {{
                if (ObjectUtils.isNotNullOrZero(listVO.getOptimizeTargetId())) {
                    MonitorEventIdsGetVO getVO = new MonitorEventIdsGetVO();
                    getVO.setMasterId(listVO.getMasterId());
                    getVO.setActionIds(List.of(listVO.getOptimizeTargetId()));
                    addAll(fgMarketService.listMonitorEventIdsByAction(getVO).getData());
                }
            }};
            queryWrapper.in(CollectionUtils.isNotEmpty(listVO.getAdxIds()), "dim_adx_id", listVO.getAdxIds())
                    .in(CollectionUtils.isNotEmpty(listVO.getEpIds()), "dim_ep_id", listVO.getEpIds())
                    .in(CollectionUtils.isNotEmpty(listVO.getRtaStrategyIds()), "dim_rta_id", listVO.getRtaStrategyIds())
                    .in(CollectionUtils.isNotEmpty(listVO.getSlotTypes()), "m_plan.slot_type", listVO.getSlotTypes())
                    .eq(ObjectUtils.isNotNullOrZero(listVO.getBidType()), "m_plan.bid_type", listVO.getBidType())
//                    .in(CollectionUtils.isNotEmpty(listVO.getTemplateIds()), "m_plan.template_id", listVO.getTemplateIds())
                    .in(CollectionUtils.isNotEmpty(optimizeTargets), "m_plan.optimize_target_id", optimizeTargets)
                    .like(StringUtils.isNotBlank(listVO.getSearch()) && listVO.getDimensions().contains(AnalysisReportFieldEnum.PACKAGE.getKey()),
                            "dim_pkg", listVO.getSearch());
        }
    }

    /**
     * 填充趋势图表数据
     *
     * @param listData              数据List
     * @param indicators            趋势数据集合
     * @param groupField            聚合字段（dim_day、dim_hour）
     * @param module                模块
     * @param identify              项目标识
     * @param isIncludeUpdateRecord 是否包含更新记录
     * @return 返回数据
     */
    private MultiIndexChartDTO fillChartData(List<ReportListDTO> listData, List<String> indicators, String groupField,
                                             String module, String identify, Integer isIncludeUpdateRecord, Long planId,
                                             String startDate, String endDate, ReportListVO listVO) {

        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        // 日期
        List<String> dates = new ArrayList<>();
        // 指标的数据
        LinkedHashMap<String, List<Object>> valueMap = new LinkedHashMap<>();
        // <天, <小时, 数据>>映射Map
        Map<String, Map<String, Object>> dayDateMap = new HashMap<>();
        // 设置小时
        listData.forEach(record -> record.setHour(Integer.parseInt(DateUtils.format(DateUtils.long2Date(record.getDayHour()), "HH"))));
        // 备注记录
        List<Object> remarks = new ArrayList<>();
        // 如果需要查询编辑记录
        Map<Long, List<PlanUpdateRecordListDTO>> hourRecordMap = new HashMap<>();
        if (ObjectUtils.isNotNullOrZero(isIncludeUpdateRecord)) {
            PlanUpdateRecordListVO planUpdateRecordListVO = new PlanUpdateRecordListVO();
            planUpdateRecordListVO.setPage(1L);
            planUpdateRecordListVO.setPageNum(9999999L);
            planUpdateRecordListVO.setPlanId(planId);
            planUpdateRecordListVO.setStartDate(startDate);
            planUpdateRecordListVO.setEndDate(endDate);
            FeignR<List<PlanUpdateRecordListDTO>> feignR = this.fgMarketService.listPlanUpdateRecord(planUpdateRecordListVO);
            if (feignR.getCode() == 0) {
                List<PlanUpdateRecordListDTO> list = feignR.getData();
                if (CollectionUtils.isNotEmpty(list)) {
                    hourRecordMap = list.stream().collect(Collectors.groupingBy(PlanUpdateRecordListDTO::getDay));
                }
            }
        }


        // 如果是时间对比报表，则将对比维度置为空；并设置获取的key
        String fieldKey = "";
        if (listVO != null && ReportTypeEnum.TIME_COMPARE.getId().equals(listVO.getReportType())) {
            listVO.setComparisonIndicator("");
            fieldKey = AnalysisReportFieldEnum.get(listVO.getCompareField()).getFieldKey();
        }

        assert listVO != null;
        DateTypeEnum dateTypeEnum = DateTypeEnum.get(listVO.getDateType());
        // 如果是按日期为y轴
        if (groupField.equals("`day`,`hour`")) {

            dates = listData.stream().map(reportListDTO -> DateUtils.hour2Name(reportListDTO.getHour())).distinct().sorted().collect(Collectors.toList());
            // 先循环日期
            for (String date : dates) {
                for (ReportListDTO reportListDTO : listData) {
                    String day = DateUtils.long2String(reportListDTO.getDay());
                    String hour = DateUtils.hour2Name(reportListDTO.getHour());
                    Object value = this.getObject2BigDecimal(reportListDTO, indicators.get(0));
                    if (dayDateMap.get(day) == null) {
                        // 填充默认小时映射Map，并为小时补0
                        Map<String, Object> hourMap = new LinkedHashMap<>();
                        dates.forEach(u -> hourMap.put(u, 0));
                        hourMap.put(hour, value);
                        dayDateMap.put(day, hourMap);
                    } else {
                        if (date.equals(hour)) {
                            dayDateMap.get(day).put(hour, value);
                        }
                    }
                }
            }
        } else {
            for (ReportListDTO reportListDTO : listData) {
                // 填充时间轴
                switch (dateTypeEnum) {
                    case DAY:
                        dates.add(DateUtils.long2String(reportListDTO.getDay()));
                        break;
                    case HOUR:
                        dates.add(DateUtils.hour2Name(reportListDTO.getHour()));
                        break;
                    default:
                        switch (groupField) {
                            case "`day`":
                                dates.add(DateUtils.long2String(reportListDTO.getDay()));
                                break;
                            case "dim_report_hour":
                                dates.add(DateUtils.format(DateUtils.long2Date(reportListDTO.getDayHour()), "yyyy-MM-dd HH:mm:ss"));
                                break;
                            case "`hour`":
                                dates.add(DateUtils.hour2Name(reportListDTO.getHour()));
                                break;
                        }
                }
                if (ObjectUtils.isNotNullOrZero(isIncludeUpdateRecord)) {
                    // 如果当前小时存在记录
                    remarks.add(CollectionUtils.isNotEmpty(hourRecordMap.get(reportListDTO.getDayHour()))
                            ? hourRecordMap.get(reportListDTO.getDayHour()).stream()
                            .map(record -> new SelectDTO2(record.getDate(), record.getContent())).collect(Collectors.toList()) : List.of());
                }
                // 如果是时间对比报表，且对比维度不为时间，则根据对比维度汇总数据
                if (ReportTypeEnum.TIME_COMPARE.getId().equals(listVO.getReportType())) {
                    Object value = this.getObject2BigDecimal(reportListDTO, indicators.get(0));
                    String fieldKeyValue = reportListDTO.getDay().toString() +
                            (List.of(AnalysisReportFieldEnum.REPORT_DATE.getKey(), AnalysisReportFieldEnum.REPORT_HOUR.getKey()).contains(listVO.getCompareField())
                                    ? "" : "_" + ObjectUtils.getObjectValue(reportListDTO, fieldKey));
                    if (valueMap.get(fieldKeyValue) == null) {
                        valueMap.put(fieldKeyValue, new ArrayList<>(List.of(value)));
                    } else {
                        valueMap.get(fieldKeyValue).add(value);
                    }
                } else {
                    // 如果是其他维度，则根据对比指标汇总数据
                    for (String indicator : indicators) {
                        Object value = this.getObject2BigDecimal(reportListDTO, indicator);
                        if (valueMap.get(indicator) == null) {
                            valueMap.put(indicator, new ArrayList<>(List.of(value)));
                        } else {
                            valueMap.get(indicator).add(value);
                        }
                    }
                }
            }
        }

        // 补充数据
        multiIndexChartDTO.setXAxis(List.of(new LegendDTO(dates.stream().distinct().collect(Collectors.toList()))));
        // 如果是按日期为y轴
        if (groupField.equals("`day`,`hour`")) {
            List<SeriesDTO> series = new ArrayList<>();
            dayDateMap.forEach((key, value) -> series.add(new SeriesDTO(key, "line", new ArrayList<>(value.values()))));
            multiIndexChartDTO.setSeries(series);
            // 取dayDateMap的key列表（天）做列名
            multiIndexChartDTO.setLegend(new LegendDTO(new ArrayList<>(dayDateMap.keySet())));
        } else {
            // 填充y轴名称
            List<String> legendList = findReportFieldsTitle(module, identify, indicators);
            AtomicInteger index = new AtomicInteger(0);
            if (ReportTypeEnum.TIME_COMPARE.getId().equals(listVO.getReportType())) {

                Map<Long, String> compareMap = this.getMapByList(listData, listVO, AnalysisReportFieldEnum.get(listVO.getCompareField()));
                List<SeriesDTO> seriesDTOS = new ArrayList<>();
                Map<String, String> keyMap = new HashMap<>();
                List<String> fieldIds = new ArrayList<>();
                multiIndexChartDTO.setLegend(new LegendDTO(valueMap.keySet().stream().map(key -> {
                    // 如果是分时，且对比维度为时间，时间返回时间天
                    if (List.of(AnalysisReportFieldEnum.REPORT_DATE.getKey(), AnalysisReportFieldEnum.REPORT_HOUR.getKey()).contains(listVO.getCompareField())) {
                        keyMap.put(key, DateUtils.long2String(Long.valueOf(key)));
                        fieldIds.add(key);
                        return DateUtils.long2String(Long.valueOf(key));
                    }
                    String[] keys = key.split("_");
                    String res = DateUtils.long2String(Long.valueOf(keys[0])) + " " + compareMap.getOrDefault(Long.valueOf(key.split("_")[1]), ConstantUtils.UNKNOWN);
                    keyMap.put(key, res);
                    // 如果是天趋势，返回day，如果是小时趋势，返回day-数据
                    if (DateTypeEnum.DAY.equals(dateTypeEnum)) {
                        fieldIds.add(keys[1]);
                        return compareMap.getOrDefault(Long.valueOf(keys[1]), ConstantUtils.UNKNOWN);
                    } else {
                        return res;
                    }
                }).distinct().collect(Collectors.toList())));
                if (DateTypeEnum.DAY.equals(dateTypeEnum)) {
                    if (AnalysisReportFieldEnum.REPORT_HOUR.getKey().equals(listVO.getCompareField())) {
                        seriesDTOS.add(new SeriesDTO(legendList.get(0), "line", multiIndexChartDTO.getXAxis().get(0).getData().stream().map(date -> {
                            if (valueMap.get(String.valueOf(DateUtils.string2Long(date))) != null) {
                                return valueMap.get(String.valueOf(DateUtils.string2Long(date))).get(0);
                            } else {
                                return 0;
                            }
                        }).collect(Collectors.toList())));
                    } else {
                        seriesDTOS = fieldIds.stream().distinct().map(fieldId -> {
                            SeriesDTO seriesDTO = new SeriesDTO();
                            seriesDTO.setName(compareMap.getOrDefault(Long.valueOf(fieldId), ConstantUtils.UNKNOWN));
                            seriesDTO.setType("line");
                            seriesDTO.setData(multiIndexChartDTO.getXAxis().get(0).getData().stream().map(date -> {
                                if (valueMap.get(DateUtils.string2Long(date) + "_" + fieldId) != null) {
                                    return valueMap.get(DateUtils.string2Long(date) + "_" + fieldId).get(0);
                                } else {
                                    return 0;
                                }
                            }).collect(Collectors.toList()));
                            return seriesDTO;
                        }).collect(Collectors.toList());
                    }
                } else {
                    for (Map.Entry<String, List<Object>> entry : valueMap.entrySet()) {
                        seriesDTOS.add(new SeriesDTO(keyMap.get(entry.getKey()), "line", entry.getValue()));
                    }
                }
                multiIndexChartDTO.setSeries(seriesDTOS);
            } else {
                multiIndexChartDTO.setLegend(new LegendDTO(legendList));
                multiIndexChartDTO.setSeries(indicators.stream().map(indicator ->
                        new SeriesDTO(legendList.get(index.getAndIncrement()), "line", valueMap.getOrDefault(indicator, List.of()))).collect(Collectors.toList()));
            }
        }
        multiIndexChartDTO.setRemark(List.of(new RemarkDTO(remarks)));
        return multiIndexChartDTO;
    }

    /**
     * 获取List类型报表数据
     *
     * @param listVO         查询参数
     * @param reportTypeEnum 报表类型
     * @param queryWrapper   查询Wrapper
     * @return 返回数据
     */
    private List<CreativeHourReportListDTO> getDataList(ReportListVO listVO, ReportTypeEnum reportTypeEnum, QueryWrapper<?> queryWrapper) {

        switch (reportTypeEnum) {
            case CREATIVE:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return this.sheinCreativeUnitHourMapper.getReportList(queryWrapper);
                } else {
                    //增加特殊字段限制
                    String customFieldsSql = this.customFieldSql(listVO);
                    if (StringUtils.isBlank(customFieldsSql)) {
                        return this.creativeUnitHourMapper.getReportList(queryWrapper);
                    } else {
                        return this.creativeUnitHourMapper.getReportList2(queryWrapper, customFieldsSql);
                    }
                }
            case AGENT:
            case MASTER:
            case CAMPAIGN:
            case PLAN:
            case TIME:
            case TIME_HOUR:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return this.sheinCreativeUnitHourMapper.getReportList(queryWrapper);
                } else {
                    //增加特殊字段限制
                    String customFieldsSql = this.customFieldSql(listVO);
                    if (StringUtils.isBlank(customFieldsSql)) {
                        return this.planHourMapper.getReportList(queryWrapper);
                    } else {
                        return this.planHourMapper.getReportList2(queryWrapper, customFieldsSql);
                    }
                }
            case PLAN_GROUP:
                return this.planGroupHourMapper.getReportList(queryWrapper);
            case TIME_COMPARE:
            case CUSTOM:
                if (ReportTypeEnum.CUSTOM.equals(reportTypeEnum)) {
                    return (CollectionUtils.isNotEmpty(listVO.getDimensions()) && listVO.getDimensions().contains(AnalysisReportFieldEnum.PACKAGE.getKey()))
                            ? this.packageHourMapper.listReportJoinPlan(queryWrapper) : this.planEpHourMapper.getReportListJoinPlan(queryWrapper);
                }
                return this.planEpHourMapper.getReportList(queryWrapper);
            case RTA:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return this.sheinCreativeUnitHourMapper.getReportList(queryWrapper);
                } else {
                    return this.planRtaHourMapper.listRtaReport(queryWrapper);
                }
            case REGION:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return this.sheinCreativeUnitHourMapper.getReportList(queryWrapper);
                } else {
                    return this.planAreaHourMapper.listAreaReport(queryWrapper);
                }
            case SSP:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return List.of();
                } else {
                    return this.planSspHourMapper.listSspReport(queryWrapper);
                }
            case DEAL:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return List.of();
                } else {
                    return this.dealHourMapper.listDealReport(queryWrapper);
                }
            default:
                return List.of();
        }
    }

    /**
     * 获取Page类型报表数据
     *
     * @param listVO         传入参数
     * @param reportTypeEnum 报表类型
     * @param queryWrapper   查询Wrapper
     * @return 返回数据
     */
    private IPage<CreativeHourReportListDTO> getDataPageList(ReportListVO listVO, ReportTypeEnum reportTypeEnum, QueryWrapper<?> queryWrapper) {

        IPage<CreativeHourReportListDTO> page = new Page<>(listVO.getPage(), listVO.getPageNum());
        switch (reportTypeEnum) {
            case CREATIVE:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return this.sheinCreativeUnitHourMapper.getReportPageList(page, queryWrapper);
                } else {
                    String customFieldsSql = this.customFieldSql(listVO);
                    if (StringUtils.isBlank(customFieldsSql)) {
                        return this.creativeUnitHourMapper.getReportPageList(page, queryWrapper);
                    } else {
                        return this.creativeUnitHourMapper.getReportPageList2(page, queryWrapper, customFieldsSql);
                    }
                }
            case AGENT:
            case MASTER:
            case CAMPAIGN:
            case PLAN:
            case TIME:
            case TIME_HOUR:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return this.sheinCreativeUnitHourMapper.getReportPageList(page, queryWrapper);
                } else {
                    String customFieldsSql = this.customFieldSql(listVO);
                    if (StringUtils.isBlank(customFieldsSql)) {
                        return this.planHourMapper.getReportPageList(page, queryWrapper);
                    } else {
                        return this.planHourMapper.getReportPageList2(page, queryWrapper, customFieldsSql);
                    }
                }
            case PLAN_GROUP:
                return this.planGroupHourMapper.getReportPageList(page, queryWrapper);
            case TIME_COMPARE:
                if (AnalysisReportFieldEnum.PACKAGE.getKey().equals(listVO.getCompareField())) {
                    IPage<CreativeHourReportListDTO> pageData = new Page<>();
                    pageData.setRecords(this.packageHourMapper.listPkgReport(queryWrapper
                            .last("LIMIT " + listVO.getPageNum())));
                    return pageData;
                }
                return this.planEpHourMapper.getReportPageList(page, queryWrapper);
            case RTA:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return this.sheinCreativeUnitHourMapper.getReportPageList(page, queryWrapper);
                } else {
                    return this.planRtaHourMapper.listRtaReportPage(page, queryWrapper);
                }
            case REGION:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return this.sheinCreativeUnitHourMapper.getReportPageList(page, queryWrapper);
                } else {
                    return this.planAreaHourMapper.listAreaReportPage(page, queryWrapper);
                }
            case SSP:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return page;
                } else {
                    return this.planSspHourMapper.listSspReportPage(page, queryWrapper);
                }
            case DEAL:
                if (PutEnum.NOT_PUT.getId().equals(listVO.getIsPut())) {
                    return page;
                } else {
                    return this.dealHourMapper.listDealReportPage(page, queryWrapper);
                }
            default:
                return page;
        }
    }

    /**
     * 填充报表中的投放相关字段
     *
     * @param resultList       列表
     * @param creativeDataList 投放数据List
     * @param reportTypeEnum   报表类型
     */
    private void fillData(List<CreativeHourReportListDTO> resultList, List<CreativeUnitAllDataListDTO> creativeDataList, ReportTypeEnum reportTypeEnum, ReportListVO listVO) {

        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }
        String fieldName = HumpLineUtils.lineToHump(reportTypeEnum.getGroupField().replace("dim_", ""));
        AnalysisReportFieldEnum analysisReportFieldEnum = ReportTypeEnum.TIME_COMPARE.getId().equals(listVO.getReportType())
                ? AnalysisReportFieldEnum.get(listVO.getCompareField()) : null;
        // 获取对应报表类型的投放数据Map映射
        Map<Long, CreativeUnitAllDataListDTO> infoMap = List.of(ReportTypeEnum.TIME, ReportTypeEnum.TIME_HOUR,
                ReportTypeEnum.TIME_COMPARE, ReportTypeEnum.RTA).contains(reportTypeEnum) ? new HashMap<>()
                : creativeDataList.stream().collect(Collectors.toMap(u ->
                Long.parseLong(Objects.requireNonNull(ObjectUtils.getObjectValue(u, fieldName)).toString()), Function.identity(), (u1, u2) -> u2));

        Map<Long, String> rtaMap = new HashMap<>() {{
            if (ReportTypeEnum.RTA.equals(reportTypeEnum)) {
                RtaSelectGetVO selectGetVO = new RtaSelectGetVO();
                selectGetVO.setIds(resultList.stream().map(CreativeHourReportListDTO::getRtaId).collect(Collectors.toList()));
                fgMarketService.selectRtaStrategy(selectGetVO).getData().forEach(selectDTO -> put(selectDTO.getId(), selectDTO.getName()));
            }
        }};
        Map<Long, String> compareMap = this.getMapByList(resultList.stream().map(u -> {
            ReportListDTO reportListDTO = new ReportListDTO();
            BeanUtils.copyProperties(u, reportListDTO);
            return reportListDTO;
        }).collect(Collectors.toList()), listVO, analysisReportFieldEnum);
        AtomicInteger index = new AtomicInteger(0);
        for (CreativeHourReportListDTO entity : resultList) {
            entity.setReportDate(DateUtils.long2String(entity.getDay()));
            entity.setReportHour(DateUtils.hour2Name(entity.getHour()));
            // 如果报表类型为RTA报表，只需要设置RTA名称，不需要设置时间及其他信息
            if (ReportTypeEnum.RTA.equals(reportTypeEnum)) {
                entity.setRtaName(rtaMap.getOrDefault(entity.getRtaId(), ""));
                continue;
            }
            if (ReportTypeEnum.REGION.equals(reportTypeEnum)) {
                entity.setCountryName(ObjectUtils.isNullOrZero(entity.getCountryId()) ? ConstantUtils.UNKNOWN : infoMap.get(entity.getCountryId()).getCountryName());
                continue;
            }
            // 如果报表类型为时间报表，不需要设置投放数据信息，则直接跳过
            // 小时报表需要根据dim_report_hour重新定义小时数据
            if (List.of(ReportTypeEnum.TIME, ReportTypeEnum.TIME_HOUR, ReportTypeEnum.TIME_COMPARE).contains(reportTypeEnum)) {
                if (ReportTypeEnum.TIME_HOUR.equals(reportTypeEnum) && listVO.getIsMerge() == 1) {
                    entity.setReportHour(DateUtils.hour2Str(entity.getHour()));
                } else {
                    entity.setReportHour(DateUtils.format(DateUtils.long2Date(entity.getDayHour()), "HH:mm:ss"));
                }
                // 如果是时间报表，设置差值
                if (List.of(ReportTypeEnum.TIME, ReportTypeEnum.TIME_HOUR).contains(reportTypeEnum) && index.get() < resultList.size() - 1) {
                    this.fillDiffOfObjects(entity, resultList.get(index.getAndIncrement() + 1));
                }
                // 如果是时间对比报表，且映射Map不为空，则设置对应Name
                if (ReportTypeEnum.TIME_COMPARE.equals(reportTypeEnum) && CollectionUtils.isNotEmpty(compareMap)) {
                    assert analysisReportFieldEnum != null;
                    ObjectUtils.setObjectValue(entity, analysisReportFieldEnum.getKey(),
                            compareMap.getOrDefault(Long.valueOf(Objects.requireNonNull(ObjectUtils.getObjectValue(entity, analysisReportFieldEnum.getFieldKey())).toString()),
                                    ConstantUtils.UNKNOWN));
                }
                continue;
            }
            // 获取并设置具体的投放数据信息
            CreativeUnitAllDataListDTO creativeDTO = infoMap.get(
                    Long.parseLong(Objects.requireNonNull(ObjectUtils.getObjectValue(entity, fieldName)).toString()));
            if (creativeDTO == null) {
                continue;
            }
            entity.setAgentId(creativeDTO.getAgentId());
            entity.setAgentName(creativeDTO.getAgentName());
            entity.setMasterId(creativeDTO.getMasterId());
            entity.setMasterName(creativeDTO.getMasterName());
            entity.setAuditMasterId(creativeDTO.getAuditMasterId());
            entity.setAuditMasterName(creativeDTO.getAuditMasterName());
            entity.setCampaignId(creativeDTO.getCampaignId());
            entity.setCampaignName(creativeDTO.getCampaignName());
            entity.setMarketTarget(creativeDTO.getMarketTarget());
            entity.setMarketTargetName(creativeDTO.getMarketTargetName());
            entity.setPutOnTarget(creativeDTO.getPutOnTarget());
            entity.setPutOnTargetName(creativeDTO.getPutOnTargetName());
            entity.setPlanId(creativeDTO.getPlanId());
            entity.setPlanName(creativeDTO.getPlanName());
            entity.setPlanGroupId(creativeDTO.getPlanGroupId());
            entity.setPlanGroupName(creativeDTO.getPlanGroupName());
            entity.setCreativeId(creativeDTO.getCreativeId());
            entity.setCreativeUnitName(creativeDTO.getCreativeUnitName());
            entity.setSlotType(creativeDTO.getSlotType());
            entity.setSlotTypeName(creativeDTO.getSlotTypeName());
            entity.setOsType(creativeDTO.getOsType());
            entity.setOsTypeName(creativeDTO.getOsTypeName());
            entity.setCreativeType(creativeDTO.getCreativeType());
            entity.setCreativeTypeName(creativeDTO.getCreativeTypeName());
            entity.setAssets(creativeDTO.getAssets());
        }
    }

    private void fillDiffOfObjects(CreativeHourReportListDTO currentObj, CreativeHourReportListDTO targetObj) {

        for (Field field : currentObj.getClass().getSuperclass().getSuperclass().getDeclaredFields()) {
            try {
                String firstUpperKey = HumpLineUtils.firstToUpper(field.getName());
                Object currentValue = ObjectUtils.getObjectValue(currentObj, field.getName()),
                        targetValue = ObjectUtils.getObjectValue(targetObj, field.getName());
                // 计算差值
                ObjectUtils.setObjectValue(currentObj, "diff" + firstUpperKey,
                        ObjectUtils.getObjectCalculatedResult(currentValue, targetValue, "-"));
                // 计算差值比例
                ObjectUtils.setObjectValue(currentObj, "diffRate" + firstUpperKey,
                        ObjectUtils.getObjectCalculatedResult(ObjectUtils.getObjectCalculatedResult(ObjectUtils.getObjectValue(currentObj, "diff" + firstUpperKey),
                                100L, "*"), targetValue, "/"));
            } catch (Exception ignored) {
            }
        }
    }

    private Map<Long, String> getMapByList(List<ReportListDTO> resultList, ReportListVO listVO, AnalysisReportFieldEnum analysisReportFieldEnum) {

        if (analysisReportFieldEnum == null) {
            return new HashMap<>();
        }
        List<SelectDTO> selectDTOS = this.getListByFieldType(resultList, listVO, analysisReportFieldEnum);
        if (CollectionUtils.isEmpty(selectDTOS)) {
            return new HashMap<>();
        }
        return selectDTOS.stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
    }

    private List<SelectDTO> getListByFieldType(List<ReportListDTO> reportList, ReportListVO listVO, AnalysisReportFieldEnum analysisReportFieldEnum) {

        switch (analysisReportFieldEnum) {
            case MASTER:
                return this.fgMarketService.selectMaster().getData();
            case CAMPAIGN:
                CampaignSelectGetVO campaignGetVO = new CampaignSelectGetVO();
                campaignGetVO.setMasterId(listVO.getMasterId());
                campaignGetVO.setMasterIds(listVO.getMasterIds().stream().map(Long::valueOf).collect(Collectors.toList()));
                campaignGetVO.setCampaignIds(reportList.stream().map(ReportListDTO::getCampaignId)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                campaignGetVO.setIsPut(listVO.getIsPut());
                return this.fgMarketService.selectCampaign(campaignGetVO).getData();
            case PLAN:
                PlanSelectGetVO planGetVO = new PlanSelectGetVO();
                planGetVO.setMasterId(listVO.getMasterId());
                planGetVO.setMasterIds(listVO.getMasterIds().stream().map(Long::valueOf).collect(Collectors.toList()));
                planGetVO.setPlanIds(reportList.stream().map(ReportListDTO::getPlanId)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                return this.fgMarketService.selectPlan(planGetVO).getData();
            case PLAN_GROUP:
                PlanSelectGetVO planGroupGetVO = new PlanSelectGetVO();
                planGroupGetVO.setMasterId(listVO.getMasterId());
                planGroupGetVO.setMasterIds(listVO.getMasterIds().stream().map(Long::valueOf).collect(Collectors.toList()));
                planGroupGetVO.setPlanIds(reportList.stream().map(ReportListDTO::getPlanGroupId)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                return this.fgMarketService.selectPlan(planGroupGetVO).getData();
            case ADX:
                return this.fgMarketService.selectAdx().getData();
            case EP:
                return this.fgMarketService.selectEp(new EpSelectGetVO()).getData();
            case RTA:
                RtaSelectGetVO getVO = new RtaSelectGetVO();
                getVO.setIsSearchMasterIds(0);
                getVO.setIds(reportList.stream().map(ReportListDTO::getRtaId)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                return this.fgMarketService.selectRtaStrategy(getVO).getData();
            case REPORT_HOUR:
            default:
                return List.of();
        }
    }

    private BigDecimal getObject2BigDecimal(Object object, String indicator) {
        Object obj = ObjectUtils.getObjectValue(object, indicator);
        return ObjectUtils.isNullOrZero(obj) ? new BigDecimal(0) : new BigDecimal(obj.toString());
    }

    private QueryWrapper<PlanHour> getPlanHourWrapper(Long masterId, Long startDate, Long endDate, String module, String identify, Integer timeZone) {

        Long day = DateUtils.string2Long(DateUtils.format(DateUtils.long2Date(startDate)));
        return new QueryWrapper<PlanHour>()
                .select("FROM_UNIXTIME(dim_report_hour + " + 3600 * Objects.requireNonNull(TimeZoneEnum.get(timeZone)).getFormatHour() + ", '%H') AS `hour`," +
                        this.translateField(ReportTypeEnum.MASTER.getGroupField()) + "," + findReportFields(module, identify, ""))
                .eq("dim_master_id", masterId)
                .between("dim_report_hour", startDate, endDate)
                .groupBy("dim_master_id")
                // 如果开始日期等于当日时间，则表示查询今天数据，则查询10点前数据
                .having(day.equals(DateUtils.date2Long(DateUtils.getTodayDate())), "`hour` <= 10");
    }

    private CreativeHourReportListDTO fillTodayData(CreativeHourReportListDTO todayData, CreativeHourReportListDTO compareData) {

        CreativeHourReportListDTO resultData = new CreativeHourReportListDTO();
        if (todayData == null) {
            BeanUtils.copyProperties(compareData, resultData);
        } else {
            // 先将其他查询结果赋值
            BeanUtils.copyProperties(todayData, resultData);
        }
        // 再遍历field依次赋值
        for (Field field : BaseReportDTO.class.getDeclaredFields()) {
            try {
                String firstUpperKey = HumpLineUtils.firstToUpper(field.getName());

                Object fieldData = this.getObjectByField(ObjectUtils.getObjectValue(compareData, field.getName()), field.getType()),
                        todayFieldData = this.getObjectByField(ObjectUtils.getObjectValue(todayData, field.getName()), field.getType());
                ObjectUtils.setObjectValue(resultData, field.getName(), fieldData);
                ObjectUtils.setObjectValue(resultData, "today" + firstUpperKey, todayFieldData);
                // 计算差值
                ObjectUtils.setObjectValue(resultData, "diff" + firstUpperKey,
                        ObjectUtils.getObjectCalculatedResult(todayFieldData, fieldData, "-"));
                // 计算差值比例
                ObjectUtils.setObjectValue(resultData, "diffRate" + firstUpperKey,
                        ObjectUtils.getObjectCalculatedResult(ObjectUtils.getObjectCalculatedResult(ObjectUtils.getObjectValue(resultData, "diff" + firstUpperKey),
                                100L, "*"), fieldData, "/"));
            } catch (Exception exception) {
                log.error(exception.getMessage(), exception);
            }
        }
        return resultData;
    }

    private Object getObjectByField(Object object, Class<?> classType) {
        if (object != null) {
            return object;
        }
        if (classType.equals(Long.class)) {
            return 0L;
        }
        if (classType.equals(Double.class)) {
            return 0D;
        }
        return 0;
    }

    private CreativeHourReportListDTO fillCompareData(CreativeHourReportListDTO todayData, CreativeHourReportListDTO compareData) {

        if (todayData == null && compareData == null) {
            return null;
        }

        return this.fillTodayData(todayData, compareData);
    }

    /**
     * 获取指标string 给sql
     *
     * @param module   数据
     * @param identify 标识
     * @return 返回数据
     */
    @Override
    public String findReportFields(String module, String identify, String orderBy) {
        return this.getFields(module, identify, orderBy);
    }

    private String getFields(String module, String identify, String orderBy) {
        if (StringUtils.isBlank(module)) {
            return ReportFieldEnum.getBaseIndicatorRules();
        }
        String fields = this.fgSystemService.getUserCustomIndexReportSelect(CustomIndexGetVO.builder()
                .module(module).identify(identify).build()).getData();
        //解决 orderBy不在内部的问题
        if (StringUtils.isNotBlank(orderBy)) {
            try {
                ReportFieldEnum reportFieldEnum = ReportFieldEnum.getByField(orderBy);
                String[] plant = reportFieldEnum.getRule().split("AS");
                if (!(fields.contains(String.format("AS `%s`", plant[plant.length - 1].trim())) ||
                        fields.contains(String.format("AS %s", plant[plant.length - 1].trim()))
                )) {
                    fields = String.format("%s,%s", fields, reportFieldEnum.getRule());
                }
            } catch (Exception ignore) {
            }
        }
        return fields;
    }

    @Override
    public List<SelectDTO2> findReportHeader(List<String> fieldKeys, String module, String identify, Class<?> clazz) {
        Map<String, String> fieldMap = new HashMap<>();
        if (StringUtils.isNotBlank(module)) {
            fieldMap.putAll(fgSystemService.getUserCustomIndexReportDownload(CustomIndexGetVO.builder()
                            .module(module).identify(identify).build()).getData()
                    .stream().flatMap(u -> u.getColumns().stream())
                    .collect(Collectors.toMap(CustomIndexChildColumnDTO::getKey, CustomIndexChildColumnDTO::getTitle, (o, n) -> n))
            );
        }
        if (null != clazz) {
            // 获取DTO里反射Map
            fieldMap.putAll(ObjectUtils.getFields(clazz)
                    .stream().filter(field -> field.getAnnotation(ExcelProperty.class) != null)
                    .collect(Collectors.toMap(Field::getName, field -> field.getAnnotation(ExcelProperty.class).value()[0]))
            );
        }
        return fieldKeys.stream().map(field -> {
            if (!fieldMap.containsKey(field)) {
                return null;
            }
            return new SelectDTO2(field, fieldMap.get(field));
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<String> findReportFieldsTitle(String module, String identify, List<String> keys) {
        Map<String, String> keyMap = fgSystemService.getUserCustomIndexReport(CustomIndexGetVO.builder()
                        .module(module).identify(identify).build()).getData()
                .stream().collect(Collectors.toMap(CustomIndexChildColumnDTO::getKey, CustomIndexChildColumnDTO::getTitle));
        return keys.stream().map(key -> keyMap.getOrDefault(key, "")).collect(Collectors.toList());
    }

    @Override
    public void updateReportHour() {
        Long date = DateUtils.date2Long(DateUtils.afterDay(new Date(), -5));
        this.creativeUnitHourMapper.updateCreativeUnitReportHour(date);
        this.assetHourMapper.updateAssetReportHour(date);
    }

    /**
     * shein 参数处理
     *
     * @param listVO 参数
     */
    private void sheinDeal(ReportListVO listVO, User user) {
        if (null == user) {
            listVO.setIsPut(PutEnum.IS_PUT.getId());
        } else if (ReportTypeEnum.PLAN_GROUP.getId().equals(listVO.getReportType())) {
            listVO.setIsPut(PutEnum.NOT_PUT.getId());
        } else {
            listVO.setIsPut(sheinConfiguration.isPut(listVO.getIsPut(), user.getRoleId()));
        }
    }

    /**
     * 获取 report list
     *
     * @param listVO list
     * @return 返回数据
     */
    private String customFieldSql(ReportListVO listVO) {
        this.filterFieldRules(listVO);
        // 获取需要过滤的SQL
        if (CollectionUtils.isEmpty(listVO.getFieldRules())
                || !List.of(ReportTypeEnum.CAMPAIGN.getId(), ReportTypeEnum.PLAN.getId(), ReportTypeEnum.CREATIVE.getId()).contains(listVO.getReportType())) {
            return "";
        }
        // 获取需要过滤的SQL
        Map<String, String> conditionMap = Map.of("gt", ">", "ge", ">=", "equal", "=",
                "lt", "<", "le", "<=");
        return " WHERE " + listVO.getFieldRules().stream()
                .map(fieldRule -> String.format("report.%s %s %s", HumpLineUtils.humpToLine2(fieldRule.getKey()), conditionMap.get(fieldRule.getCondition()), fieldRule.getValue()))
                .collect(Collectors.joining(" AND "));
    }

    /**
     * 优化数据
     *
     * @param sql sql
     * @return 返回数据
     */
    private String innerSelectSql(String sql) {
        List<String> fieldList = new ArrayList<>();
        // 获取对应的field
        Pattern pattern = Pattern.compile("SUM\\([`a-z0-9_]*\\)");
        Matcher matcher = pattern.matcher(sql);
        while (matcher.find()) {
            fieldList.add(matcher.group());
        }
        return fieldList.stream().map(this::getKeyField).distinct().collect(Collectors.joining(" ,"));
    }

    /**
     * 数据sql
     *
     * @param sumSql sum sql
     * @return 返回数据
     */
    private String getKeyField(String sumSql) {
        String asName = "";
        // 获取对应的field
        Pattern pattern = Pattern.compile("\\([`a-z0-9_]*\\)");
        Matcher matcher = pattern.matcher(sumSql);
        while (matcher.find()) {
            asName = matcher.group();
        }
        return sumSql + " AS " + asName.replace("(", "").replaceAll("`", "").replace(")", "");
    }

    /**
     * 获取total 过滤数据
     *
     * @param listVO 条件
     * @return 返回数据
     */
    private String totalCustomFieldSql(ReportListVO listVO) {
        this.filterFieldRules(listVO);
        if (CollectionUtils.isEmpty(listVO.getFieldRules())
                || !List.of(ReportTypeEnum.CAMPAIGN.getId(), ReportTypeEnum.PLAN.getId(), ReportTypeEnum.CREATIVE.getId()).contains(listVO.getReportType())) {
            return "";
        }
        Map<String, CustomIndexFieldDTO> keyFieldMap = this.fgSystemService.getCustomIndexFieldMap(CustomIndexGetVO.builder()
                        .identify(listVO.getIdentify()).module(listVO.getModule()).build()).getData()
                .stream().collect(Collectors.toMap(CustomIndexFieldDTO::getKey, Function.identity()));
        Map<String, String> conditionMap = Map.of("gt", ">", "ge", ">=", "equal", "=",
                "lt", "<", "le", "<=");
        return "WHERE " + listVO.getFieldRules().stream()
                .map(fieldRule -> keyFieldMap.get(fieldRule.getKey()).getRule().replace("SUM", "")
                        + conditionMap.get(fieldRule.getCondition()) + fieldRule.getValue())
                .collect(Collectors.joining(" AND "));
    }

    /**
     * 进行数据过滤
     *
     * @param listVO 条件
     */
    private void filterFieldRules(ReportListVO listVO) {
        if (CollectionUtils.isNotEmpty(listVO.getFieldRules())) {
            listVO.setFieldRules(
                    listVO.getFieldRules().stream()
                            .filter(u -> StringUtils.isNotBlank(u.getKey())
                                    && StringUtils.isNotBlank(u.getValue())
                                    && StringUtils.isNotBlank(u.getCondition()))
                            .collect(Collectors.toList())
            );
        }
    }
}
