package com.overseas.service.report.schedule;

import com.overseas.service.report.service.AnalysisReportService;
import com.overseas.service.report.service.ReportScheduleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online"})
public class ReportSchedule {

    private final AnalysisReportService analysisReportService;

    private final ReportScheduleService reportScheduleService;

    /**
     * 每隔十分钟拉取当前小时和上小时的数据到临时表
     */
    @Scheduled(fixedDelay = 600000)
    public void saveHourReportData() {
//        Long startDate = DateUtils.date2Long(DateUtils.getTodayDate());
//        int hour = DateUtils.getHour();
//        log.info("start save pkg date to temp, hour : {}", hour);
//        long startTime = System.currentTimeMillis();
//        this.analysisReportService.savePkgReport2Temp(startDate,startDate, hour, null, 0);
//        log.info("end save pkg date to temp, hour : {}, cost : {}", hour, System.currentTimeMillis() - startTime);
//        if (hour > 0) {
//            this.analysisReportService.savePkgReport2Temp(startDate,startDate, hour - 1, null, 0);
//            log.info("end save pkg date to temp, hour : {}, cost : {}", hour - 1, System.currentTimeMillis() - startTime);
//        }
    }

    /**
     * 每小时的第一分钟同步最近七天的数据中最近两小时有更新的数据
     */
    @Scheduled(cron = "0 1 * * * ?")
    public void saveRecentUpdateData() {
//        Date updateDate = DateUtils.formatHour(new Date(), -1);
//        Long startDate = DateUtils.date2Long(DateUtils.format(new Date(), -8));
//        Long endDate = DateUtils.date2Long(DateUtils.getTodayDate());
//
//        log.info("start save pkg data to temp, start date : {} , end date : {}, update date : {}",
//                startDate, endDate, updateDate);
//        long startTime = System.currentTimeMillis();
//        this.analysisReportService.savePkgReport2Temp(startDate, endDate, null, updateDate, 0);
//        log.info("end save pkg data to temp, start date : {} , end date : {}, update date : {} , cost : {}",
//                startDate, endDate, updateDate, System.currentTimeMillis() - startTime);
    }

    /**
     * 每天凌晨1时1分删除七天前数据
     */
    @Scheduled(cron = "1 1 1 * * ?")
//    @Scheduled(fixedDelay = 600000)
    public void deletePkgTempData() {
//        Long reportDate = DateUtils.date2Long(DateUtils.format(DateUtils.getTodayDate(), -45));
//        log.info("start delete pkg temp, report date : {}", reportDate);
//        long startTime = System.currentTimeMillis();
//        this.analysisReportService.deletePkgTemp(reportDate);
//        log.info("end delete pkg temp, report date : {} , cost : {}", reportDate,
//                System.currentTimeMillis() - startTime);
    }

    /**
     * 每两分钟执行一次扫描，如果有待创建的任务（不涉及包名维度），则执行
     */
    @Scheduled(fixedDelay = 120000)
    public void exportTask() {
        this.reportScheduleService.exportOfflineTask();
    }

    /**
     * 每十分钟执行一次扫描，如果有待创建的任务（涉及包名维度），则执行
     */
    @Scheduled(fixedDelay = 180000)
    public void exportIncludePkgTask() {
        this.reportScheduleService.exportOfflineIncludePkgTask();
    }
}
