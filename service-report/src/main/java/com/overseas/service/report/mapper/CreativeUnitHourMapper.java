package com.overseas.service.report.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.CreativeHourReportListDTO;
import com.overseas.common.dto.report.DimensionTotalDTO;
import com.overseas.common.dto.report.diagnosis.DiagnosisReportDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.CreativeUnitHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CreativeUnitHourMapper extends BaseMapper<CreativeUnitHour> {

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_creative_unit_hour ${ew.customSqlSegment}")
    List<CreativeHourReportListDTO> getReportList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT * FROM (SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_creative_unit_hour ${ew.customSqlSegment}) report ${customFieldsSql}")
    List<CreativeHourReportListDTO> getReportList2(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                   @Param("customFieldsSql") String customFieldsSql);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_creative_unit_hour ${ew.customSqlSegment}")
    IPage<CreativeHourReportListDTO> getReportPageList(IPage<CreativeHourReportListDTO> page, @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT * from (SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_creative_unit_hour ${ew.customSqlSegment}) report ${customFieldsSql}")
    IPage<CreativeHourReportListDTO> getReportPageList2(IPage<CreativeHourReportListDTO> page,
                                                        @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                        @Param("customFieldsSql") String customFieldsSql);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_creative_unit_hour ${ew.customSqlSegment}")
    CreativeHourReportListDTO getTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT ${finalSelectSql} from ( SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_creative_unit_hour ${ew.customSqlSegment} ) report ${customFieldsSql}")
    CreativeHourReportListDTO getTotal2(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                        @Param("finalSelectSql") String finalSelectSql,
                                        @Param("customFieldsSql") String customFieldsSql);

    @Update("UPDATE t_ads_dsp_flow_creative_unit_hour " +
            "SET dim_report_hour = dim_day + dim_hour * 3600 " +
            "WHERE dim_day > #{reportDate} AND dim_report_hour = 0 ")
    void updateCreativeUnitReportHour(@Param("reportDate") Long reportDate);

    /**
     * 维度数据查询
     *
     * @param wrapper      筛选条件
     * @param whereWrapper 筛选外部条件
     * @return 返回数据
     */
    @Select("SELECT  * from (" +
            "SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_creative_unit_hour " +
            "LEFT JOIN iflytek_overseas_dsp.m_plan " +
            "ON t_ads_dsp_flow_creative_unit_hour.dim_plan_id = iflytek_overseas_dsp.m_plan.id " +
            "${ew.customSqlSegment}) " +
            "t ${where.customSqlSegment}")
    List<DimensionTotalDTO> dimensionTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper, @Param("where") Wrapper<?> whereWrapper);


    @Select("SELECT ROUND( SUM( idx_report_cost / 1000000 ), 2 ) AS cost, count( fcuh.dim_plan_id ) AS num, " +
            " mcu.creative_unit_status AS creative_unit_status, mp.plan_status AS plan_status, " +
            " mc.campaign_status AS campaign_status  " +
            "FROM t_ads_dsp_flow_creative_unit_hour fcuh " +
            "LEFT JOIN iflytek_overseas_dsp.m_creative_unit mcu ON fcuh.dim_creative_unit_id = mcu.id " +
            "LEFT JOIN iflytek_overseas_dsp.m_plan mp ON fcuh.dim_plan_id = mp.id " +
            "LEFT JOIN iflytek_overseas_dsp.m_campaign mc ON fcuh.dim_campaign_id = mc.id  " +
            " ${ew.customSqlSegment}")
    List<DiagnosisReportDTO> creativeUnitDiagnosis(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT  * from t_ads_dsp_flow_creative_unit_hour ${ew.customSqlSegment}")
    List<JSONObject> getList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    @Select("SELECT " +
            "material_id,  " +
            "GROUP_CONCAT(DISTINCT concat(asset_id, \"--\", field_type, \"--\", ma.asset_type, \"--\", field_name) ) as asset  " +
            "FROM  " +
            "iflytek_overseas_dsp.m_material_asset  mma LEFT JOIN iflytek_overseas_dsp.m_asset ma ON mma.asset_id = ma.id " +
            "WHERE " +
            " material_id IN( ${materialId} ) " +
            "GROUP BY " +
            " material_id")
    List<JSONObject> getMaterialAsset(@Param("materialId") String materialIds);


    @Select("SELECT id, material_id from iflytek_overseas_dsp.m_creative_unit WHERE id IN( ${creativeUnitIds} ) ")
    List<JSONObject> getMaterialMap(@Param("creativeUnitIds") String creativeUnitIds);
}
