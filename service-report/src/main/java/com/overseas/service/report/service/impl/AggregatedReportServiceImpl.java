package com.overseas.service.report.service.impl;

import com.overseas.service.report.mapper.AssetDayMapper;
import com.overseas.service.report.service.AggregatedReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AggregatedReportServiceImpl implements AggregatedReportService {

    private final AssetDayMapper assetDayMapper;

    @Override
    public void batchInsertAssetDayReport(Long reportDate) {
        this.assetDayMapper.batchInsertAssetDayReport(reportDate);
    }
}
