package com.overseas.service.report.schedule;

import com.overseas.common.utils.DateUtils;
import com.overseas.service.report.service.RevenueDayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"online"})
public class RevenueReportSchedule {

    private final RevenueDayService revenueDayService;

    /**
     * 每小时的第10分钟获取当前小时对应时区的帐号的头一天的营收数据
     */
    @Scheduled(cron = "0 10 * * * ?")
//    @Scheduled(fixedDelay = 120000)
    public void updateRevenueDspData() {
        Date now = new Date();
        log.info("start exec get revenue : {}", DateUtils.format(now, DateUtils.DATE_TIME_PATTERN));
        this.revenueDayService.saveDspData(DateUtils.format(now, "yyyy-MM-dd HH:00:00"));

        // 更新指定日期往前推N天的数据
//        for (int j = 0; j <= 15; j++) {
//            Date day = DateUtils.string2Date("2025-06-08");
//            day = DateUtils.afterDay(day, j * -1);
//            for (int i = 0; i < 24; i++) {
//                Date date = DateUtils.afterHour(day, i);
//                log.info(DateUtils.format(date, DateUtils.DATE_TIME_PATTERN));
//                log.info("start exec get revenue : {}", DateUtils.format(date, DateUtils.DATE_TIME_PATTERN));
//                this.revenueDayService.saveDspData(DateUtils.format(date, "yyyy-MM-dd HH:00:00"));
//            }
//        }
    }

    /**
     * 第10分钟获取当前小时对应时区的帐号的头两天的营收数据
     */
    @Scheduled(cron = "0 10 8,16 * * ?")
//    @Scheduled(fixedDelay = 120000)
    public void updateRevenueDspDataByBeforeDay() {
        Date now = new Date();
        log.info("start exec refresh revenue : {}", DateUtils.format(now, DateUtils.DATE_TIME_PATTERN));
        Date reportHour = DateUtils.afterHour(now, -1);
        this.revenueDayService.revenueForUpdate(DateUtils.format(reportHour, "yyyy-MM-dd HH:00:00"));
        log.info("end exec refresh revenue : {}", DateUtils.format(now, DateUtils.DATE_TIME_PATTERN));
    }
}
