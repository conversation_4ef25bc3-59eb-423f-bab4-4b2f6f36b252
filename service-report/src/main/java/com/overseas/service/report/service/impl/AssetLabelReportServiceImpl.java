package com.overseas.service.report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.chart.LegendDTO;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.chart.SeriesDTO;
import com.overseas.common.dto.report.AssetLabelListDTO;
import com.overseas.common.entity.User;
import com.overseas.common.utils.*;
import com.overseas.common.vo.market.asset.assetLabel.AssetLabelSelectGetVO;
import com.overseas.common.vo.market.assetLabel.AssetLabelGetVO;
import com.overseas.common.vo.report.assetLabel.AssetLabelListVO;
import com.overseas.common.vo.report.assetLabel.AssetLabelTrendVO;
import com.overseas.common.vo.sys.customIndex.CustomIndexGetVO;
import com.overseas.common.vo.sys.user.UserMasterGetVO;
import com.overseas.service.report.entity.LabelHour;
import com.overseas.service.report.feign.FgMarketService;
import com.overseas.service.report.feign.FgSystemService;
import com.overseas.service.report.mapper.LabelHourMapper;
import com.overseas.service.report.service.AssetLabelReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class AssetLabelReportServiceImpl implements AssetLabelReportService {

    private final LabelHourMapper labelHourMapper;

    private final FgSystemService fgSystemService;

    private final FgMarketService fgMarketService;

    @Override
    public MultiIndexChartDTO assetLabelTrend(AssetLabelTrendVO trendVO, User user) {
        List<Long> masterIds = null;
        if (ObjectUtils.isNotNullOrZero(trendVO.getProjectId())) {
            masterIds = this.getMasterIdsByProject(trendVO.getProjectId(), user.getId().longValue());
            if (CollectionUtils.isEmpty(masterIds)) {
                return new MultiIndexChartDTO();
            }
        }
        String customIndexes = this.customSelect(trendVO.getProjectId());
        //计算top
        if (CollectionUtils.isEmpty(trendVO.getLabelIds())) {
            List<AssetLabelListDTO> list = this.labelHourMapper.list(new QueryWrapper<LabelHour>()
                    .select(customIndexes + ", dim_label_id as label_id")
                    .in(CollectionUtils.isNotEmpty(trendVO.getLabelIds()), "dim_label_id", trendVO.getLabelIds())
                    .in(CollectionUtils.isNotEmpty(trendVO.getMasterIds()), "dim_master_id", trendVO.getMasterIds())
                    .in(CollectionUtils.isNotEmpty(masterIds), "dim_master_id", masterIds)
                    .between("dim_day", DateUtils.string2Long(trendVO.getStartDate()), DateUtils.string2Long(trendVO.getEndDate()))
                    .in("dim_label_id", this.labelIds())
                    .groupBy("dim_label_id")
                    .orderByDesc(trendVO.getCustomIndexes().stream()
                            .map(u -> HumpLineUtils.humpToLine2(u.getId())).collect(Collectors.toList())
                    ).last("limit " + trendVO.getTop())
            );
            if (list.isEmpty()) {
                return new MultiIndexChartDTO();
            }
            trendVO.setLabelIds(list.stream().map(AssetLabelListDTO::getLabelId).collect(Collectors.toList()));
        }
        Map<Long, List<AssetLabelListDTO>> listMap = labelHourMapper.list(new QueryWrapper<LabelHour>()
                .select(customIndexes + ", dim_label_id as label_id, dim_day as day")
                .in(CollectionUtils.isNotEmpty(trendVO.getLabelIds()), "dim_label_id", trendVO.getLabelIds())
                .in(CollectionUtils.isNotEmpty(trendVO.getMasterIds()), "dim_master_id", trendVO.getMasterIds())
                .in(CollectionUtils.isNotEmpty(masterIds), "dim_master_id", masterIds)
                .between("dim_day", DateUtils.string2Long(trendVO.getStartDate()), DateUtils.string2Long(trendVO.getEndDate()))
                .lambda()
                .groupBy(LabelHour::getLabelId)
                .groupBy(LabelHour::getDay)
        ).stream().collect(Collectors.groupingBy(AssetLabelListDTO::getLabelId));
        if (listMap.isEmpty()) {
            return new MultiIndexChartDTO();
        }
        AssetLabelSelectGetVO assetLabelSelectGetVO = new AssetLabelSelectGetVO();
        assetLabelSelectGetVO.setIds(new ArrayList<>(listMap.keySet()));
        Map<Long, String> labelMap = fgMarketService.assetLabelSelect(assetLabelSelectGetVO)
                .getData().stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
        List<String> days = DateUtils.getBetweenDate(trendVO.getStartDate(), trendVO.getEndDate(), DateUtils.DATE_PATTERN);
        MultiIndexChartDTO multiIndexChartDTO = new MultiIndexChartDTO();
        multiIndexChartDTO.setXAxis(List.of(new LegendDTO(days)));
        multiIndexChartDTO.setSeries(new ArrayList<>());
        listMap.forEach((labelId, val) -> {
            Map<String, AssetLabelListDTO> labelDayMap = val.stream().collect(Collectors.toMap(u -> DateUtils.long2String(u.getDay()), Function.identity()));
            trendVO.getCustomIndexes().forEach(customIndex ->
                    multiIndexChartDTO.getSeries().add(
                            new SeriesDTO(String.format("%s-%s", labelMap.getOrDefault(labelId, "未知"), customIndex.getName()), "line",
                                    days.stream().map(day -> {
                                        if (labelDayMap.containsKey(day)) {
                                            return ObjectUtils.getObjectValue(labelDayMap.get(day), customIndex.getId());
                                        }
                                        return 0L;
                                    }).collect(Collectors.toList())
                            )
                    )
            );
        });
        multiIndexChartDTO.setLegend(new LegendDTO(multiIndexChartDTO.getSeries().stream().map(SeriesDTO::getName).collect(Collectors.toList())));
        return multiIndexChartDTO;
    }

    @Override
    public PageUtils<?> assetLabelList(AssetLabelListVO listVO, User user) {
        List<Long> masterIds = null;
        if (ObjectUtils.isNotNullOrZero(listVO.getProjectId())) {
            masterIds = this.getMasterIdsByProject(listVO.getProjectId(), user.getId().longValue());
            if (CollectionUtils.isEmpty(masterIds)) {
                return new PageUtils<>(List.of(), 0L);
            }
        }
        IPage<AssetLabelListDTO> iPage = new Page<>(listVO.getPage(), listVO.getPageNum());
        QueryWrapper<?> queryWrapper = new QueryWrapper<LabelHour>()
                .select(this.customSelect(listVO.getProjectId()) + ", dim_label_id as label_id, dim_day as day")
                .in(CollectionUtils.isNotEmpty(listVO.getLabelIds()), "dim_label_id", listVO.getLabelIds())
                .in(CollectionUtils.isNotEmpty(listVO.getMasterIds()), "dim_master_id", listVO.getMasterIds())
                .in(CollectionUtils.isNotEmpty(masterIds), "dim_master_id", masterIds)
                .between("dim_day", DateUtils.string2Long(listVO.getStartDate()), DateUtils.string2Long(listVO.getEndDate()))
                .in("dim_label_id", this.labelIds());
        if (CollectionUtils.isEmpty(listVO.getGroupBys())) {
            listVO.setGroupBys(List.of("labelId"));
        }
        AssetLabelListDTO total = this.labelHourMapper.totalList(queryWrapper);
        iPage = labelHourMapper.listAssetLabel(iPage, queryWrapper
                .groupBy(listVO.getGroupBys().stream().map(u -> {
                    if ("reportDate".equals(u)) {
                        return "dim_day";
                    }
                    return "dim_label_id";
                }).distinct().collect(Collectors.toList()))
                .orderBy(StringUtils.isNotBlank(listVO.getSortField()),
                        listVO.getSortType().equalsIgnoreCase("asc"),
                        HumpLineUtils.humpToLine2(listVO.getSortField())
                ).orderByDesc("dim_day").orderByDesc("dim_label_id")
        );
        if (iPage.getRecords().isEmpty()) {
            return new PageUtils<>(iPage);
        }
        total.setLabelId(0L);
        total.setLabelName(ConstantUtils.PLACEHOLDER);
        total.setReportDate(ConstantUtils.PLACEHOLDER);
        AssetLabelSelectGetVO assetLabelSelectGetVO = new AssetLabelSelectGetVO();
        assetLabelSelectGetVO.setIds(iPage.getRecords().stream().map(AssetLabelListDTO::getLabelId).collect(Collectors.toList()));
        Map<Long, String> labelMap = fgMarketService.assetLabelSelect(assetLabelSelectGetVO)
                .getData().stream().collect(Collectors.toMap(SelectDTO::getId, SelectDTO::getName));
        iPage.getRecords().forEach(lab -> {
            lab.setLabelName(labelMap.getOrDefault(lab.getLabelId(), "未知"));
            if (ObjectUtils.isNotNullOrZero(lab.getDay())) {
                lab.setReportDate(DateUtils.long2String(lab.getDay()));
            }
        });
        IPage<AssetLabelListDTO> finalIPage = iPage;
        return new PageUtils<>(new ArrayList<>() {
            {
                add(total);
                addAll(finalIPage.getRecords());
            }
        }, iPage.getTotal());
    }

    @Override
    public void exportAssetLabelList(HttpServletResponse response, AssetLabelListVO listVO, User user) throws IOException {
        listVO.setPage(1L);
        listVO.setPageNum(10000L);
        PageUtils<?> list = this.assetLabelList(listVO, user);
        ExcelUtils.download(response, "素材标签报表", AssetLabelListDTO.class, list.getData(), new ArrayList<>());
    }

    /**
     * 根据项目获取账户
     *
     * @param projectId 项目ID
     * @param userId    用户ID
     * @return 返回数据
     */
    private List<Long> getMasterIdsByProject(Long projectId, Long userId) {
        UserMasterGetVO userMasterGetVO = new UserMasterGetVO();
        userMasterGetVO.setProjectId(projectId);
        userMasterGetVO.setUserId(userId);
        return fgSystemService.getAllMasterIdsByProjectId(userMasterGetVO).getData();
    }

    /**
     * 获取指标名称
     *
     * @param projectId 项目ID
     * @return 返回数据
     */
    private String customSelect(Long projectId) {
        CustomIndexGetVO customIndexGetVO = CustomIndexGetVO.builder()
                .module("asset_label_report")
                .build();
        if (ObjectUtils.isNotNullOrZero(projectId)) {
            customIndexGetVO.setProjectId(projectId);
        } else {
            customIndexGetVO.setIdentify("");
        }
        return fgSystemService.getUserCustomIndexReportSelect(customIndexGetVO).getData();
    }

    /**
     * @return 获取标签ID
     */
    private List<Long> labelIds() {
        AssetLabelSelectGetVO assetLabelSelectGetVO = new AssetLabelSelectGetVO();
        return fgMarketService.assetLabelSelect(assetLabelSelectGetVO)
                .getData().stream().map(SelectDTO::getId).collect(Collectors.toList());
    }
}
