package com.overseas.service.report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.overseas.common.dto.SelectDTO;
import com.overseas.common.dto.SelectDTO2;
import com.overseas.common.dto.chart.MultiIndexChartDTO;
import com.overseas.common.dto.report.*;
import com.overseas.common.dto.report.adx.AdxDailyCostListDTO;
import com.overseas.common.dto.report.revenue.dsp.RevenueDspReportDTO;
import com.overseas.common.dto.sys.project.ProjectByMasterDTO;
import com.overseas.common.entity.User;
import com.overseas.common.utils.PageUtils;
import com.overseas.common.vo.market.cps.recycle.CpsRecycleListVO;
import com.overseas.common.vo.report.*;
import com.overseas.common.vo.report.plan.PlanHourReportListVO;
import com.overseas.common.vo.report.revenue.RevenueRtaReportListVO;
import com.overseas.service.report.entity.CreativeUnitDay;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ReportService extends IService<CreativeUnitDay> {

    /**
     * 获取趋势图数据
     *
     * @param listVO 传入参数
     * @return 返回参数
     */
    MultiIndexChartDTO getTrendChart(ReportListVO listVO, User user);

    /**
     * 获取报表列表数据
     *
     * @param listVO 传入参数
     * @return 返回参数
     */
    PageUtils<?> getReportPageList(ReportListVO listVO, User user);


    /**
     * 获取报表列表数据
     *
     * @param listVO 传入参数
     * @return 返回参数
     */
    ProjectByMasterDTO getReportPageListIdentify(ReportListVO listVO, User user);

    /**
     * 通过ID获取报表列表数据（不查询投放数据、汇总数据）
     *
     * @param listVO           传入参数
     * @param isValidSearchIds 是否使用searchIds进行查询
     * @param searchIds        ID集合
     * @return 返回参数
     */
    IPage<?> getReportList(ReportListVO listVO, Boolean isValidSearchIds, List<Long> searchIds, User user);

    /**
     * 获取报表汇总数据
     *
     * @param listVO           传入参数
     * @param isValidSearchIds 是否使用searchIds进行查询
     * @param searchIds        ID集合
     * @return 返回参数
     */
    CreativeHourReportListDTO getSummaryData(ReportListVO listVO, Boolean isValidSearchIds, List<Long> searchIds, User user);

    /**
     * 导出报表数据
     *
     * @param listVO 传入参数
     */
    void download(ReportListVO listVO, User user, HttpServletResponse response);

    void download(ReportListVO listVO, String path, User user);

    /**
     * 获取有消耗的对象信息
     *
     * @param getVO 查询参数
     * @return id集合
     */
    List<Long> getHasCostIds(ReportHasCostGetVO getVO);

    /**
     * 获取计划小时报表数据
     *
     * @param listVO 传入参数
     * @return 返回数据
     */
    List<ReportListDTO> getPlanHourReportList(PlanHourReportListVO listVO);

    /**
     * 检查一组记录是否存在消耗
     *
     * @param hasCostListVO 查询对象
     * @return 结果集
     */
    List<Long> listHasCostRecord(ReportHasCostListVO hasCostListVO);

    /**
     * 获取用户消耗
     *
     * @param getVO 传入参数
     * @return 返回数据
     */
    UserCostDTO getUserCost(UserCostGetVO getVO);

    /**
     * 根据帐号类型，获取所有账号指定时段的总消耗
     *
     * @param listVO 请求参数
     * @return 返回数据
     */
    Map<Long, UserCostDTO> listUserCost(UserCostListVO listVO);

    /**
     * 获取广告主下计划概览数据
     *
     * @param getVO 获取数据
     * @return 返回数据
     */
    OverviewReportDTO getOverviewReport(OverviewReportGetVO getVO, User user);

    /**
     * 获取广告主下计划概览趋势图数据
     *
     * @param getVO 传入参数
     * @param user  用户信息
     * @return 返回参数
     */
    MultiIndexChartDTO getOverviewTrendChart(OverviewChartGetVO getVO, User user);

    /**
     * 获取广告主下计划概览趋势图数据
     *
     * @param getVO 传入参数
     * @return 返回参数
     */
    MultiIndexChartDTO getTrendChart(TrendChartGetVO getVO);

    /**
     * 保存指定日期的ADX消耗对比数据至临时表
     *
     * @param reportDate 日期
     */
//    void saveAdxDailyCostData(Date reportDate) throws InterruptedException;

    /**
     * 保存指定 日期的 topOnAdx数据
     *
     * @param reportDate 日期
     */
//    void saveAdxTopOnDailyCostData(Date reportDate);

    /**
     * 获取adx指定日期的消耗对比
     *
     * @param listVO 传入参数
     */
    PageUtils<AdxDailyCostListDTO> listAdxDailyCost(AdxDailyCostListVO listVO);

//    void exportAdxDailyCostList(AdxDailyCostListVO listVO, HttpServletResponse response);

    List<RevenueDspReportDTO> listDspReportData(RevenueRtaReportListVO listVO);

//    List<RevenueDspReportDTO> listDspRtaReportData(RevenueRtaReportListVO listVO);

    /**
     * 维度汇总
     *
     * @param dimensionTotalVO 维度参数
     * @return 返回数据
     */
    List<DimensionTotalDTO> dimensionTotal(DimensionTotalVO dimensionTotalVO);

    /**
     * 恢复数据获取
     *
     * @param resetVO 参数
     * @return 返回数据
     */
    BigDecimal dimensionReset(DimensionResetVO resetVO);


    /**
     * 获取筛选字段
     *
     * @param module   模块
     * @param identify 项目标识
     * @param orderBy  排序
     * @return 返回数据
     */
    String findReportFields(String module, String identify, String orderBy);

    /**
     * 获取报表header
     *
     * @param fieldKeys 字段key
     * @param module    module
     * @param identify  标识
     * @param clazz     基础类型
     * @return 返回数据
     */
    List<SelectDTO2> findReportHeader(List<String> fieldKeys, String module, String identify, Class<?> clazz);

    /**
     * 根据keys 获取title
     *
     * @param module   模块
     * @param identify 项目标识
     * @param keys     keys
     * @return 返回数据
     */
    List<String> findReportFieldsTitle(String module, String identify, List<String> keys);

    /**
     * 更新小时时间戳
     */
    void updateReportHour();

    void updateSheinT1Settlement();

    void updateSheinRealTimeSettlement();

    /**
     * 根据 cps roi 条件获取花费数据
     *
     * @param listVO 列表
     * @return 返回数据
     */
    Map<String, BigDecimal> recycleCost(CpsRecycleListVO listVO);

    /**
     * 根据花费排序下啦
     *
     * @param sortByCostVO 条件
     * @return 返回数据
     */
    List<SelectDTO> selectSortByCost(SelectSortByCostVO sortByCostVO);
}
