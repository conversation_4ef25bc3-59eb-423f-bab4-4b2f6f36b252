package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.CreativeHourReportListDTO;
import com.overseas.common.dto.report.ReportListDTO;
import com.overseas.common.dto.report.channel.ChannelReportListDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.CreativeUnitHour;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SheinCreativeUnitHourMapper extends BaseMapper<CreativeUnitHour> {

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_shein_creative_unit_hour ${ew.customSqlSegment}")
    List<CreativeHourReportListDTO> getReportList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_shein_creative_unit_hour ${ew.customSqlSegment}")
    IPage<CreativeHourReportListDTO> getReportPageList(IPage<CreativeHourReportListDTO> page,
                                                       @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_shein_creative_unit_hour ${ew.customSqlSegment}")
    CreativeHourReportListDTO getTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 获取某个小时有花费的计划数据集合
     *
     * @param wrapper 查询参数
     * @return 数据结果
     */
    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_shein_creative_unit_hour ${ew.customSqlSegment}")
    List<ReportListDTO> getPlanHourReportList(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Update("UPDATE t_ads_dsp_flow_shein_creative_unit_hour " +
            "SET idx_action40 = idx_settlement " +
            "${ew.customSqlSegment}")
    void updateT1Settlement(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 特殊处理shein的报表字段，满足客户要求同一个字段表示当天实时数据和T+1数据需求
     * @param wrapper 查询条件
     */
    @Update("UPDATE t_ads_dsp_flow_shein_creative_unit_hour " +
            "SET idx_action40 = idx_action35,idx_action39 = idx_action33 " +
            "${ew.customSqlSegment}")
    void updateRealTimeSettlement(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    /**
     * 特殊处理shein的报表字段，满足客户要求同一个字段表示当天实时数据和T+1数据需求
     * @param wrapper 查询条件
     */
    @Update("UPDATE t_ads_dsp_flow_shein_creative_unit_hour " +
            "SET idx_action39 = idx_action12 " +
            "${ew.customSqlSegment}")
    void updateT1Purchase(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);


    /**
     * 渠道报表list
     *
     * @param iPage     分页参数
     * @param wrapper   条件
     * @return 返回水
     */
    @Select("SELECT  " +
            "${ew3.sqlSelect} " +
            "FROM " +
            "( select ${ew.sqlSelect} from t_ads_dsp_flow_shein_creative_unit_hour ${ew.customSqlSegment} ) cuh " +
            "LEFT JOIN " +
            "( select ${ew2.sqlSelect} from td_channel_report ${ew2.customSqlSegment} ) tcr ON ${onSql}" +
            "${ew3.customSqlSegment}")
    IPage<ChannelReportListDTO> channelReportList(IPage<?> iPage,
                                                  @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                                  @Param("ew2") Wrapper<?> wrapper2,
                                                  @Param("ew3") Wrapper<?> wrapper3,
                                                  @Param("onSql") String onSql);

    /**
     * 总数
     *
     * @param wrapper   条件
     * @param selectSql 数据
     * @return 返回数据
     */
    @Select("SELECT  " +
            "${selectSql} " +
            "FROM " +
            "( select ${ew.sqlSelect} from t_ads_dsp_flow_shein_creative_unit_hour ${ew.customSqlSegment} ) cuh " +
            "LEFT JOIN " +
            "( select ${ew2.sqlSelect} from td_channel_report ${ew2.customSqlSegment} ) tcr " +
            "ON ${onSql}")
    ChannelReportListDTO channelReportTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper,
                                            @Param("ew2") Wrapper<?> wrapper2,
                                            @Param("selectSql") String selectSql,
                                            @Param("onSql") String onSql);
}
