package com.overseas.service.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.overseas.common.dto.report.CreativeHourReportListDTO;
import com.overseas.common.dto.report.revenue.RevenueForUpdateDTO;
import com.overseas.common.dto.report.revenue.dsp.RevenueDspReportDTO;
import com.overseas.common.utils.ConstantUtils;
import com.overseas.service.report.entity.PlanRtaHour;
import com.overseas.service.report.entity.RevenueDay;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PlanRtaHourMapper extends BaseMapper<PlanRtaHour> {

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_hour ${ew.customSqlSegment}")
    IPage<CreativeHourReportListDTO> listRtaReportPage(IPage<CreativeHourReportListDTO> page,
                                                       @Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_hour ${ew.customSqlSegment}")
    List<CreativeHourReportListDTO> listRtaReport(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} FROM t_ads_dsp_flow_plan_rta_hour ${ew.customSqlSegment}")
    CreativeHourReportListDTO getRtaReportTotal(@Param(ConstantUtils.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT ${ew.sqlSelect} " +
            "FROM t_ads_dsp_flow_plan_rta_hour " +
            "${ew.customSqlSegment}")
    List<RevenueDspReportDTO> listPlanReport(@Param(ConstantUtils.WRAPPER) Wrapper<PlanRtaHour> wrapper);

//    @Select("SELECT ${ew.sqlSelect} " +
//            "FROM t_ads_dsp_flow_plan_rta_hour AS report " +
//            "LEFT JOIN `iflytek_overseas_dsp`.`d_rta_strategy` AS rta ON report.dim_rta_id = rta.id " +
//            "LEFT JOIN `iflytek_overseas_dsp`.`d_country_all` AS country " +
//            "ON rta.rta_country = country.country_alias AND country.country_alias <> '' " +
//            "${ew.customSqlSegment}")
//    List<RevenueDspReportDTO> listPlanReportByCountry(@Param(ConstantUtils.WRAPPER) Wrapper<PlanRtaHour> wrapper);

    @Select("SELECT ${ew.sqlSelect} " +
            "FROM t_ads_dsp_flow_plan_ep_rta_hour AS report " +
            "LEFT JOIN `iflytek_overseas_dsp`.`d_rta_strategy` AS rta ON report.dim_rta_id = rta.id " +
            "LEFT JOIN `iflytek_overseas_dsp`.`d_country_all` AS country " +
            "ON rta.rta_country = country.country_alias AND country.country_alias <> '' " +
            "LEFT JOIN `iflytek_overseas_dsp`.`d_ep` AS d_ep ON report.dim_ep_id = d_ep.id  " +
            "${ew.customSqlSegment}")
    List<RevenueDay> revenueDayByWrapper(@Param(ConstantUtils.WRAPPER) Wrapper<PlanRtaHour> wrapper);

    /**
     * 获取数据
     *
     * @param beforeDay 昨日
     * @param afterDay  今日二个小时内
     * @return 返回数据
     */
    @Select("SELECT report.`dim_master_id` as `master_id`, FROM_UNIXTIME(report.`dim_day`,'%Y-%m-%d') AS `day`," +
            " mm.time_zone as `timezone`" +
            " FROM t_ads_dsp_flow_plan_rta_hour AS report" +
            " LEFT JOIN `iflytek_overseas_dsp`.`m_master` as mm ON report.dim_master_id = mm.user_id " +
            " WHERE report.create_time < #{before} AND report.update_time > #{after}" +
            " group by `master_id`, `day` , `timezone`")
    List<RevenueForUpdateDTO> revenueDayForUpdate(@Param("before") String beforeDay, @Param("after") String afterDay);
}
