package com.overseas.service.report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.overseas.common.dto.report.openApi.ae.AeDailyCostListDTO;
import com.overseas.common.dto.report.openApi.ae.DailyCostListDTO;
import com.overseas.common.enums.TimeZoneEnum;
import com.overseas.common.exception.CustomException;
import com.overseas.common.utils.DateUtils;
import com.overseas.common.vo.report.openApi.DailyCostListVO;
import com.overseas.service.report.entity.AeDailyCost;
import com.overseas.service.report.mapper.AeDailyCostMapper;
import com.overseas.service.report.mapper.PlanHourMapper;
import com.overseas.service.report.service.AeDailyCostService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class AeDailyCostServiceImpl extends ServiceImpl<AeDailyCostMapper, AeDailyCost> implements AeDailyCostService {

    private final PlanHourMapper planHourMapper;

    @Override
    public List<AeDailyCostListDTO> listAeCampaignDailyCost(DailyCostListVO getVO) {
        List<AeDailyCostListDTO> dataList = this.listAeDailyCost(getVO.getBizdate(), getVO.getCampaignIds());
        if (dataList.isEmpty()) {
            dataList = this.listAeDailyCostFromReport(getVO);
        }
        return dataList;
    }

    private List<AeDailyCostListDTO> listAeDailyCostFromReport(DailyCostListVO getVO) {
        List<AeDailyCostListDTO> dataList = new ArrayList<>();
        // 计算UTC-08:00时区的起始时间
        Long startHour = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(
                DateUtils.string2Date(getVO.getBizdate()), TimeZoneEnum.UTC_17.getId(), 0));
        Long endHour = DateUtils.date2Long(TimeZoneEnum.getUTC_8Date(
                DateUtils.string2Date(getVO.getBizdate()), TimeZoneEnum.UTC_17.getId(), 23));
        try {
            List<DailyCostListDTO> dailyCostList = this.planHourMapper.getAeDailyCost(new QueryWrapper<>()
                    .between("prh.dim_report_hour", startHour, endHour)
                    .in("drs.campaign_id", getVO.getCampaignIds())
                    .groupBy("prh.dim_rta_id"));
            if (!dailyCostList.isEmpty()) {
                dailyCostList.forEach(dailyCost -> {
                    // 系数不为0时入库数据
//                    if (!dailyCost.getCostRate().equals(0D)) {
                    // 直接读取计算后的客户花费字段，不再进行系数计算
//                        dailyCost.setCost(DoubleUtils.mul(dailyCost.getCost(), dailyCost.getCostRate()));
                        AeDailyCost aeDailyCost = new AeDailyCost();
                        aeDailyCost.setReportDate(DateUtils.string2Long(getVO.getBizdate()));
                        BeanUtils.copyProperties(dailyCost, aeDailyCost);
                        this.baseMapper.insert(aeDailyCost);
                        AeDailyCostListDTO aeDailyCostDTO = new AeDailyCostListDTO();
                        BeanUtils.copyProperties(dailyCost, aeDailyCostDTO);
                        dataList.add(aeDailyCostDTO);
//                    }
                });
            }
        } catch (Exception e) {
            throw new CustomException(3001, "内部处理异常");
        }
        return dataList;
    }

    private List<AeDailyCostListDTO> listAeDailyCost(String date, List<String> campaignIds) {
        return this.baseMapper.listDailyCost(new QueryWrapper<AeDailyCost>().lambda()
                .eq(AeDailyCost::getReportDate, DateUtils.string2Long(date))
                .in(AeDailyCost::getCampaignId, campaignIds)
        );
    }
}
